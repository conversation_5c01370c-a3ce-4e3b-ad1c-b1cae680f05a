#ifndef __APP_TURN_H
#define __APP_TURN_H

#include "HeadFiles.h"

// 舵机转弯状态枚举
typedef enum {
    TURN_STATE_IDLE = 0,           // 空闲状态，正常循迹
    TURN_STATE_WHITE_DETECTED,     // 检测到全白，准备转弯
    TURN_STATE_TURNING,            // 正在转弯
    TURN_STATE_SEARCHING,          // 转弯后寻找黑线
    TURN_STATE_COMPLETED           // 转弯完成，恢复循迹
} TurnState_t;

// 舵机转弯控制函数
uint8_t Servo_Turn_WhiteDetection(void);                                    // 主转弯控制函数
void Servo_Turn_SetParams(float angle, uint32_t duration, float speed, uint8_t direction);  // 设置转弯参数
TurnState_t Servo_Turn_GetState(void);                                      // 获取转弯状态
void Servo_Turn_Reset(void);                                                // 重置转弯状态

#endif
