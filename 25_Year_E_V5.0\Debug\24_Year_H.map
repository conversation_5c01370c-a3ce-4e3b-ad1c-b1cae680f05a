******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  9 20:33:35 2025

OUTPUT FILE NAME:   <24_Year_H.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004bf9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000061c0  00019e40  R  X
  SRAM                  20200000   00008000  00000672  0000798e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000061c0   000061c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005660   00005660    r-x .text
  00005720    00005720    00000a10   00000a10    r-- .rodata
  00006130    00006130    00000090   00000090    r-- .cinit
20200000    20200000    00000475   00000000    rw-
  20200000    20200000    00000295   00000000    rw- .bss
  20200298    20200298    000001dd   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005660     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000138a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000138c    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001514    00000184     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001698    00000174     pid.o (.text.PID_tracing_realize)
                  0000180c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001948    0000012c     pid.o (.text.Tracing_Value_Get)
                  00001a74    00000128     pid.o (.text.PID_speed_realize)
                  00001b9c    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001cc0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001de0    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001ef8    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00002008    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002114    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002218    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  0000230e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002310    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  000023fc    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000024e4    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  000025cc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000026b0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000278c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002864    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000293c    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00002a0c    000000c4     motor.o (.text.motor_direction)
                  00002ad0    000000bc     key.o (.text.Key_Proc)
                  00002b8c    000000ac     motor.o (.text.pwm_set)
                  00002c38    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002ce2    00000002     libc.a : _lock.c.obj (.text._nop)
                  00002ce4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002d86    00000002     --HOLE-- [fill = 0]
                  00002d88    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002e28    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002ec4    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002f5c    00000090     pid.o (.text.PID_init)
                  00002fec    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003078    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003104    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003188    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000320c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000328e    00000002     --HOLE-- [fill = 0]
                  00003290    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000330c    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  00003384    00000074     Scheduler.o (.text.Scheduler_Run)
                  000033f8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000346c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003470    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000034e4    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003556    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000035c6    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003632    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000369c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00003704    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000376c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000037d2    00000002     --HOLE-- [fill = 0]
                  000037d4    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00003838    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000389a    00000002     --HOLE-- [fill = 0]
                  0000389c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000038fe    00000002     --HOLE-- [fill = 0]
                  00003900    00000060     key.o (.text.Key_Read)
                  00003960    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000039be    00000002     --HOLE-- [fill = 0]
                  000039c0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003a1c    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003a78    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003ad4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003b2c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003b84    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003bdc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003c32    00000002     --HOLE-- [fill = 0]
                  00003c34    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003c88    00000054     encoder.o (.text.Encoder_Get)
                  00003cdc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003d30    00000054     interrupt.o (.text.TIMG0_IRQHandler)
                  00003d84    00000054     main.o (.text.main)
                  00003dd8    00000054     usart_app.o (.text.uart_task)
                  00003e2c    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003e7e    00000002     --HOLE-- [fill = 0]
                  00003e80    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003ed0    00000050     clock.o (.text.SysTick_Config)
                  00003f20    00000050     wit.o (.text.WIT_Init)
                  00003f70    00000050     usart_app.o (.text.fputs)
                  00003fc0    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000400c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004058    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000040a4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000040ee    00000002     --HOLE-- [fill = 0]
                  000040f0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000413a    0000004a     adc_app.o (.text.adc_getValue)
                  00004184    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000041cc    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004214    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  0000425c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000042a0    00000044     usart_app.o (.text.UART0_IRQHandler)
                  000042e4    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00004326    00000002     --HOLE-- [fill = 0]
                  00004328    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004368    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000043a8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000043e8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004428    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004464    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000044a0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000044dc    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00004518    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  00004554    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004590    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000045cc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004608    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004644    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000467e    00000002     --HOLE-- [fill = 0]
                  00004680    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000046ba    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000046f2    00000002     --HOLE-- [fill = 0]
                  000046f4    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000472c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004764    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  0000479c    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000047d0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004804    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00004838    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00004868    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00004898    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  000048c8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000048f8    00000030     clock.o (.text.mspm0_delay_ms)
                  00004928    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00004958    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004984    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  000049b0    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  000049dc    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004a08    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  00004a34    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004a60    0000002c     usart_app.o (.text.fputc)
                  00004a8c    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004ab8    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004ae0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004b08    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004b30    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  00004b58    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00004b80    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004ba8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004bd0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004bf8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004c20    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004c46    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004c6c    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00004c92    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004cb8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004cde    00000002     --HOLE-- [fill = 0]
                  00004ce0    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004d04    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004d28    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004d4c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004d70    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004d92    00000002     --HOLE-- [fill = 0]
                  00004d94    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004db4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004dd4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004df2    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004e10    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004e2e    00000002     --HOLE-- [fill = 0]
                  00004e30    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004e4c    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004e68    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004e84    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004ea0    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004ebc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004ed8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004ef4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004f10    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004f2c    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00004f48    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004f64    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004f80    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004f9c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004fb8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004fd4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004ff0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005008    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005020    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005038    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005050    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005068    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005080    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005098    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  000050b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000050c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000050e0    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  000050f8    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005110    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005128    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005140    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005158    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005170    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005188    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000051a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000051b8    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  000051d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000051e8    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005200    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00005218    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005230    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005248    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005260    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005278    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005290    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000052a8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000052c0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000052d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000052f0    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00005308    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005320    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005338    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005350    00000018     clock.o (.text.SysTick_Init)
                  00005368    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00005380    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00005396    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  000053ac    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000053c2    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000053d8    00000016     key.o (.text.DL_GPIO_readPins)
                  000053ee    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00005404    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000541a    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00005430    00000016     encoder.o (.text.Encoder_Init)
                  00005446    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000545c    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00005470    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00005484    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00005498    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000054ac    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  000054c0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000054d4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000054e8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000054fc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005510    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005524    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005538    00000014     app_question_task.o (.text.State_Machine_init)
                  0000554c    00000014     wit.o (.text.WIT_Calibrate_Yaw)
                  00005560    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005574    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005588    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  0000559a    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  000055ac    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000055be    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000055d0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000055e2    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000055f2    00000002     --HOLE-- [fill = 0]
                  000055f4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005604    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005614    00000010     interrupt.o (.text.SysTick_Handler)
                  00005624    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005634    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005644    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  00005652    00000002     --HOLE-- [fill = 0]
                  00005654    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005662    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005670    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000567e    00000002     --HOLE-- [fill = 0]
                  00005680    0000000c     Scheduler.o (.text.Scheduler_Init)
                  0000568c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005698    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000056a2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000056ac    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000056bc    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000056c6    0000000a            : sprintf.c.obj (.text._outc)
                  000056d0    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000056d8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000056e0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000056e8    00000008     libc.a : printf.c.obj (.text._outc)
                  000056f0    00000008            : printf.c.obj (.text._outs)
                  000056f8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000056fc    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000570c    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005710    00000004            : exit.c.obj (.text:abort)
                  00005714    0000000c     --HOLE-- [fill = 0]

.cinit     0    00006130    00000090     
                  00006130    0000006b     (.cinit..data.load) [load image, compression = lzss]
                  0000619b    00000001     --HOLE-- [fill = 0]
                  0000619c    0000000c     (__TI_handler_table)
                  000061a8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000061b0    00000010     (__TI_cinit_table)

.rodata    0    00005720    00000a10     
                  00005720    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005d10    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00005f38    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005f40    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006041    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006044    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000606c    00000020     pid.o (.rodata.str1.142847404293518999571)
                  0000608c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  000060a4    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  000060b8    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000060c9    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000060da    00000010     encoder.o (.rodata.encoder_table)
                  000060ea    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  000060f9    00000001     --HOLE-- [fill = 0]
                  000060fa    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006104    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  0000610e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006110    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006118    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  0000611d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006120    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006122    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006124    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000295     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    0000002c     (.common:wit_data)
                  2020017c    00000028     (.common:angle_pid)
                  202001a4    00000028     (.common:speedA_pid)
                  202001cc    00000028     (.common:speedB_pid)
                  202001f4    00000028     (.common:tracing_pid)
                  2020021c    00000021     (.common:wit_dmaBuffer)
                  2020023d    00000014     (.common:OLED_String)
                  20200251    00000001     (.common:Digtal)
                  20200252    00000001     (.common:Key_Down)
                  20200253    00000001     (.common:Key_Old)
                  20200254    00000014     (.common:State_Machine)
                  20200268    00000010     (.common:Normal)
                  20200278    00000008     (.common:grayscale_data)
                  20200280    00000004     (.common:start_time)
                  20200284    00000004     (.common:tick_ms)
                  20200288    00000004     (.common:tracing_val)
                  2020028c    00000004     (.common:uart_rx_ticks)
                  20200290    00000001     (.common:Key_Up)
                  20200291    00000001     (.common:Key_Val)
                  20200292    00000001     (.common:grayscale_count)
                  20200293    00000001     (.common:task_num)
                  20200294    00000001     (.common:uart_rx_index)

.data      0    20200298    000001dd     UNINITIALIZED
                  20200298    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200388    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200408    00000024     Scheduler.o (.data.scheduler_task)
                  2020042c    00000010     Ganv_Grayscale.o (.data.Anolog)
                  2020043c    00000010     Ganv_Grayscale.o (.data.black)
                  2020044c    00000010     Ganv_Grayscale.o (.data.white)
                  2020045c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200460    00000004            : _lock.c.obj (.data._lock)
                  20200464    00000004            : _lock.c.obj (.data._unlock)
                  20200468    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  2020046c    00000002     encoder.o (.data.encoder_A_count)
                  2020046e    00000002     encoder.o (.data.encoder_B_count)
                  20200470    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200471    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200472    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200473    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200474    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2988    132       160    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3168    324       160    
                                                               
    .\APP\
       app_tracing_check.o            236     0         7      
       app_question_task.o            20      0         20     
    +--+------------------------------+-------+---------+---------+
       Total:                         256     0         27     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\Encoder\
       encoder.o                      560     16        6      
    +--+------------------------------+-------+---------+---------+
       Total:                         560     16        6      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          306     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         306     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1238    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1566    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2306    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2306    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          1112    32        164    
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    32        164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          430     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         430     0         77     
                                                               
    D:/ti_ccstheia/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti_ccstheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti_ccstheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       143       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22064   2898      1650   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000061b0 records: 2, size/record: 8, table size: 16
	.data: load addr=00006130, load size=0000006b bytes, run addr=20200298, run size=000001dd bytes, compression=lzss
	.bss: load addr=000061a8, load size=00000008 bytes, run addr=20200000, run size=00000295 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000619c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011f9     000056ac     000056aa   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004bf9     000056fc     000056f8   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000346d  ADC0_IRQHandler                      
0000346d  ADC1_IRQHandler                      
0000346d  AES_IRQHandler                       
2020042c  Anolog                               
00005710  C$$EXIT                              
0000346d  CANFD0_IRQHandler                    
0000346d  DAC0_IRQHandler                      
00004329  DL_ADC12_setClockConfig              
00005699  DL_Common_delayCycles                
00003fc1  DL_DMA_initChannel                   
00003961  DL_I2C_fillControllerTXFIFO          
00004cb9  DL_I2C_setClockConfig                
000026b1  DL_SYSCTL_configSYSPLL               
0000425d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002115  DL_Timer_initFourCCPWMMode           
000023fd  DL_Timer_initTimerMode               
00004f9d  DL_Timer_setCaptCompUpdateMethod     
00005291  DL_Timer_setCaptureCompareOutCtl     
00005605  DL_Timer_setCaptureCompareValue      
00004fb9  DL_Timer_setClockConfig              
00003c35  DL_UART_drainRXFIFO                  
00004185  DL_UART_init                         
000055ad  DL_UART_setClockConfig               
0000346d  DMA_IRQHandler                       
0000346d  Default_Handler                      
20200251  Digtal                               
00003c89  Encoder_Get                          
00005431  Encoder_Init                         
0000346d  GROUP0_IRQHandler                    
00001b9d  GROUP1_IRQHandler                    
0000293d  Get_Analog_value                     
000044dd  Get_Anolog_Value                     
00005645  Get_Digtal_For_User                  
000046bb  Get_Normalize_For_User               
0000346d  HardFault_Handler                    
0000346d  I2C0_IRQHandler                      
0000346d  I2C1_IRQHandler                      
20200252  Key_Down                             
20200253  Key_Old                              
00002ad1  Key_Proc                             
00003901  Key_Read                             
20200290  Key_Up                               
20200291  Key_Val                              
0000346d  NMI_Handler                          
0000138d  No_MCU_Ganv_Sensor_Init              
000034e5  No_MCU_Ganv_Sensor_Init_Frist        
000042e5  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200268  Normal                               
00003633  OLED_Clear                           
00001ef9  OLED_Init                            
00004519  OLED_Set_Pos                         
00001de1  OLED_ShowChar                        
00002219  OLED_ShowNum                         
00003557  OLED_ShowString                      
2020023d  OLED_String                          
00002ec5  OLED_WR_Byte                         
000024e5  Oled_Task                            
00002f5d  PID_init                             
00001a75  PID_speed_realize                    
00001699  PID_tracing_realize                  
0000346d  PendSV_Handler                       
0000346d  RTC_IRQHandler                       
000056f9  Reset_Handler                        
0000346d  SPI0_IRQHandler                      
0000346d  SPI1_IRQHandler                      
0000346d  SVC_Handler                          
00004059  SYSCFG_DL_ADC1_init                  
00005339  SYSCFG_DL_DMA_WIT_init               
000056d1  SYSCFG_DL_DMA_init                   
00004805  SYSCFG_DL_FOR_CONTROL_init           
00001515  SYSCFG_DL_GPIO_init                  
00003ad5  SYSCFG_DL_I2C_OLED_init              
00002fed  SYSCFG_DL_PWM_MOTOR_init             
00004555  SYSCFG_DL_SYSCTL_init                
00003cdd  SYSCFG_DL_UART_0_init                
0000369d  SYSCFG_DL_UART_WIT_init              
000046f5  SYSCFG_DL_init                       
00002e29  SYSCFG_DL_initPower                  
00005681  Scheduler_Init                       
00003385  Scheduler_Run                        
20200254  State_Machine                        
00005539  State_Machine_init                   
00005615  SysTick_Handler                      
00005351  SysTick_Init                         
0000346d  TIMA0_IRQHandler                     
0000346d  TIMA1_IRQHandler                     
00003d31  TIMG0_IRQHandler                     
0000346d  TIMG12_IRQHandler                    
0000346d  TIMG6_IRQHandler                     
0000346d  TIMG7_IRQHandler                     
0000346d  TIMG8_IRQHandler                     
000055bf  TI_memcpy_small                      
00005671  TI_memset_small                      
00001949  Tracing_Value_Get                    
000042a1  UART0_IRQHandler                     
0000346d  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
0000346d  UART3_IRQHandler                     
0000554d  WIT_Calibrate_Yaw                    
0000330d  WIT_Get_Relative_Yaw                 
00003f21  WIT_Init                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000061b0  __TI_CINIT_Base                      
000061c0  __TI_CINIT_Limit                     
000061c0  __TI_CINIT_Warm                      
0000619c  __TI_Handler_Table_Base              
000061a8  __TI_Handler_Table_Limit             
00004609  __TI_auto_init_nobinit_nopinit       
00003291  __TI_decompress_lzss                 
000055d1  __TI_decompress_none                 
00003b2d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005447  __TI_zero_init_nomemset              
00001203  __adddf3                             
0000286f  __addsf3                             
00005f40  __aeabi_ctype_table_                 
00005f40  __aeabi_ctype_table_C                
00003471  __aeabi_d2f                          
000040f1  __aeabi_d2iz                         
00001203  __aeabi_dadd                         
00003839  __aeabi_dcmpeq                       
00003875  __aeabi_dcmpge                       
00003889  __aeabi_dcmpgt                       
00003861  __aeabi_dcmple                       
0000384d  __aeabi_dcmplt                       
00002009  __aeabi_ddiv                         
000025cd  __aeabi_dmul                         
000011f9  __aeabi_dsub                         
2020045c  __aeabi_errno                        
000056d9  __aeabi_errno_addr                   
000043a9  __aeabi_f2d                          
0000472d  __aeabi_f2iz                         
0000286f  __aeabi_fadd                         
0000389d  __aeabi_fcmpeq                       
000038d9  __aeabi_fcmpge                       
000038ed  __aeabi_fcmpgt                       
000038c5  __aeabi_fcmple                       
000038b1  __aeabi_fcmplt                       
0000320d  __aeabi_fdiv                         
00003079  __aeabi_fmul                         
00002865  __aeabi_fsub                         
00004a35  __aeabi_i2d                          
00004591  __aeabi_i2f                          
00003bdd  __aeabi_idiv                         
0000138b  __aeabi_idiv0                        
00003bdd  __aeabi_idivmod                      
0000230f  __aeabi_ldiv0                        
00004e11  __aeabi_llsl                         
00004d4d  __aeabi_lmul                         
0000568d  __aeabi_memclr                       
0000568d  __aeabi_memclr4                      
0000568d  __aeabi_memclr8                      
000056e1  __aeabi_memcpy                       
000056e1  __aeabi_memcpy4                      
000056e1  __aeabi_memcpy8                      
00005655  __aeabi_memset                       
00005655  __aeabi_memset4                      
00005655  __aeabi_memset8                      
00004d29  __aeabi_ui2d                         
00004369  __aeabi_uidiv                        
00004369  __aeabi_uidivmod                     
00005561  __aeabi_uldivmod                     
00004e11  __ashldi3                            
ffffffff  __binit__                            
00003705  __cmpdf2                             
00004645  __cmpsf2                             
00002009  __divdf3                             
0000320d  __divsf3                             
00003705  __eqdf2                              
00004645  __eqsf2                              
000043a9  __extendsfdf2                        
000040f1  __fixdfsi                            
0000472d  __fixsfsi                            
00004a35  __floatsidf                          
00004591  __floatsisf                          
00004d29  __floatunsidf                        
000033f9  __gedf2                              
000045cd  __gesf2                              
000033f9  __gtdf2                              
000045cd  __gtsf2                              
00003705  __ledf2                              
00004645  __lesf2                              
00003705  __ltdf2                              
00004645  __ltsf2                              
UNDEFED   __mpu_init                           
000025cd  __muldf3                             
00004d4d  __muldi3                             
00004681  __muldsi3                            
00003079  __mulsf3                             
00003705  __nedf2                              
00004645  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011f9  __subdf3                             
00002865  __subsf3                             
00003471  __truncdfsf2                         
00002ce5  __udivmoddi4                         
00004bf9  _c_int00_noargs                      
20200298  _ftable                              
20200460  _lock                                
00002ce3  _nop                                 
UNDEFED   _system_post_cinit                   
0000570d  _system_pre_init                     
20200464  _unlock                              
00005711  abort                                
0000413b  adc_getValue                         
2020017c  angle_pid                            
00005d10  asc2_0806                            
00005720  asc2_1608                            
000043e9  atoi                                 
ffffffff  binit                                
2020043c  black                                
000035c7  convertAnalogToDigital               
00005625  delay_ms                             
00002311  detect_trace_state_change            
2020046c  encoder_A_count                      
2020046e  encoder_B_count                      
00004a61  fputc                                
00003f71  fputs                                
000039c1  frexp                                
000039c1  frexpl                               
202000b0  gPWM_MOTORBackup                     
000041cd  gray_init                            
00002d89  gray_task                            
20200292  grayscale_count                      
20200278  grayscale_data                       
00000000  interruptVectors                     
0000278d  ldexp                                
0000278d  ldexpl                               
00003d85  main                                 
00004d71  memccpy                              
00002a0d  motor_direction                      
000048f9  mspm0_delay_ms                       
00004a8d  mspm0_get_clock_ms                   
00002c39  normalizeAnalogValues                
000037d5  oled_i2c_sda_unlock                  
00004929  oled_pow                             
00003a79  printf                               
00002b8d  pwm_set                              
0000278d  scalbn                               
0000278d  scalbnl                              
20200000  sensor                               
202001a4  speedA_pid                           
202001cc  speedB_pid                           
00004765  sprintf                              
20200280  start_time                           
20200293  task_num                             
20200284  tick_ms                              
202001f4  tracing_pid                          
20200288  tracing_val                          
20200388  uart_rx_buffer                       
20200294  uart_rx_index                        
2020028c  uart_rx_ticks                        
00003dd9  uart_task                            
00005635  wcslen                               
2020044c  white                                
20200150  wit_data                             
2020021c  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  __aeabi_dsub                         
000011f9  __subdf3                             
00001203  __adddf3                             
00001203  __aeabi_dadd                         
0000138b  __aeabi_idiv0                        
0000138d  No_MCU_Ganv_Sensor_Init              
00001515  SYSCFG_DL_GPIO_init                  
00001699  PID_tracing_realize                  
00001949  Tracing_Value_Get                    
00001a75  PID_speed_realize                    
00001b9d  GROUP1_IRQHandler                    
00001de1  OLED_ShowChar                        
00001ef9  OLED_Init                            
00002009  __aeabi_ddiv                         
00002009  __divdf3                             
00002115  DL_Timer_initFourCCPWMMode           
00002219  OLED_ShowNum                         
0000230f  __aeabi_ldiv0                        
00002311  detect_trace_state_change            
000023fd  DL_Timer_initTimerMode               
000024e5  Oled_Task                            
000025cd  __aeabi_dmul                         
000025cd  __muldf3                             
000026b1  DL_SYSCTL_configSYSPLL               
0000278d  ldexp                                
0000278d  ldexpl                               
0000278d  scalbn                               
0000278d  scalbnl                              
00002865  __aeabi_fsub                         
00002865  __subsf3                             
0000286f  __addsf3                             
0000286f  __aeabi_fadd                         
0000293d  Get_Analog_value                     
00002a0d  motor_direction                      
00002ad1  Key_Proc                             
00002b8d  pwm_set                              
00002c39  normalizeAnalogValues                
00002ce3  _nop                                 
00002ce5  __udivmoddi4                         
00002d89  gray_task                            
00002e29  SYSCFG_DL_initPower                  
00002ec5  OLED_WR_Byte                         
00002f5d  PID_init                             
00002fed  SYSCFG_DL_PWM_MOTOR_init             
00003079  __aeabi_fmul                         
00003079  __mulsf3                             
0000320d  __aeabi_fdiv                         
0000320d  __divsf3                             
00003291  __TI_decompress_lzss                 
0000330d  WIT_Get_Relative_Yaw                 
00003385  Scheduler_Run                        
000033f9  __gedf2                              
000033f9  __gtdf2                              
0000346d  ADC0_IRQHandler                      
0000346d  ADC1_IRQHandler                      
0000346d  AES_IRQHandler                       
0000346d  CANFD0_IRQHandler                    
0000346d  DAC0_IRQHandler                      
0000346d  DMA_IRQHandler                       
0000346d  Default_Handler                      
0000346d  GROUP0_IRQHandler                    
0000346d  HardFault_Handler                    
0000346d  I2C0_IRQHandler                      
0000346d  I2C1_IRQHandler                      
0000346d  NMI_Handler                          
0000346d  PendSV_Handler                       
0000346d  RTC_IRQHandler                       
0000346d  SPI0_IRQHandler                      
0000346d  SPI1_IRQHandler                      
0000346d  SVC_Handler                          
0000346d  TIMA0_IRQHandler                     
0000346d  TIMA1_IRQHandler                     
0000346d  TIMG12_IRQHandler                    
0000346d  TIMG6_IRQHandler                     
0000346d  TIMG7_IRQHandler                     
0000346d  TIMG8_IRQHandler                     
0000346d  UART1_IRQHandler                     
0000346d  UART3_IRQHandler                     
00003471  __aeabi_d2f                          
00003471  __truncdfsf2                         
000034e5  No_MCU_Ganv_Sensor_Init_Frist        
00003557  OLED_ShowString                      
000035c7  convertAnalogToDigital               
00003633  OLED_Clear                           
0000369d  SYSCFG_DL_UART_WIT_init              
00003705  __cmpdf2                             
00003705  __eqdf2                              
00003705  __ledf2                              
00003705  __ltdf2                              
00003705  __nedf2                              
000037d5  oled_i2c_sda_unlock                  
00003839  __aeabi_dcmpeq                       
0000384d  __aeabi_dcmplt                       
00003861  __aeabi_dcmple                       
00003875  __aeabi_dcmpge                       
00003889  __aeabi_dcmpgt                       
0000389d  __aeabi_fcmpeq                       
000038b1  __aeabi_fcmplt                       
000038c5  __aeabi_fcmple                       
000038d9  __aeabi_fcmpge                       
000038ed  __aeabi_fcmpgt                       
00003901  Key_Read                             
00003961  DL_I2C_fillControllerTXFIFO          
000039c1  frexp                                
000039c1  frexpl                               
00003a79  printf                               
00003ad5  SYSCFG_DL_I2C_OLED_init              
00003b2d  __TI_ltoa                            
00003bdd  __aeabi_idiv                         
00003bdd  __aeabi_idivmod                      
00003c35  DL_UART_drainRXFIFO                  
00003c89  Encoder_Get                          
00003cdd  SYSCFG_DL_UART_0_init                
00003d31  TIMG0_IRQHandler                     
00003d85  main                                 
00003dd9  uart_task                            
00003f21  WIT_Init                             
00003f71  fputs                                
00003fc1  DL_DMA_initChannel                   
00004059  SYSCFG_DL_ADC1_init                  
000040f1  __aeabi_d2iz                         
000040f1  __fixdfsi                            
0000413b  adc_getValue                         
00004185  DL_UART_init                         
000041cd  gray_init                            
0000425d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000042a1  UART0_IRQHandler                     
000042e5  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004329  DL_ADC12_setClockConfig              
00004369  __aeabi_uidiv                        
00004369  __aeabi_uidivmod                     
000043a9  __aeabi_f2d                          
000043a9  __extendsfdf2                        
000043e9  atoi                                 
000044dd  Get_Anolog_Value                     
00004519  OLED_Set_Pos                         
00004555  SYSCFG_DL_SYSCTL_init                
00004591  __aeabi_i2f                          
00004591  __floatsisf                          
000045cd  __gesf2                              
000045cd  __gtsf2                              
00004609  __TI_auto_init_nobinit_nopinit       
00004645  __cmpsf2                             
00004645  __eqsf2                              
00004645  __lesf2                              
00004645  __ltsf2                              
00004645  __nesf2                              
00004681  __muldsi3                            
000046bb  Get_Normalize_For_User               
000046f5  SYSCFG_DL_init                       
0000472d  __aeabi_f2iz                         
0000472d  __fixsfsi                            
00004765  sprintf                              
00004805  SYSCFG_DL_FOR_CONTROL_init           
000048f9  mspm0_delay_ms                       
00004929  oled_pow                             
00004a35  __aeabi_i2d                          
00004a35  __floatsidf                          
00004a61  fputc                                
00004a8d  mspm0_get_clock_ms                   
00004bf9  _c_int00_noargs                      
00004cb9  DL_I2C_setClockConfig                
00004d29  __aeabi_ui2d                         
00004d29  __floatunsidf                        
00004d4d  __aeabi_lmul                         
00004d4d  __muldi3                             
00004d71  memccpy                              
00004e11  __aeabi_llsl                         
00004e11  __ashldi3                            
00004f9d  DL_Timer_setCaptCompUpdateMethod     
00004fb9  DL_Timer_setClockConfig              
00005291  DL_Timer_setCaptureCompareOutCtl     
00005339  SYSCFG_DL_DMA_WIT_init               
00005351  SysTick_Init                         
00005431  Encoder_Init                         
00005447  __TI_zero_init_nomemset              
00005539  State_Machine_init                   
0000554d  WIT_Calibrate_Yaw                    
00005561  __aeabi_uldivmod                     
000055ad  DL_UART_setClockConfig               
000055bf  TI_memcpy_small                      
000055d1  __TI_decompress_none                 
00005605  DL_Timer_setCaptureCompareValue      
00005615  SysTick_Handler                      
00005625  delay_ms                             
00005635  wcslen                               
00005645  Get_Digtal_For_User                  
00005655  __aeabi_memset                       
00005655  __aeabi_memset4                      
00005655  __aeabi_memset8                      
00005671  TI_memset_small                      
00005681  Scheduler_Init                       
0000568d  __aeabi_memclr                       
0000568d  __aeabi_memclr4                      
0000568d  __aeabi_memclr8                      
00005699  DL_Common_delayCycles                
000056d1  SYSCFG_DL_DMA_init                   
000056d9  __aeabi_errno_addr                   
000056e1  __aeabi_memcpy                       
000056e1  __aeabi_memcpy4                      
000056e1  __aeabi_memcpy8                      
000056f9  Reset_Handler                        
0000570d  _system_pre_init                     
00005710  C$$EXIT                              
00005711  abort                                
00005720  asc2_1608                            
00005d10  asc2_0806                            
00005f40  __aeabi_ctype_table_                 
00005f40  __aeabi_ctype_table_C                
0000619c  __TI_Handler_Table_Base              
000061a8  __TI_Handler_Table_Limit             
000061b0  __TI_CINIT_Base                      
000061c0  __TI_CINIT_Limit                     
000061c0  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  wit_data                             
2020017c  angle_pid                            
202001a4  speedA_pid                           
202001cc  speedB_pid                           
202001f4  tracing_pid                          
2020021c  wit_dmaBuffer                        
2020023d  OLED_String                          
20200251  Digtal                               
20200252  Key_Down                             
20200253  Key_Old                              
20200254  State_Machine                        
20200268  Normal                               
20200278  grayscale_data                       
20200280  start_time                           
20200284  tick_ms                              
20200288  tracing_val                          
2020028c  uart_rx_ticks                        
20200290  Key_Up                               
20200291  Key_Val                              
20200292  grayscale_count                      
20200293  task_num                             
20200294  uart_rx_index                        
20200298  _ftable                              
20200388  uart_rx_buffer                       
2020042c  Anolog                               
2020043c  black                                
2020044c  white                                
2020045c  __aeabi_errno                        
20200460  _lock                                
20200464  _unlock                              
2020046c  encoder_A_count                      
2020046e  encoder_B_count                      
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[275 symbols]
