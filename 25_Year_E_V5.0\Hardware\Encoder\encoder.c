#include "encoder.h"

int16_t encoder_A_count = 0;
int16_t encoder_B_count = 0;
uint8_t encoder_count_flag = 0;
int16_t encoder_count = 0;

// 全局查表（避免冗余）
static const int8_t encoder_table[4][4] = {
    { 0, +1, -1, 0 }, // 上一状态 00
    { -1, 0, 0, +1 }, // 上一状态 01
    { +1, 0, 0, -1 }, // 上一状态 10
    { 0, -1, +1, 0 }  // 上一状态 11
};

int16_t Encoder_Get(uint8_t which_encoder)
{
    int16_t temp = 0;
    if(which_encoder == 1)
    {
        temp = encoder_A_count;
        if(encoder_count_flag == 1)
            encoder_count += encoder_A_count;
        encoder_A_count = 0;
    }
    else if(which_encoder == 2)
    {
        temp = -encoder_B_count;
        encoder_B_count = 0;
    }
    return temp;
}

void Encoder_Init(void)
{
	//编码器引脚外部中断
	NVIC_ClearPendingIRQ(ENCODER_INT_IRQN);
	NVIC_EnableIRQ(ENCODER_INT_IRQN);
}

void GROUP1_IRQHandler(void) {
    // 正确读取中断状态（修复引脚错误和拼写）
    uint32_t gpio_interrupt_A = DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT, ENCODER_E1A_PIN | ENCODER_E1B_PIN);
    uint32_t gpio_interrupt_B = DL_GPIO_getEnabledInterruptStatus(ENCODER_PORT, ENCODER_E2A_PIN | ENCODER_E2B_PIN);

    // 独立处理编码器 A（修复 else if 问题）
    if ((gpio_interrupt_A & ENCODER_E1A_PIN) || (gpio_interrupt_A & ENCODER_E1B_PIN)) {
        uint8_t A_state = DL_GPIO_readPins(ENCODER_PORT, ENCODER_E1A_PIN) ? 1 : 0;
        uint8_t B_state = DL_GPIO_readPins(ENCODER_PORT, ENCODER_E1B_PIN) ? 1 : 0;
        uint8_t current_state = (A_state << 1) | B_state;
        
        static uint8_t last_state_A = 0; // 静态局部变量保存状态
        
        encoder_A_count += encoder_table[last_state_A][current_state]; // 更新计数
        last_state_A = current_state;

        DL_GPIO_clearInterruptStatus(ENCODER_PORT, ENCODER_E1A_PIN | ENCODER_E1B_PIN); // 及时清除中断
    }

    // 独立处理编码器 B
    if ((gpio_interrupt_B & ENCODER_E2A_PIN) || (gpio_interrupt_B & ENCODER_E2B_PIN)) {
        uint8_t A_state = DL_GPIO_readPins(ENCODER_PORT, ENCODER_E2A_PIN) ? 1 : 0;
        uint8_t B_state = DL_GPIO_readPins(ENCODER_PORT, ENCODER_E2B_PIN) ? 1 : 0;
        uint8_t current_state = (A_state << 1) | B_state;
        
        static uint8_t last_state_B = 0; // 静态局部变量保存状态
        
        encoder_B_count += encoder_table[last_state_B][current_state]; // 更新计数
        last_state_B = current_state;

        DL_GPIO_clearInterruptStatus(ENCODER_PORT, ENCODER_E2A_PIN | ENCODER_E2B_PIN); // 及时清除中断
    }
}

