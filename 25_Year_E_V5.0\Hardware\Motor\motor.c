#include "motor.h"

uint32_t speed_basic = 200;//基础速度

/**
	电机控制方向函数
	motor： 1-电机A 2-电机B
	direction： 1-正向 2-反向
**/
void motor_direction(uint8_t motor, uint8_t direction)
{
	if(motor == 1)
	{
		if(direction == 1)
		{
			DL_GPIO_setPins(GPIO_MOTOR_PORT, GPIO_MOTOR_AIN1_PIN);
			DL_GPIO_clearPins(GPIO_MOTOR_PORT, GPIO_MOTOR_AIN2_PIN);
		}
		else if(direction == 2)
		{
			DL_GPIO_setPins(GPIO_MOTOR_PORT, GPIO_MOTOR_AIN2_PIN);
			DL_GPIO_clearPins(GPIO_MOTOR_PORT, GPIO_MOTOR_AIN1_PIN);
		}
	}
	else if(motor == 2)
	{
		if(direction == 1)
		{
			DL_GPIO_setPins(GPIO_MOTOR_PORT, GPIO_MOTOR_BIN2_PIN);
			DL_GPIO_clearPins(GPIO_MOTOR_PORT, GPIO_MOTOR_BIN1_PIN);
		}
		else if(direction == 2)
		{
			DL_GPIO_setPins(GPIO_MOTOR_PORT, GPIO_MOTOR_BIN1_PIN);
			DL_GPIO_clearPins(GPIO_MOTOR_PORT, GPIO_MOTOR_BIN2_PIN);
		}
	}
}


/**
	电机设置速度函数
	motorA： 电机A的PWM  0-100
	motorB： 电机B的PWM  0-100
**/
void pwm_set(float motorA, float motorB)
{
	if(motorA > 0)
		motor_direction(1, 1);
	else
		motor_direction(1, 2);
	
	if(motorB > 0)
		motor_direction(2, 1);
	else
		motor_direction(2, 2);
	
	if(motorA < 0)
		motorA = -motorA;
	
	if(motorB < 0)
		motorB = -motorB;
	
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, (uint16_t)motorA, GPIO_PWM_MOTOR_C0_IDX);
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, (uint16_t)motorB, GPIO_PWM_MOTOR_C1_IDX);
}
