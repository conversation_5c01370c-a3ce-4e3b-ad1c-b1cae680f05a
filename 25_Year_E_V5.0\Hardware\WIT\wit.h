/*
 * SysConfig Configuration Steps:
 *   UART:
 *     1. Add an UART module.
 *     2. Name it as "UART_WIT".
 *     3. Set "Target Baud Rate" according to module configuration.
 *     4. Set "Communication Direction " to "RX only".
 *     5. Check the box "Enable FIFOs".
 *     6. Set "RX Timeout Interrupt Counts" to "1".
 *     7. Check the "RX timeout" box at "Enable Interrupts".
 *     8. Set "Configure DMA RX Trigger" to "UART RX interrupt".
 *     9. Set "DMA Channel RX Name" to "DMA_WIT".
 *     10. Set "Address Mode" to "Fixed addr. to Block addr.".
 *     11. Set "Source Length" and "Destination Length" to "Byte".
 *     12. Set the pin according to your needs.
 */

#ifndef __WIT_H
#define __WIT_H

#include "HeadFiles.h"

typedef struct {
    float pitch;
    float roll;
    float yaw;
    float temperature;
    int16_t ax;
    int16_t ay;
    int16_t az;
    int16_t gx;
    int16_t gy;
    int16_t gz;
    int16_t version;
    // 添加相对角度相关变量
    float yaw_offset;        // 小车初始偏航角（校准值）
    float relative_yaw;      // 相对偏航角
    uint8_t is_calibrated;   // 校准标志
} WIT_Data_t;

extern WIT_Data_t wit_data;
extern float first_angle;
void WIT_Init(void);
void WIT_Get_Relative_Yaw(void);
void WIT_Calibrate_Yaw(void);

#endif /* #ifndef __WIT_H */
