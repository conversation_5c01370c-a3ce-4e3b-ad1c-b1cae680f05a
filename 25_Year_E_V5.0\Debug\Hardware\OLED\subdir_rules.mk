################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
Hardware/OLED/%.o: ../Hardware/OLED/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0" -I"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug" -I"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"Hardware/OLED/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


