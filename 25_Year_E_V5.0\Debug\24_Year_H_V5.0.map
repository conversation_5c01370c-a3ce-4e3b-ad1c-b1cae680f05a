******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Jul 19 21:41:59 2025

OUTPUT FILE NAME:   <24_Year_H_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005a1d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00007020  00018fe0  R  X
  SRAM                  20200000   00008000  00000697  00007969  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007020   00007020    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000064d0   000064d0    r-x .text
  00006590    00006590    000009f0   000009f0    r-- .rodata
  00006f80    00006f80    000000a0   000000a0    r-- .cinit
20200000    20200000    0000049a   00000000    rw-
  20200000    20200000    000002a1   00000000    rw- .bss
  202002a4    202002a4    000001f6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000064d0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000474     app_question_task.o (.text.Question_Task_4)
                  00000f04    000003cc     app_question_task.o (.text.Question_Task_3)
                  000012d0    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  0000163c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000185c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001a38    000001ac     app_question_task.o (.text.Question_Task_2)
                  00001be4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001d76    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001d78    00000190     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001f08    00000190     pid.o (.text.Yaw_error_zzk)
                  00002098    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002220    00000150     pid.o (.text.PID_angle_realize)
                  00002370    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000024ac    0000012c     pid.o (.text.Tracing_Value_Get)
                  000025d8    00000128     pid.o (.text.PID_speed_realize)
                  00002700    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00002824    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002944    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00002a5c    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00002b6c    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  00002c7c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002d88    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002e8c    00000104     pid.o (.text.PID_tracing_realize)
                  00002f90    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  00003086    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003088    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  00003174    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000325c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003340    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000341c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000034f4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000035cc    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  0000369c    000000c4     motor.o (.text.motor_direction)
                  00003760    000000ac     motor.o (.text.pwm_set)
                  0000380c    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  000038b6    00000002     libc.a : _lock.c.obj (.text._nop)
                  000038b8    000000a8     key.o (.text.Key_Proc)
                  00003960    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003a02    00000002     --HOLE-- [fill = 0]
                  00003a04    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00003aa4    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003b40    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00003bd8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003c64    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003cf0    00000088     pid.o (.text.PID_init)
                  00003d78    00000084     interrupt.o (.text.TIMG0_IRQHandler)
                  00003dfc    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003e80    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003f04    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003f86    00000002     --HOLE-- [fill = 0]
                  00003f88    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004004    00000078     key.o (.text.Key_Read)
                  0000407c    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  000040f4    00000074     encoder.o (.text.Encoder_Get)
                  00004168    00000074     Scheduler.o (.text.Scheduler_Run)
                  000041dc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004250    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000042c4    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004336    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  000043a6    00000002     --HOLE-- [fill = 0]
                  000043a8    00000070     app_question_task.o (.text.Question_Task_1)
                  00004418    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00004484    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  000044ee    00000002     --HOLE-- [fill = 0]
                  000044f0    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00004558    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000045c0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004626    00000002     --HOLE-- [fill = 0]
                  00004628    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  0000468c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000046ee    00000002     --HOLE-- [fill = 0]
                  000046f0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00004752    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000047b0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000480c    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00004868    0000005c     libc.a : printf.c.obj (.text.printf)
                  000048c4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000491c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004974    00000058            : _printfi.c.obj (.text._pconv_f)
                  000049cc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004a22    00000002     --HOLE-- [fill = 0]
                  00004a24    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00004a78    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004acc    00000054     main.o (.text.main)
                  00004b20    00000054     usart_app.o (.text.uart_task)
                  00004b74    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004bc6    00000002     --HOLE-- [fill = 0]
                  00004bc8    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00004c18    00000050     clock.o (.text.SysTick_Config)
                  00004c68    00000050     wit.o (.text.WIT_Init)
                  00004cb8    00000050     usart_app.o (.text.fputs)
                  00004d08    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004d54    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004da0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004dec    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004e36    00000002     --HOLE-- [fill = 0]
                  00004e38    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004e82    0000004a     adc_app.o (.text.adc_getValue)
                  00004ecc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004f14    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004f5c    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00004fa4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004fe8    00000044     usart_app.o (.text.UART0_IRQHandler)
                  0000502c    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000506e    00000002     --HOLE-- [fill = 0]
                  00005070    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000050b0    00000040     app_tracing_control.o (.text.Tracing_Control)
                  000050f0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005130    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005170    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000051b0    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000051ec    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00005228    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00005264    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000052a0    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000052dc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005318    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00005354    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00005390    0000003c     app_angle_control.o (.text.angele_control)
                  000053cc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00005408    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00005442    00000002     --HOLE-- [fill = 0]
                  00005444    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000547e    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000054b6    00000002     --HOLE-- [fill = 0]
                  000054b8    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000054f0    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005528    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005560    00000034     beep.o (.text.Beep_Time_Control)
                  00005594    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000055c8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000055fc    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00005630    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00005660    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00005690    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  000056c0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000056f0    00000030     clock.o (.text.mspm0_delay_ms)
                  00005720    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00005750    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  0000577c    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  000057a8    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  000057d4    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00005800    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  0000582c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00005858    0000002c     pid.o (.text.fabs_zzk)
                  00005884    0000002c     usart_app.o (.text.fputc)
                  000058b0    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  000058dc    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00005904    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000592c    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00005954    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  0000597c    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  000059a4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000059cc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000059f4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005a1c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005a44    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00005a6a    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00005a90    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00005ab6    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005adc    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005b02    00000002     --HOLE-- [fill = 0]
                  00005b04    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00005b28    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005b4c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005b70    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005b94    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005bb6    00000002     --HOLE-- [fill = 0]
                  00005bb8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005bd8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005bf8    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00005c16    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005c34    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005c52    00000002     --HOLE-- [fill = 0]
                  00005c54    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00005c70    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00005c8c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00005ca8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005cc4    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00005ce0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005cfc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005d18    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005d34    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005d50    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005d6c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005d88    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005da4    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005dc0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005ddc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005df8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005e14    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005e2c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005e44    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005e5c    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005e74    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005e8c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005ea4    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005ebc    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00005ed4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005eec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005f04    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005f1c    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005f34    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005f4c    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005f64    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005f7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005f94    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005fac    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005fc4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005fdc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005ff4    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  0000600c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00006024    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  0000603c    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00006054    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000606c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00006084    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000609c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000060b4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000060cc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000060e4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000060fc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00006114    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000612c    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00006144    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  0000615c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00006174    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  0000618c    00000018     clock.o (.text.SysTick_Init)
                  000061a4    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000061bc    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  000061d2    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  000061e8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000061fe    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00006214    00000016     key.o (.text.DL_GPIO_readPins)
                  0000622a    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00006240    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00006256    00000016     usart_app.o (.text.DL_UART_transmitData)
                  0000626c    00000016     encoder.o (.text.Encoder_Init)
                  00006282    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00006298    00000014     beep.o (.text.Beep_ms)
                  000062ac    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  000062c0    00000014     beep.o (.text.DL_GPIO_clearPins)
                  000062d4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000062e8    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  000062fc    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00006310    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00006324    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00006338    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000634c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00006360    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00006374    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00006388    00000014     usart_app.o (.text.DL_UART_receiveData)
                  0000639c    00000014     app_question_task.o (.text.State_Machine_init)
                  000063b0    00000014     wit.o (.text.WIT_Calibrate_Yaw)
                  000063c4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000063d8    00000014     pid.o (.text.pid_set_angle_target)
                  000063ec    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00006400    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00006412    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00006424    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00006436    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00006448    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000645a    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  0000646a    00000002     --HOLE-- [fill = 0]
                  0000646c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000647c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000648c    00000010     interrupt.o (.text.SysTick_Handler)
                  0000649c    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  000064ac    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000064bc    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  000064ca    00000002     --HOLE-- [fill = 0]
                  000064cc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000064da    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000064e8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000064f6    00000002     --HOLE-- [fill = 0]
                  000064f8    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00006504    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00006510    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000651a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006524    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00006534    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000653e    0000000a            : sprintf.c.obj (.text._outc)
                  00006548    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006550    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00006558    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006560    00000008     libc.a : printf.c.obj (.text._outc)
                  00006568    00000008            : printf.c.obj (.text._outs)
                  00006570    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00006574    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00006578    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00006588    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000658c    00000004            : exit.c.obj (.text:abort)

.cinit     0    00006f80    000000a0     
                  00006f80    00000079     (.cinit..data.load) [load image, compression = lzss]
                  00006ff9    00000003     --HOLE-- [fill = 0]
                  00006ffc    0000000c     (__TI_handler_table)
                  00007008    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00007010    00000010     (__TI_cinit_table)

.rodata    0    00006590    000009f0     
                  00006590    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00006b80    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006da8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006db0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006eb1    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006eb4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006edc    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006ef4    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006f08    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006f19    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00006f2a    00000010     encoder.o (.rodata.encoder_table)
                  00006f3a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006f49    00000001     --HOLE-- [fill = 0]
                  00006f4a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006f54    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00006f5e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006f60    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006f68    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  00006f6d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006f70    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006f72    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006f74    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000002a1     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    0000002c     (.common:wit_data)
                  2020017c    00000028     (.common:angle_pid)
                  202001a4    00000028     (.common:speedA_pid)
                  202001cc    00000028     (.common:speedB_pid)
                  202001f4    00000028     (.common:tracing_pid)
                  2020021c    00000021     (.common:wit_dmaBuffer)
                  2020023d    00000014     (.common:OLED_String)
                  20200251    00000001     (.common:Digtal)
                  20200252    00000001     (.common:Key_Down)
                  20200253    00000001     (.common:Key_Old)
                  20200254    00000014     (.common:State_Machine)
                  20200268    00000010     (.common:Normal)
                  20200278    00000008     (.common:grayscale_data)
                  20200280    00000004     pid.o (.bss.Yaw_error_zzk.error)
                  20200284    00000004     (.common:first_angle)
                  20200288    00000004     (.common:start_time)
                  2020028c    00000004     (.common:target_angle)
                  20200290    00000004     (.common:tick_ms)
                  20200294    00000004     (.common:tracing_val)
                  20200298    00000004     (.common:uart_rx_ticks)
                  2020029c    00000001     (.common:Key_Up)
                  2020029d    00000001     (.common:Key_Val)
                  2020029e    00000001     (.common:grayscale_count)
                  2020029f    00000001     (.common:task_num)
                  202002a0    00000001     (.common:uart_rx_index)

.data      0    202002a4    000001f6     UNINITIALIZED
                  202002a4    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200394    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200414    00000024     Scheduler.o (.data.scheduler_task)
                  20200438    00000010     Ganv_Grayscale.o (.data.Anolog)
                  20200448    00000010     Ganv_Grayscale.o (.data.black)
                  20200458    00000010     Ganv_Grayscale.o (.data.white)
                  20200468    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020046c    00000004            : _lock.c.obj (.data._lock)
                  20200470    00000004            : _lock.c.obj (.data._unlock)
                  20200474    00000004     pid.o (.data.angle_basic_speed)
                  20200478    00000004     beep.o (.data.bee_time)
                  2020047c    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200480    00000004     motor.o (.data.speed_basic)
                  20200484    00000004     pid.o (.data.tracing_basic_speed)
                  20200488    00000002     app_question_task.o (.data.Question_Task_4.q4_count)
                  2020048a    00000002     encoder.o (.data.encoder_A_count)
                  2020048c    00000002     encoder.o (.data.encoder_B_count)
                  2020048e    00000002     encoder.o (.data.encoder_count)
                  20200490    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200491    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200492    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200493    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200494    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200495    00000001     encoder.o (.data.encoder_count_flag)
                  20200496    00000001     app_question_task.o (.data.q1_first_flag)
                  20200497    00000001     app_question_task.o (.data.q2_first_flag)
                  20200498    00000001     app_question_task.o (.data.q3_first_flag)
                  20200499    00000001     app_question_task.o (.data.q4_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3000    132       160    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3180    324       160    
                                                               
    .\APP\
       app_question_task.o            2672    0         30     
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          64      0         0      
       app_angle_control.o            60      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3032    0         37     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         116     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         116     0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          310     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         310     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1286    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1614    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         4      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          1792    0         176    
    +--+------------------------------+-------+---------+---------+
       Total:                         1792    0         176    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          430     0         81     
    +--+------------------------------+-------+---------+---------+
       Total:                         430     0         81     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       157       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   25772   2880      1687   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00007010 records: 2, size/record: 8, table size: 16
	.data: load addr=00006f80, load size=00000079 bytes, run addr=202002a4, run size=000001f6 bytes, compression=lzss
	.bss: load addr=00007008, load size=00000008 bytes, run addr=20200000, run size=000002a1 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006ffc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001be5     00006524     00006522   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005a1d     00006578     00006574   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00006571  ADC0_IRQHandler                      
00006571  ADC1_IRQHandler                      
00006571  AES_IRQHandler                       
20200438  Anolog                               
00005561  Beep_Time_Control                    
00006299  Beep_ms                              
0000658c  C$$EXIT                              
00006571  CANFD0_IRQHandler                    
00006571  DAC0_IRQHandler                      
00005071  DL_ADC12_setClockConfig              
00006511  DL_Common_delayCycles                
00004d09  DL_DMA_initChannel                   
00004753  DL_I2C_fillControllerTXFIFO          
00005add  DL_I2C_setClockConfig                
00003341  DL_SYSCTL_configSYSPLL               
00004fa5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002d89  DL_Timer_initFourCCPWMMode           
00003175  DL_Timer_initTimerMode               
00005dc1  DL_Timer_setCaptCompUpdateMethod     
000060cd  DL_Timer_setCaptureCompareOutCtl     
0000647d  DL_Timer_setCaptureCompareValue      
00005ddd  DL_Timer_setClockConfig              
00004a25  DL_UART_drainRXFIFO                  
00004ecd  DL_UART_init                         
00006425  DL_UART_setClockConfig               
00006571  DMA_IRQHandler                       
00006571  Default_Handler                      
20200251  Digtal                               
000040f5  Encoder_Get                          
0000626d  Encoder_Init                         
00006571  GROUP0_IRQHandler                    
00002701  GROUP1_IRQHandler                    
000035cd  Get_Analog_value                     
00005265  Get_Anolog_Value                     
000064bd  Get_Digtal_For_User                  
0000547f  Get_Normalize_For_User               
00006571  HardFault_Handler                    
00006571  I2C0_IRQHandler                      
00006571  I2C1_IRQHandler                      
20200252  Key_Down                             
20200253  Key_Old                              
000038b9  Key_Proc                             
00004005  Key_Read                             
2020029c  Key_Up                               
2020029d  Key_Val                              
00006571  NMI_Handler                          
00002099  No_MCU_Ganv_Sensor_Init              
000042c5  No_MCU_Ganv_Sensor_Init_Frist        
0000502d  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200268  Normal                               
00004485  OLED_Clear                           
00002a5d  OLED_Init                            
000052a1  OLED_Set_Pos                         
00002945  OLED_ShowChar                        
00002f91  OLED_ShowNum                         
00004337  OLED_ShowString                      
2020023d  OLED_String                          
00003b41  OLED_WR_Byte                         
00002b6d  Oled_Task                            
00002221  PID_angle_realize                    
00003cf1  PID_init                             
000025d9  PID_speed_realize                    
00002e8d  PID_tracing_realize                  
00006571  PendSV_Handler                       
000043a9  Question_Task_1                      
00001a39  Question_Task_2                      
00000f05  Question_Task_3                      
00000a91  Question_Task_4                      
00006571  RTC_IRQHandler                       
00006575  Reset_Handler                        
00006571  SPI0_IRQHandler                      
00006571  SPI1_IRQHandler                      
00006571  SVC_Handler                          
00004da1  SYSCFG_DL_ADC1_init                  
00006175  SYSCFG_DL_DMA_WIT_init               
00006549  SYSCFG_DL_DMA_init                   
000055fd  SYSCFG_DL_FOR_CONTROL_init           
00001d79  SYSCFG_DL_GPIO_init                  
000048c5  SYSCFG_DL_I2C_OLED_init              
00003bd9  SYSCFG_DL_PWM_MOTOR_init             
000052dd  SYSCFG_DL_SYSCTL_init                
00004a79  SYSCFG_DL_UART_0_init                
000044f1  SYSCFG_DL_UART_WIT_init              
000054b9  SYSCFG_DL_init                       
00003aa5  SYSCFG_DL_initPower                  
000064f9  Scheduler_Init                       
00004169  Scheduler_Run                        
20200254  State_Machine                        
0000639d  State_Machine_init                   
0000648d  SysTick_Handler                      
0000618d  SysTick_Init                         
00006571  TIMA0_IRQHandler                     
00006571  TIMA1_IRQHandler                     
00003d79  TIMG0_IRQHandler                     
00006571  TIMG12_IRQHandler                    
00006571  TIMG6_IRQHandler                     
00006571  TIMG7_IRQHandler                     
00006571  TIMG8_IRQHandler                     
00006437  TI_memcpy_small                      
000064e9  TI_memset_small                      
000050b1  Tracing_Control                      
000024ad  Tracing_Value_Get                    
00004fe9  UART0_IRQHandler                     
00006571  UART1_IRQHandler                     
000012d1  UART2_IRQHandler                     
00006571  UART3_IRQHandler                     
000063b1  WIT_Calibrate_Yaw                    
0000407d  WIT_Get_Relative_Yaw                 
00004c69  WIT_Init                             
00001f09  Yaw_error_zzk                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00007010  __TI_CINIT_Base                      
00007020  __TI_CINIT_Limit                     
00007020  __TI_CINIT_Warm                      
00006ffc  __TI_Handler_Table_Base              
00007008  __TI_Handler_Table_Limit             
000053cd  __TI_auto_init_nobinit_nopinit       
00003f89  __TI_decompress_lzss                 
00006449  __TI_decompress_none                 
0000491d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00006283  __TI_zero_init_nomemset              
00001bef  __adddf3                             
000034ff  __addsf3                             
00006db0  __aeabi_ctype_table_                 
00006db0  __aeabi_ctype_table_C                
00004251  __aeabi_d2f                          
00004e39  __aeabi_d2iz                         
00001bef  __aeabi_dadd                         
0000468d  __aeabi_dcmpeq                       
000046c9  __aeabi_dcmpge                       
000046dd  __aeabi_dcmpgt                       
000046b5  __aeabi_dcmple                       
000046a1  __aeabi_dcmplt                       
00002c7d  __aeabi_ddiv                         
0000325d  __aeabi_dmul                         
00001be5  __aeabi_dsub                         
20200468  __aeabi_errno                        
00006551  __aeabi_errno_addr                   
00005131  __aeabi_f2d                          
000054f1  __aeabi_f2iz                         
000034ff  __aeabi_fadd                         
000046f1  __aeabi_fcmpeq                       
0000472d  __aeabi_fcmpge                       
00004741  __aeabi_fcmpgt                       
00004719  __aeabi_fcmple                       
00004705  __aeabi_fcmplt                       
00003f05  __aeabi_fdiv                         
00003c65  __aeabi_fmul                         
000034f5  __aeabi_fsub                         
0000582d  __aeabi_i2d                          
00005319  __aeabi_i2f                          
000049cd  __aeabi_idiv                         
00001d77  __aeabi_idiv0                        
000049cd  __aeabi_idivmod                      
00003087  __aeabi_ldiv0                        
00005c35  __aeabi_llsl                         
00005b71  __aeabi_lmul                         
00006505  __aeabi_memclr                       
00006505  __aeabi_memclr4                      
00006505  __aeabi_memclr8                      
00006559  __aeabi_memcpy                       
00006559  __aeabi_memcpy4                      
00006559  __aeabi_memcpy8                      
000064cd  __aeabi_memset                       
000064cd  __aeabi_memset4                      
000064cd  __aeabi_memset8                      
00005b4d  __aeabi_ui2d                         
000050f1  __aeabi_uidiv                        
000050f1  __aeabi_uidivmod                     
000063c5  __aeabi_uldivmod                     
00005c35  __ashldi3                            
ffffffff  __binit__                            
00004559  __cmpdf2                             
00005409  __cmpsf2                             
00002c7d  __divdf3                             
00003f05  __divsf3                             
00004559  __eqdf2                              
00005409  __eqsf2                              
00005131  __extendsfdf2                        
00004e39  __fixdfsi                            
000054f1  __fixsfsi                            
0000582d  __floatsidf                          
00005319  __floatsisf                          
00005b4d  __floatunsidf                        
000041dd  __gedf2                              
00005355  __gesf2                              
000041dd  __gtdf2                              
00005355  __gtsf2                              
00004559  __ledf2                              
00005409  __lesf2                              
00004559  __ltdf2                              
00005409  __ltsf2                              
UNDEFED   __mpu_init                           
0000325d  __muldf3                             
00005b71  __muldi3                             
00005445  __muldsi3                            
00003c65  __mulsf3                             
00004559  __nedf2                              
00005409  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001be5  __subdf3                             
000034f5  __subsf3                             
00004251  __truncdfsf2                         
00003961  __udivmoddi4                         
00005a1d  _c_int00_noargs                      
202002a4  _ftable                              
2020046c  _lock                                
000038b7  _nop                                 
UNDEFED   _system_post_cinit                   
00006589  _system_pre_init                     
20200470  _unlock                              
0000658d  abort                                
00004e83  adc_getValue                         
00005391  angele_control                       
20200474  angle_basic_speed                    
2020017c  angle_pid                            
00006b80  asc2_0806                            
00006590  asc2_1608                            
00005171  atoi                                 
20200478  bee_time                             
ffffffff  binit                                
20200448  black                                
00004419  convertAnalogToDigital               
0000649d  delay_ms                             
00003089  detect_trace_state_change            
2020048a  encoder_A_count                      
2020048c  encoder_B_count                      
2020048e  encoder_count                        
20200495  encoder_count_flag                   
00005859  fabs_zzk                             
20200284  first_angle                          
00005885  fputc                                
00004cb9  fputs                                
000047b1  frexp                                
000047b1  frexpl                               
202000b0  gPWM_MOTORBackup                     
00004f15  gray_init                            
00003a05  gray_task                            
2020029e  grayscale_count                      
20200278  grayscale_data                       
00000000  interruptVectors                     
0000341d  ldexp                                
0000341d  ldexpl                               
00004acd  main                                 
00005b95  memccpy                              
0000369d  motor_direction                      
000056f1  mspm0_delay_ms                       
000058b1  mspm0_get_clock_ms                   
0000380d  normalizeAnalogValues                
00004629  oled_i2c_sda_unlock                  
00005721  oled_pow                             
000063d9  pid_set_angle_target                 
00004869  printf                               
00003761  pwm_set                              
20200496  q1_first_flag                        
20200497  q2_first_flag                        
20200498  q3_first_flag                        
20200499  q4_first_flag                        
0000341d  scalbn                               
0000341d  scalbnl                              
20200000  sensor                               
202001a4  speedA_pid                           
202001cc  speedB_pid                           
20200480  speed_basic                          
00005529  sprintf                              
20200288  start_time                           
2020028c  target_angle                         
2020029f  task_num                             
20200290  tick_ms                              
20200484  tracing_basic_speed                  
202001f4  tracing_pid                          
20200294  tracing_val                          
20200394  uart_rx_buffer                       
202002a0  uart_rx_index                        
20200298  uart_rx_ticks                        
00004b21  uart_task                            
000064ad  wcslen                               
20200458  white                                
20200150  wit_data                             
2020021c  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Question_Task_4                      
00000f05  Question_Task_3                      
000012d1  UART2_IRQHandler                     
00001a39  Question_Task_2                      
00001be5  __aeabi_dsub                         
00001be5  __subdf3                             
00001bef  __adddf3                             
00001bef  __aeabi_dadd                         
00001d77  __aeabi_idiv0                        
00001d79  SYSCFG_DL_GPIO_init                  
00001f09  Yaw_error_zzk                        
00002099  No_MCU_Ganv_Sensor_Init              
00002221  PID_angle_realize                    
000024ad  Tracing_Value_Get                    
000025d9  PID_speed_realize                    
00002701  GROUP1_IRQHandler                    
00002945  OLED_ShowChar                        
00002a5d  OLED_Init                            
00002b6d  Oled_Task                            
00002c7d  __aeabi_ddiv                         
00002c7d  __divdf3                             
00002d89  DL_Timer_initFourCCPWMMode           
00002e8d  PID_tracing_realize                  
00002f91  OLED_ShowNum                         
00003087  __aeabi_ldiv0                        
00003089  detect_trace_state_change            
00003175  DL_Timer_initTimerMode               
0000325d  __aeabi_dmul                         
0000325d  __muldf3                             
00003341  DL_SYSCTL_configSYSPLL               
0000341d  ldexp                                
0000341d  ldexpl                               
0000341d  scalbn                               
0000341d  scalbnl                              
000034f5  __aeabi_fsub                         
000034f5  __subsf3                             
000034ff  __addsf3                             
000034ff  __aeabi_fadd                         
000035cd  Get_Analog_value                     
0000369d  motor_direction                      
00003761  pwm_set                              
0000380d  normalizeAnalogValues                
000038b7  _nop                                 
000038b9  Key_Proc                             
00003961  __udivmoddi4                         
00003a05  gray_task                            
00003aa5  SYSCFG_DL_initPower                  
00003b41  OLED_WR_Byte                         
00003bd9  SYSCFG_DL_PWM_MOTOR_init             
00003c65  __aeabi_fmul                         
00003c65  __mulsf3                             
00003cf1  PID_init                             
00003d79  TIMG0_IRQHandler                     
00003f05  __aeabi_fdiv                         
00003f05  __divsf3                             
00003f89  __TI_decompress_lzss                 
00004005  Key_Read                             
0000407d  WIT_Get_Relative_Yaw                 
000040f5  Encoder_Get                          
00004169  Scheduler_Run                        
000041dd  __gedf2                              
000041dd  __gtdf2                              
00004251  __aeabi_d2f                          
00004251  __truncdfsf2                         
000042c5  No_MCU_Ganv_Sensor_Init_Frist        
00004337  OLED_ShowString                      
000043a9  Question_Task_1                      
00004419  convertAnalogToDigital               
00004485  OLED_Clear                           
000044f1  SYSCFG_DL_UART_WIT_init              
00004559  __cmpdf2                             
00004559  __eqdf2                              
00004559  __ledf2                              
00004559  __ltdf2                              
00004559  __nedf2                              
00004629  oled_i2c_sda_unlock                  
0000468d  __aeabi_dcmpeq                       
000046a1  __aeabi_dcmplt                       
000046b5  __aeabi_dcmple                       
000046c9  __aeabi_dcmpge                       
000046dd  __aeabi_dcmpgt                       
000046f1  __aeabi_fcmpeq                       
00004705  __aeabi_fcmplt                       
00004719  __aeabi_fcmple                       
0000472d  __aeabi_fcmpge                       
00004741  __aeabi_fcmpgt                       
00004753  DL_I2C_fillControllerTXFIFO          
000047b1  frexp                                
000047b1  frexpl                               
00004869  printf                               
000048c5  SYSCFG_DL_I2C_OLED_init              
0000491d  __TI_ltoa                            
000049cd  __aeabi_idiv                         
000049cd  __aeabi_idivmod                      
00004a25  DL_UART_drainRXFIFO                  
00004a79  SYSCFG_DL_UART_0_init                
00004acd  main                                 
00004b21  uart_task                            
00004c69  WIT_Init                             
00004cb9  fputs                                
00004d09  DL_DMA_initChannel                   
00004da1  SYSCFG_DL_ADC1_init                  
00004e39  __aeabi_d2iz                         
00004e39  __fixdfsi                            
00004e83  adc_getValue                         
00004ecd  DL_UART_init                         
00004f15  gray_init                            
00004fa5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004fe9  UART0_IRQHandler                     
0000502d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005071  DL_ADC12_setClockConfig              
000050b1  Tracing_Control                      
000050f1  __aeabi_uidiv                        
000050f1  __aeabi_uidivmod                     
00005131  __aeabi_f2d                          
00005131  __extendsfdf2                        
00005171  atoi                                 
00005265  Get_Anolog_Value                     
000052a1  OLED_Set_Pos                         
000052dd  SYSCFG_DL_SYSCTL_init                
00005319  __aeabi_i2f                          
00005319  __floatsisf                          
00005355  __gesf2                              
00005355  __gtsf2                              
00005391  angele_control                       
000053cd  __TI_auto_init_nobinit_nopinit       
00005409  __cmpsf2                             
00005409  __eqsf2                              
00005409  __lesf2                              
00005409  __ltsf2                              
00005409  __nesf2                              
00005445  __muldsi3                            
0000547f  Get_Normalize_For_User               
000054b9  SYSCFG_DL_init                       
000054f1  __aeabi_f2iz                         
000054f1  __fixsfsi                            
00005529  sprintf                              
00005561  Beep_Time_Control                    
000055fd  SYSCFG_DL_FOR_CONTROL_init           
000056f1  mspm0_delay_ms                       
00005721  oled_pow                             
0000582d  __aeabi_i2d                          
0000582d  __floatsidf                          
00005859  fabs_zzk                             
00005885  fputc                                
000058b1  mspm0_get_clock_ms                   
00005a1d  _c_int00_noargs                      
00005add  DL_I2C_setClockConfig                
00005b4d  __aeabi_ui2d                         
00005b4d  __floatunsidf                        
00005b71  __aeabi_lmul                         
00005b71  __muldi3                             
00005b95  memccpy                              
00005c35  __aeabi_llsl                         
00005c35  __ashldi3                            
00005dc1  DL_Timer_setCaptCompUpdateMethod     
00005ddd  DL_Timer_setClockConfig              
000060cd  DL_Timer_setCaptureCompareOutCtl     
00006175  SYSCFG_DL_DMA_WIT_init               
0000618d  SysTick_Init                         
0000626d  Encoder_Init                         
00006283  __TI_zero_init_nomemset              
00006299  Beep_ms                              
0000639d  State_Machine_init                   
000063b1  WIT_Calibrate_Yaw                    
000063c5  __aeabi_uldivmod                     
000063d9  pid_set_angle_target                 
00006425  DL_UART_setClockConfig               
00006437  TI_memcpy_small                      
00006449  __TI_decompress_none                 
0000647d  DL_Timer_setCaptureCompareValue      
0000648d  SysTick_Handler                      
0000649d  delay_ms                             
000064ad  wcslen                               
000064bd  Get_Digtal_For_User                  
000064cd  __aeabi_memset                       
000064cd  __aeabi_memset4                      
000064cd  __aeabi_memset8                      
000064e9  TI_memset_small                      
000064f9  Scheduler_Init                       
00006505  __aeabi_memclr                       
00006505  __aeabi_memclr4                      
00006505  __aeabi_memclr8                      
00006511  DL_Common_delayCycles                
00006549  SYSCFG_DL_DMA_init                   
00006551  __aeabi_errno_addr                   
00006559  __aeabi_memcpy                       
00006559  __aeabi_memcpy4                      
00006559  __aeabi_memcpy8                      
00006571  ADC0_IRQHandler                      
00006571  ADC1_IRQHandler                      
00006571  AES_IRQHandler                       
00006571  CANFD0_IRQHandler                    
00006571  DAC0_IRQHandler                      
00006571  DMA_IRQHandler                       
00006571  Default_Handler                      
00006571  GROUP0_IRQHandler                    
00006571  HardFault_Handler                    
00006571  I2C0_IRQHandler                      
00006571  I2C1_IRQHandler                      
00006571  NMI_Handler                          
00006571  PendSV_Handler                       
00006571  RTC_IRQHandler                       
00006571  SPI0_IRQHandler                      
00006571  SPI1_IRQHandler                      
00006571  SVC_Handler                          
00006571  TIMA0_IRQHandler                     
00006571  TIMA1_IRQHandler                     
00006571  TIMG12_IRQHandler                    
00006571  TIMG6_IRQHandler                     
00006571  TIMG7_IRQHandler                     
00006571  TIMG8_IRQHandler                     
00006571  UART1_IRQHandler                     
00006571  UART3_IRQHandler                     
00006575  Reset_Handler                        
00006589  _system_pre_init                     
0000658c  C$$EXIT                              
0000658d  abort                                
00006590  asc2_1608                            
00006b80  asc2_0806                            
00006db0  __aeabi_ctype_table_                 
00006db0  __aeabi_ctype_table_C                
00006ffc  __TI_Handler_Table_Base              
00007008  __TI_Handler_Table_Limit             
00007010  __TI_CINIT_Base                      
00007020  __TI_CINIT_Limit                     
00007020  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  wit_data                             
2020017c  angle_pid                            
202001a4  speedA_pid                           
202001cc  speedB_pid                           
202001f4  tracing_pid                          
2020021c  wit_dmaBuffer                        
2020023d  OLED_String                          
20200251  Digtal                               
20200252  Key_Down                             
20200253  Key_Old                              
20200254  State_Machine                        
20200268  Normal                               
20200278  grayscale_data                       
20200284  first_angle                          
20200288  start_time                           
2020028c  target_angle                         
20200290  tick_ms                              
20200294  tracing_val                          
20200298  uart_rx_ticks                        
2020029c  Key_Up                               
2020029d  Key_Val                              
2020029e  grayscale_count                      
2020029f  task_num                             
202002a0  uart_rx_index                        
202002a4  _ftable                              
20200394  uart_rx_buffer                       
20200438  Anolog                               
20200448  black                                
20200458  white                                
20200468  __aeabi_errno                        
2020046c  _lock                                
20200470  _unlock                              
20200474  angle_basic_speed                    
20200478  bee_time                             
20200480  speed_basic                          
20200484  tracing_basic_speed                  
2020048a  encoder_A_count                      
2020048c  encoder_B_count                      
2020048e  encoder_count                        
20200495  encoder_count_flag                   
20200496  q1_first_flag                        
20200497  q2_first_flag                        
20200498  q3_first_flag                        
20200499  q4_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[299 symbols]
