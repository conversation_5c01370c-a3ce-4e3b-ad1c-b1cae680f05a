#include "beep.h"

#define BEEP(x)    ( (x) ? (DL_GPIO_clearPins(BEEP_PORT, BEEP_PIN_5_PIN)) : (DL_GPIO_setPins(BEEP_PORT, BEEP_PIN_5_PIN)) )

//the sound_time of beep(the unit is ms)
volatile unsigned int bee_time = 0;

void Beep_ms(unsigned int time)
{
    bee_time = time;
}

void Beep_Time_Control(void)
{
  /****************************************beep_control**********************************/
  if(bee_time > 0)
  {
    bee_time -= 10;
    BEEP(1);
  }
  else
  {
    BEEP(0);
  }
}



