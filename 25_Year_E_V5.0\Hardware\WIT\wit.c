#include "wit.h"

uint8_t wit_dmaBuffer[33];

WIT_Data_t wit_data;            //陀螺仪相关数据
float first_angle;              //第一次运行角度

void WIT_Init(void)
{
    DL_DMA_setSrcAddr(DMA, DMA_WIT_CHAN_ID, (uint32_t)(&UART_WIT_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_WIT_CHAN_ID, (uint32_t) &wit_dmaBuffer[0]);
    DL_DMA_setTransferSize(DMA, DMA_WIT_CHAN_ID, 32);
    DL_DMA_enableChannel(DMA, DMA_WIT_CHAN_ID);

    NVIC_EnableIRQ(UART_WIT_INST_INT_IRQN);

    delay_ms(1000);
    WIT_Calibrate_Yaw();
}

// 角度校准函数 - 设置小车当前方向为0度参考
void WIT_Calibrate_Yaw(void) 
{
    wit_data.yaw_offset = wit_data.yaw;  // 记录当前绝对角度作为偏移
    wit_data.is_calibrated = 1;
}

// 获取小车相对角度
void WIT_Get_Relative_Yaw(void) 
{
    if (!wit_data.is_calibrated) 
    {
        return ;  // 未校准时返回0
    }
    
    // 计算相对角度
    float relative = wit_data.yaw - wit_data.yaw_offset;
    
    // 角度归一化到 -180° ~ +180°
    while (relative > 180.0f) relative -= 360.0f;
    while (relative < -180.0f) relative += 360.0f;

    // //转为0°~360°
    // if(relative < 0)
    // {
    //     relative = -relative;        // 负角度取绝对值
    // }
    // else if(relative >= 0)
    // {
    //     relative = 360 - relative;   // 正角度用360减去
    // }
    
    wit_data.relative_yaw = relative;
}

