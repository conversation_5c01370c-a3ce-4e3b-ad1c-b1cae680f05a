#ifndef __SERVO_H
#define __SERVO_H

#include "HeadFiles.h"

// 舵机控制函数声明
void Servo_init(void);                    // 舵机初始化
void Servo_SetAngle(float angle);         // 设置舵机角度 (-45° ~ +45°)
void Servo_SetCenter(void);               // 舵机回中 (0°)

// 原始函数(保留兼容性)
void set_angle(uint16_t angle);           // 原始角度设置函数
void Servo_Proc_stiff(void);              // 舵机处理函数
void Servo_Proc_Zero(void);               // 舵机归零函数
void camra_findline_task_servoVersion(void); // 摄像头循迹任务

#endif
