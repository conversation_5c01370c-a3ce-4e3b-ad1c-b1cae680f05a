#ifndef __PID_H
#define __PID_H

#include "HeadFiles.h"

typedef struct
{
	float target_val;//目标值
	float actual_val;//实际值
    float actual_last;
	float err;//当前偏差
	float err_last;//上次偏差
	float err_sum;//误差累计值
	float Kp,Ki,Kd;//比例，积分，微分系数

    float output;
} tPid;

extern tPid angle_pid; 						 // 角度PID
extern uint8_t angle_control_enable;        // 角度控制使能
extern tPid tracing_pid;
extern tPid speedA_pid;
extern tPid speedB_pid;
extern float tracing_val;
extern float angle_basic_speed;
extern float tracing_basic_speed;


void PID_init(void);
void pid_set_angle_target(float target);
float PID_angle_realize(tPid * pid, float current_angle);
float PID_tracing_realize(tPid * pid, float line_actual);
void Tracing_Value_Get(void);
float PID_speed_realize(tPid * pid,uint8_t encoder);     //速度环
void pid_set_speed_target(float motorAspeed,float motorBspeed) ; //设置目标值
#endif
