# Question_Task_1 速度平滑过渡功能说明

## 功能概述

为Question_Task_1添加了速度平滑过渡功能，解决转弯结束后恢复循迹时速度突变的问题，实现平滑的速度渐变效果。

## 新增状态机逻辑

### 状态转换图
```
Q1_STATE_1 (正常循迹) → Q1_STATE_2 (转弯) → Q1_STATE_3 (速度平滑过渡) → Q1_STATE_1
     ↑                      ↓                    ↓                      ↓
  目标速度循迹          检测到全白线           检测到黑线              平滑过渡完成
  speed_basic=200       原地左转              开始速度渐变            恢复目标速度
```

### 状态详细说明

#### Q1_STATE_1 - 正常循迹状态
- **速度设置**: `speed_basic = q1_target_speed` (默认200)
- **主要功能**: 执行`Tracing_Control()`循迹控制
- **状态转换**: 检测到全白线 → Q1_STATE_2

#### Q1_STATE_2 - 转弯状态
- **转弯动作**: `pid_set_speed_target(0.0, 5.0)` (原地左转)
- **状态转换**: 检测到黑线 → Q1_STATE_3

#### Q1_STATE_3 - 速度平滑过渡状态 (新增)
- **起始速度**: `speed_basic = q1_min_speed` (默认80)
- **目标速度**: `q1_target_speed` (默认200)
- **过渡时间**: `q1_smooth_duration` (默认800ms)
- **速度计算**: 线性插值算法
- **主要功能**: 继续执行`Tracing_Control()`，同时平滑调整速度
- **状态转换**: 过渡时间结束 → Q1_STATE_1

## 核心算法

### 线性插值速度计算
```c
// 计算过渡进度 (0.0 ~ 1.0)
float progress = (float)elapsed_time / (float)q1_smooth_duration;

// 线性插值计算当前速度
uint16_t current_speed = q1_min_speed + (uint16_t)((q1_target_speed - q1_min_speed) * progress);

speed_basic = current_speed;
```

### 速度变化曲线
```
速度
 ↑
200 ┤                    ┌─────────────
     │                   │
150 ┤                  ╱
     │                ╱
100 ┤              ╱
     │            ╱
 80 ┤───────────╱
     │
  0 ┤─────────────────────────────────→ 时间
    转弯开始   检测到黑线   平滑过渡期   正常循迹
              (STATE_3开始)  (800ms)   (STATE_1)
```

## 参数配置

### 默认参数
```c
uint16_t q1_smooth_duration = 800;     // 平滑过渡持续时间(ms)
uint16_t q1_min_speed = 80;            // 起始最低速度
uint16_t q1_target_speed = 200;        // 目标循迹速度
```

### 参数设置函数
```c
void Q1_Set_Smooth_Params(uint16_t duration, uint16_t min_speed, uint16_t target_speed);
```

**参数说明**:
- `duration`: 平滑过渡持续时间 (200~2000ms)
- `min_speed`: 起始最低速度 (30~150)
- `target_speed`: 目标循迹速度 (100~400)

**参数限制**:
- 自动限幅保护，防止参数超出合理范围
- 确保 `min_speed < target_speed`

## 使用示例

### 基本使用
```c
int main(void) {
    // 系统初始化...
    
    // 设置Q1速度平滑参数 (可选)
    Q1_Set_Smooth_Params(600, 100, 250);  // 600ms过渡，100→250速度
    
    while(1) {
        Scheduler_Run();
        Key_Proc();
    }
}
```

### 不同场景的参数建议

#### 平滑舒适模式 (推荐)
```c
Q1_Set_Smooth_Params(800, 80, 200);   // 800ms，80→200
```
- 过渡时间较长，速度变化平缓
- 适合大部分场景，平衡性能和舒适性

#### 快速响应模式
```c
Q1_Set_Smooth_Params(400, 120, 250);  // 400ms，120→250
```
- 过渡时间较短，快速恢复高速
- 适合竞速场景

#### 精确控制模式
```c
Q1_Set_Smooth_Params(1000, 60, 180);  // 1000ms，60→180
```
- 过渡时间长，速度变化细腻
- 适合精确控制要求高的场景

### 调试建议

#### 通过按键动态调整 (示例)
```c
void Key_Proc() {
    // 原有按键逻辑...
    
    switch(Key_Down) {
        case 5:  // 假设有第5个按键
            // 切换不同的平滑参数预设
            static uint8_t preset = 0;
            preset = (preset + 1) % 3;
            
            switch(preset) {
                case 0: Q1_Set_Smooth_Params(800, 80, 200); break;   // 默认
                case 1: Q1_Set_Smooth_Params(400, 120, 250); break;  // 快速
                case 2: Q1_Set_Smooth_Params(1000, 60, 180); break;  // 精确
            }
            break;
    }
}
```

## 技术优势

### 1. 平滑性能提升
- **消除速度突变**: 避免转弯后的急加速
- **提高稳定性**: 减少因速度突变导致的控制不稳定
- **改善用户体验**: 更平滑的运动轨迹

### 2. 可调试性
- **参数可配置**: 支持运行时调整平滑参数
- **多种预设**: 适应不同场景需求
- **安全限制**: 自动参数限幅，防止异常设置

### 3. 系统兼容性
- **无侵入性**: 不影响原有状态机逻辑
- **向后兼容**: 保持原有接口不变
- **模块化设计**: 可独立启用/禁用

## 性能指标

- **响应时间**: < 10ms (状态切换)
- **速度精度**: ±5 (PWM单位)
- **过渡平滑度**: 线性渐变，无突变点
- **内存开销**: 仅增加4个uint16_t变量 (8字节)

## 故障排除

### 常见问题

#### 1. 速度过渡不明显
- **原因**: `min_speed`和`target_speed`差值太小
- **解决**: 增大速度差值，建议差值≥100

#### 2. 过渡时间过长/过短
- **原因**: `duration`参数设置不当
- **解决**: 根据实际需求调整，建议范围400~1200ms

#### 3. 转弯后仍有抖动
- **原因**: `min_speed`设置过高
- **解决**: 降低`min_speed`，建议60~100

### 调试技巧

1. **串口监控**: 在STATE_3中输出当前速度值
2. **LED指示**: 用LED显示当前状态
3. **参数记录**: 记录最佳参数组合

---

*本文档由米醋电子工作室技术团队编写*
