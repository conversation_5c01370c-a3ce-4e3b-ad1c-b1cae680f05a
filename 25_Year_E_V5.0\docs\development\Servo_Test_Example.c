/**
 * @file Servo_Test_Example.c
 * @brief 舵机角度控制测试示例代码
 * <AUTHOR>
 * @date 2025-01
 * 
 * 本文件提供了舵机角度控制函数的测试示例，展示如何使用新封装的角度控制接口
 */

#include "HeadFiles.h"

/**
 * @brief 舵机基本功能测试
 * @note 测试舵机的基本角度控制功能
 */
void Servo_BasicTest(void)
{
    printf("开始舵机基本功能测试...\r\n");
    
    // 测试中心位置
    printf("设置舵机到中心位置(0度)\r\n");
    Servo_SetCenter();
    delay_ms(1000);
    
    // 测试左转
    printf("左转15度\r\n");
    Servo_SetAngle(15.0f);
    delay_ms(1000);
    
    printf("左转30度\r\n");
    Servo_SetAngle(30.0f);
    delay_ms(1000);
    
    printf("左转45度(最大角度)\r\n");
    Servo_SetAngle(45.0f);
    delay_ms(1000);
    
    // 回中
    printf("回到中心位置\r\n");
    Servo_SetCenter();
    delay_ms(1000);
    
    // 测试右转
    printf("右转15度\r\n");
    Servo_SetAngle(-15.0f);
    delay_ms(1000);
    
    printf("右转30度\r\n");
    Servo_SetAngle(-30.0f);
    delay_ms(1000);
    
    printf("右转45度(最大角度)\r\n");
    Servo_SetAngle(-45.0f);
    delay_ms(1000);
    
    // 最终回中
    printf("最终回到中心位置\r\n");
    Servo_SetCenter();
    delay_ms(1000);
    
    printf("舵机基本功能测试完成!\r\n");
}

/**
 * @brief 舵机角度限幅测试
 * @note 测试角度超出范围时的保护功能
 */
void Servo_LimitTest(void)
{
    printf("开始舵机角度限幅测试...\r\n");
    
    // 测试超出正向最大角度
    printf("尝试设置角度为60度(超出最大值45度)\r\n");
    Servo_SetAngle(60.0f);  // 应该被限制为45度
    delay_ms(2000);
    
    // 测试超出负向最大角度
    printf("尝试设置角度为-60度(超出最小值-45度)\r\n");
    Servo_SetAngle(-60.0f); // 应该被限制为-45度
    delay_ms(2000);
    
    // 回中
    printf("回到中心位置\r\n");
    Servo_SetCenter();
    delay_ms(1000);
    
    printf("舵机角度限幅测试完成!\r\n");
}

/**
 * @brief 舵机精度测试
 * @note 测试小角度变化的控制精度
 */
void Servo_PrecisionTest(void)
{
    printf("开始舵机精度测试...\r\n");
    
    // 从中心位置开始，逐步增加角度
    for(float angle = 0.0f; angle <= 30.0f; angle += 5.0f)
    {
        printf("设置角度: %.1f度\r\n", angle);
        Servo_SetAngle(angle);
        delay_ms(500);
    }
    
    // 逐步减少角度到负值
    for(float angle = 30.0f; angle >= -30.0f; angle -= 5.0f)
    {
        printf("设置角度: %.1f度\r\n", angle);
        Servo_SetAngle(angle);
        delay_ms(500);
    }
    
    // 回到中心
    printf("回到中心位置\r\n");
    Servo_SetCenter();
    delay_ms(1000);
    
    printf("舵机精度测试完成!\r\n");
}

/**
 * @brief 舵机连续摆动测试
 * @note 测试舵机的连续工作能力
 */
void Servo_ContinuousTest(void)
{
    printf("开始舵机连续摆动测试(10次循环)...\r\n");
    
    for(int i = 0; i < 10; i++)
    {
        printf("第%d次循环\r\n", i + 1);
        
        // 左转
        Servo_SetAngle(30.0f);
        delay_ms(300);
        
        // 右转
        Servo_SetAngle(-30.0f);
        delay_ms(300);
    }
    
    // 最终回中
    Servo_SetCenter();
    printf("舵机连续摆动测试完成!\r\n");
}

/**
 * @brief 舵机与按键联动测试
 * @note 通过按键控制舵机角度
 */
void Servo_KeyControlTest(void)
{
    static uint8_t test_running = 1;
    
    if(!test_running) return;
    
    switch(Key_Down)
    {
        case 1:  // KEY1 - 左转15度
            printf("KEY1按下: 左转15度\r\n");
            Servo_SetAngle(15.0f);
            break;
            
        case 2:  // KEY2 - 右转15度
            printf("KEY2按下: 右转15度\r\n");
            Servo_SetAngle(-15.0f);
            break;
            
        case 3:  // KEY3 - 回中
            printf("KEY3按下: 回到中心位置\r\n");
            Servo_SetCenter();
            break;
            
        case 4:  // KEY4 - 停止测试
            printf("KEY4按下: 停止按键控制测试\r\n");
            Servo_SetCenter();
            test_running = 0;
            break;
    }
}

/**
 * @brief 舵机与陀螺仪联动测试
 * @note 根据陀螺仪角度控制舵机
 */
void Servo_GyroControlTest(void)
{
    // 获取当前偏航角
    float current_yaw = wit_data.relative_yaw;
    
    // 将偏航角映射到舵机角度 (简单比例控制)
    float servo_angle = current_yaw * 0.3f;  // 比例系数0.3
    
    // 设置舵机角度
    Servo_SetAngle(servo_angle);
    
    // 打印调试信息
    printf("陀螺仪角度: %.2f°, 舵机角度: %.2f°\r\n", current_yaw, servo_angle);
}

/**
 * @brief 舵机测试主函数
 * @note 可以在main函数中调用进行测试
 */
void Servo_TestMain(void)
{
    printf("=== 舵机角度控制测试程序 ===\r\n");
    printf("请按按键选择测试项目:\r\n");
    printf("KEY1: 基本功能测试\r\n");
    printf("KEY2: 角度限幅测试\r\n");
    printf("KEY3: 精度测试\r\n");
    printf("KEY4: 连续摆动测试\r\n");
    
    switch(Key_Down)
    {
        case 1:
            Servo_BasicTest();
            break;
            
        case 2:
            Servo_LimitTest();
            break;
            
        case 3:
            Servo_PrecisionTest();
            break;
            
        case 4:
            Servo_ContinuousTest();
            break;
    }
}

/**
 * @brief 延时函数 (毫秒)
 * @param ms 延时时间(毫秒)
 * @note 简单的延时实现，实际项目中建议使用系统定时器
 */
void delay_ms(uint32_t ms)
{
    uint32_t start_time = tick_ms;
    while((tick_ms - start_time) < ms)
    {
        // 等待
    }
}

/* 使用示例:
 * 
 * 在main函数中添加:
 * 
 * int main(void) {
 *     // 系统初始化
 *     SYSCFG_DL_init();
 *     SysTick_Init();
 *     Servo_init();
 *     
 *     // 运行测试
 *     Servo_BasicTest();
 *     
 *     while(1) {
 *         // 按键控制测试
 *         Key_Proc();
 *         Servo_KeyControlTest();
 *         
 *         // 或者陀螺仪控制测试
 *         // Servo_GyroControlTest();
 *     }
 * }
 */
