******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Jul 19 18:54:02 2025

OUTPUT FILE NAME:   <24_Year_H_V3.5.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005541


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006b40  000194c0  R  X
  SRAM                  20200000   00008000  00000694  0000796c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006b40   00006b40    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005ff0   00005ff0    r-x .text
  000060b0    000060b0    000009f0   000009f0    r-- .rodata
  00006aa0    00006aa0    000000a0   000000a0    r-- .cinit
20200000    20200000    00000497   00000000    rw-
  20200000    20200000    000002a1   00000000    rw- .bss
  202002a4    202002a4    000001f3   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005ff0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000003b4     app_question_task.o (.text.Question_Task_3)
                  00000e44    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  000011b0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000013d0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000015ac    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000173e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001740    00000190     app_question_task.o (.text.Question_Task_2)
                  000018d0    00000190     pid.o (.text.Yaw_error_zzk)
                  00001a60    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001be8    00000184     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001d6c    00000150     pid.o (.text.PID_angle_realize)
                  00001ebc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001ff8    0000012c     pid.o (.text.Tracing_Value_Get)
                  00002124    00000128     pid.o (.text.PID_speed_realize)
                  0000224c    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00002370    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002490    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  000025a8    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  000026b8    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  000027c8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000028d4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000029d8    00000104     pid.o (.text.PID_tracing_realize)
                  00002adc    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  00002bd2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002bd4    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  00002cc0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002da8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002e8c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002f68    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003040    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003118    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  000031e8    000000c4     motor.o (.text.motor_direction)
                  000032ac    000000ac     motor.o (.text.pwm_set)
                  00003358    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00003402    00000002     libc.a : _lock.c.obj (.text._nop)
                  00003404    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000034a6    00000002     --HOLE-- [fill = 0]
                  000034a8    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00003548    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000035e4    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  0000367c    00000090     pid.o (.text.PID_init)
                  0000370c    0000008c     key.o (.text.Key_Proc)
                  00003798    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003824    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000038b0    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003934    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000039b8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003a3a    00000002     --HOLE-- [fill = 0]
                  00003a3c    00000080     interrupt.o (.text.TIMG0_IRQHandler)
                  00003abc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003b38    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  00003bb0    00000074     encoder.o (.text.Encoder_Get)
                  00003c24    00000074     Scheduler.o (.text.Scheduler_Run)
                  00003c98    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003d0c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003d10    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003d84    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003df6    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00003e66    00000002     --HOLE-- [fill = 0]
                  00003e68    00000070     app_question_task.o (.text.Question_Task_1)
                  00003ed8    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003f44    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  00003fae    00000002     --HOLE-- [fill = 0]
                  00003fb0    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00004018    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00004080    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000040e6    00000002     --HOLE-- [fill = 0]
                  000040e8    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  0000414c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000041ae    00000002     --HOLE-- [fill = 0]
                  000041b0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00004212    00000002     --HOLE-- [fill = 0]
                  00004214    00000060     key.o (.text.Key_Read)
                  00004274    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000042d2    00000002     --HOLE-- [fill = 0]
                  000042d4    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004330    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  0000438c    0000005c     libc.a : printf.c.obj (.text.printf)
                  000043e8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00004440    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004498    00000058            : _printfi.c.obj (.text._pconv_f)
                  000044f0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004546    00000002     --HOLE-- [fill = 0]
                  00004548    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  0000459c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000045f0    00000054     main.o (.text.main)
                  00004644    00000054     usart_app.o (.text.uart_task)
                  00004698    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000046ea    00000002     --HOLE-- [fill = 0]
                  000046ec    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  0000473c    00000050     clock.o (.text.SysTick_Config)
                  0000478c    00000050     wit.o (.text.WIT_Init)
                  000047dc    00000050     usart_app.o (.text.fputs)
                  0000482c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004878    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000048c4    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004910    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000495a    00000002     --HOLE-- [fill = 0]
                  0000495c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000049a6    0000004a     adc_app.o (.text.adc_getValue)
                  000049f0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004a38    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004a80    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00004ac8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004b0c    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00004b50    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00004b92    00000002     --HOLE-- [fill = 0]
                  00004b94    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004bd4    00000040     app_tracing_control.o (.text.Tracing_Control)
                  00004c14    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004c54    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004c94    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004cd4    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004d10    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004d4c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004d88    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00004dc4    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  00004e00    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004e3c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004e78    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004eb4    0000003c     app_angle_control.o (.text.angele_control)
                  00004ef0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004f2c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004f66    00000002     --HOLE-- [fill = 0]
                  00004f68    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004fa2    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  00004fda    00000002     --HOLE-- [fill = 0]
                  00004fdc    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005014    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000504c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005084    00000034     beep.o (.text.Beep_Time_Control)
                  000050b8    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000050ec    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005120    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00005154    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00005184    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  000051b4    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  000051e4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005214    00000030     clock.o (.text.mspm0_delay_ms)
                  00005244    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00005274    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  000052a0    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  000052cc    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  000052f8    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00005324    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  00005350    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000537c    0000002c     pid.o (.text.fabs_zzk)
                  000053a8    0000002c     usart_app.o (.text.fputc)
                  000053d4    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00005400    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00005428    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005450    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00005478    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  000054a0    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  000054c8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000054f0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00005518    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005540    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005568    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  0000558e    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  000055b4    00000026     wit.o (.text.DL_DMA_enableChannel)
                  000055da    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005600    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005626    00000002     --HOLE-- [fill = 0]
                  00005628    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  0000564c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005670    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005694    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000056b8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000056da    00000002     --HOLE-- [fill = 0]
                  000056dc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000056fc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000571c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000573a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005758    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005776    00000002     --HOLE-- [fill = 0]
                  00005778    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00005794    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  000057b0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000057cc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000057e8    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00005804    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005820    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000583c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005858    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005874    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005890    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000058ac    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000058c8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000058e4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005900    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000591c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005938    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005950    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005968    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005980    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005998    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000059b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000059c8    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000059e0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  000059f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005a10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005a28    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005a40    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005a58    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005a70    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005a88    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005aa0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005ab8    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005ad0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005ae8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005b00    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005b18    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005b30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005b48    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005b60    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00005b78    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005b90    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005ba8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005bc0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005bd8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005bf0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005c08    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005c20    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005c38    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005c50    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00005c68    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005c80    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005c98    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005cb0    00000018     clock.o (.text.SysTick_Init)
                  00005cc8    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00005ce0    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00005cf6    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  00005d0c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005d22    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00005d38    00000016     key.o (.text.DL_GPIO_readPins)
                  00005d4e    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00005d64    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005d7a    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00005d90    00000016     encoder.o (.text.Encoder_Init)
                  00005da6    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005dbc    00000014     beep.o (.text.Beep_ms)
                  00005dd0    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00005de4    00000014     beep.o (.text.DL_GPIO_clearPins)
                  00005df8    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00005e0c    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00005e20    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005e34    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00005e48    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005e5c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005e70    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005e84    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005e98    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005eac    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005ec0    00000014     app_question_task.o (.text.State_Machine_init)
                  00005ed4    00000014     wit.o (.text.WIT_Calibrate_Yaw)
                  00005ee8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005efc    00000014     pid.o (.text.pid_set_angle_target)
                  00005f10    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005f24    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00005f36    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00005f48    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005f5a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005f6c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005f7e    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  00005f8e    00000002     --HOLE-- [fill = 0]
                  00005f90    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005fa0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005fb0    00000010     interrupt.o (.text.SysTick_Handler)
                  00005fc0    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005fd0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005fe0    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  00005fee    00000002     --HOLE-- [fill = 0]
                  00005ff0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005ffe    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000600c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000601a    00000002     --HOLE-- [fill = 0]
                  0000601c    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00006028    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00006034    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000603e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006048    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00006058    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006062    0000000a            : sprintf.c.obj (.text._outc)
                  0000606c    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006074    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000607c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006084    00000008     libc.a : printf.c.obj (.text._outc)
                  0000608c    00000008            : printf.c.obj (.text._outs)
                  00006094    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00006098    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000060a8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000060ac    00000004            : exit.c.obj (.text:abort)

.cinit     0    00006aa0    000000a0     
                  00006aa0    00000079     (.cinit..data.load) [load image, compression = lzss]
                  00006b19    00000003     --HOLE-- [fill = 0]
                  00006b1c    0000000c     (__TI_handler_table)
                  00006b28    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006b30    00000010     (__TI_cinit_table)

.rodata    0    000060b0    000009f0     
                  000060b0    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  000066a0    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  000068c8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000068d0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000069d1    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  000069d4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000069fc    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006a14    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006a28    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006a39    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00006a4a    00000010     encoder.o (.rodata.encoder_table)
                  00006a5a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006a69    00000001     --HOLE-- [fill = 0]
                  00006a6a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006a74    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00006a7e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006a80    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006a88    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  00006a8d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006a90    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006a92    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006a94    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000002a1     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    0000002c     (.common:wit_data)
                  2020017c    00000028     (.common:angle_pid)
                  202001a4    00000028     (.common:speedA_pid)
                  202001cc    00000028     (.common:speedB_pid)
                  202001f4    00000028     (.common:tracing_pid)
                  2020021c    00000021     (.common:wit_dmaBuffer)
                  2020023d    00000014     (.common:OLED_String)
                  20200251    00000001     (.common:Digtal)
                  20200252    00000001     (.common:Key_Down)
                  20200253    00000001     (.common:Key_Old)
                  20200254    00000014     (.common:State_Machine)
                  20200268    00000010     (.common:Normal)
                  20200278    00000008     (.common:grayscale_data)
                  20200280    00000004     pid.o (.bss.Yaw_error_zzk.error)
                  20200284    00000004     (.common:first_angle)
                  20200288    00000004     (.common:start_time)
                  2020028c    00000004     (.common:target_angle)
                  20200290    00000004     (.common:tick_ms)
                  20200294    00000004     (.common:tracing_val)
                  20200298    00000004     (.common:uart_rx_ticks)
                  2020029c    00000001     (.common:Key_Up)
                  2020029d    00000001     (.common:Key_Val)
                  2020029e    00000001     (.common:grayscale_count)
                  2020029f    00000001     (.common:task_num)
                  202002a0    00000001     (.common:uart_rx_index)

.data      0    202002a4    000001f3     UNINITIALIZED
                  202002a4    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200394    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200414    00000024     Scheduler.o (.data.scheduler_task)
                  20200438    00000010     Ganv_Grayscale.o (.data.Anolog)
                  20200448    00000010     Ganv_Grayscale.o (.data.black)
                  20200458    00000010     Ganv_Grayscale.o (.data.white)
                  20200468    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020046c    00000004            : _lock.c.obj (.data._lock)
                  20200470    00000004            : _lock.c.obj (.data._unlock)
                  20200474    00000004     pid.o (.data.angle_basic_speed)
                  20200478    00000004     beep.o (.data.bee_time)
                  2020047c    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200480    00000004     motor.o (.data.speed_basic)
                  20200484    00000004     pid.o (.data.tracing_basic_speed)
                  20200488    00000002     encoder.o (.data.encoder_A_count)
                  2020048a    00000002     encoder.o (.data.encoder_B_count)
                  2020048c    00000002     encoder.o (.data.encoder_count)
                  2020048e    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  2020048f    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200490    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200491    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200492    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200493    00000001     encoder.o (.data.encoder_count_flag)
                  20200494    00000001     app_question_task.o (.data.q1_first_flag)
                  20200495    00000001     app_question_task.o (.data.q2_first_flag)
                  20200496    00000001     app_question_task.o (.data.q3_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2988    132       160    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3168    324       160    
                                                               
    .\APP\
       app_question_task.o            1480    0         27     
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          64      0         0      
       app_angle_control.o            60      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1840    0         34     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         116     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         116     0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          258     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         258     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1282    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1610    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         4      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          1800    0         176    
    +--+------------------------------+-------+---------+---------+
       Total:                         1800    0         176    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          430     0         81     
    +--+------------------------------+-------+---------+---------+
       Total:                         430     0         81     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       157       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   24520   2880      1684   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006b30 records: 2, size/record: 8, table size: 16
	.data: load addr=00006aa0, load size=00000079 bytes, run addr=202002a4, run size=000001f3 bytes, compression=lzss
	.bss: load addr=00006b28, load size=00000008 bytes, run addr=20200000, run size=000002a1 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006b1c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000015ad     00006048     00006046   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005541     00006098     00006094   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003d0d  ADC0_IRQHandler                      
00003d0d  ADC1_IRQHandler                      
00003d0d  AES_IRQHandler                       
20200438  Anolog                               
00005085  Beep_Time_Control                    
00005dbd  Beep_ms                              
000060ac  C$$EXIT                              
00003d0d  CANFD0_IRQHandler                    
00003d0d  DAC0_IRQHandler                      
00004b95  DL_ADC12_setClockConfig              
00006035  DL_Common_delayCycles                
0000482d  DL_DMA_initChannel                   
00004275  DL_I2C_fillControllerTXFIFO          
00005601  DL_I2C_setClockConfig                
00002e8d  DL_SYSCTL_configSYSPLL               
00004ac9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000028d5  DL_Timer_initFourCCPWMMode           
00002cc1  DL_Timer_initTimerMode               
000058e5  DL_Timer_setCaptCompUpdateMethod     
00005bf1  DL_Timer_setCaptureCompareOutCtl     
00005fa1  DL_Timer_setCaptureCompareValue      
00005901  DL_Timer_setClockConfig              
00004549  DL_UART_drainRXFIFO                  
000049f1  DL_UART_init                         
00005f49  DL_UART_setClockConfig               
00003d0d  DMA_IRQHandler                       
00003d0d  Default_Handler                      
20200251  Digtal                               
00003bb1  Encoder_Get                          
00005d91  Encoder_Init                         
00003d0d  GROUP0_IRQHandler                    
0000224d  GROUP1_IRQHandler                    
00003119  Get_Analog_value                     
00004d89  Get_Anolog_Value                     
00005fe1  Get_Digtal_For_User                  
00004fa3  Get_Normalize_For_User               
00003d0d  HardFault_Handler                    
00003d0d  I2C0_IRQHandler                      
00003d0d  I2C1_IRQHandler                      
20200252  Key_Down                             
20200253  Key_Old                              
0000370d  Key_Proc                             
00004215  Key_Read                             
2020029c  Key_Up                               
2020029d  Key_Val                              
00003d0d  NMI_Handler                          
00001a61  No_MCU_Ganv_Sensor_Init              
00003d85  No_MCU_Ganv_Sensor_Init_Frist        
00004b51  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200268  Normal                               
00003f45  OLED_Clear                           
000025a9  OLED_Init                            
00004dc5  OLED_Set_Pos                         
00002491  OLED_ShowChar                        
00002add  OLED_ShowNum                         
00003df7  OLED_ShowString                      
2020023d  OLED_String                          
000035e5  OLED_WR_Byte                         
000026b9  Oled_Task                            
00001d6d  PID_angle_realize                    
0000367d  PID_init                             
00002125  PID_speed_realize                    
000029d9  PID_tracing_realize                  
00003d0d  PendSV_Handler                       
00003e69  Question_Task_1                      
00001741  Question_Task_2                      
00000a91  Question_Task_3                      
00003d0d  RTC_IRQHandler                       
00006095  Reset_Handler                        
00003d0d  SPI0_IRQHandler                      
00003d0d  SPI1_IRQHandler                      
00003d0d  SVC_Handler                          
000048c5  SYSCFG_DL_ADC1_init                  
00005c99  SYSCFG_DL_DMA_WIT_init               
0000606d  SYSCFG_DL_DMA_init                   
00005121  SYSCFG_DL_FOR_CONTROL_init           
00001be9  SYSCFG_DL_GPIO_init                  
000043e9  SYSCFG_DL_I2C_OLED_init              
00003799  SYSCFG_DL_PWM_MOTOR_init             
00004e01  SYSCFG_DL_SYSCTL_init                
0000459d  SYSCFG_DL_UART_0_init                
00003fb1  SYSCFG_DL_UART_WIT_init              
00004fdd  SYSCFG_DL_init                       
00003549  SYSCFG_DL_initPower                  
0000601d  Scheduler_Init                       
00003c25  Scheduler_Run                        
20200254  State_Machine                        
00005ec1  State_Machine_init                   
00005fb1  SysTick_Handler                      
00005cb1  SysTick_Init                         
00003d0d  TIMA0_IRQHandler                     
00003d0d  TIMA1_IRQHandler                     
00003a3d  TIMG0_IRQHandler                     
00003d0d  TIMG12_IRQHandler                    
00003d0d  TIMG6_IRQHandler                     
00003d0d  TIMG7_IRQHandler                     
00003d0d  TIMG8_IRQHandler                     
00005f5b  TI_memcpy_small                      
0000600d  TI_memset_small                      
00004bd5  Tracing_Control                      
00001ff9  Tracing_Value_Get                    
00004b0d  UART0_IRQHandler                     
00003d0d  UART1_IRQHandler                     
00000e45  UART2_IRQHandler                     
00003d0d  UART3_IRQHandler                     
00005ed5  WIT_Calibrate_Yaw                    
00003b39  WIT_Get_Relative_Yaw                 
0000478d  WIT_Init                             
000018d1  Yaw_error_zzk                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006b30  __TI_CINIT_Base                      
00006b40  __TI_CINIT_Limit                     
00006b40  __TI_CINIT_Warm                      
00006b1c  __TI_Handler_Table_Base              
00006b28  __TI_Handler_Table_Limit             
00004ef1  __TI_auto_init_nobinit_nopinit       
00003abd  __TI_decompress_lzss                 
00005f6d  __TI_decompress_none                 
00004441  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005da7  __TI_zero_init_nomemset              
000015b7  __adddf3                             
0000304b  __addsf3                             
000068d0  __aeabi_ctype_table_                 
000068d0  __aeabi_ctype_table_C                
00003d11  __aeabi_d2f                          
0000495d  __aeabi_d2iz                         
000015b7  __aeabi_dadd                         
0000414d  __aeabi_dcmpeq                       
00004189  __aeabi_dcmpge                       
0000419d  __aeabi_dcmpgt                       
00004175  __aeabi_dcmple                       
00004161  __aeabi_dcmplt                       
000027c9  __aeabi_ddiv                         
00002da9  __aeabi_dmul                         
000015ad  __aeabi_dsub                         
20200468  __aeabi_errno                        
00006075  __aeabi_errno_addr                   
00004c55  __aeabi_f2d                          
00005015  __aeabi_f2iz                         
0000304b  __aeabi_fadd                         
000041b1  __aeabi_fcmpeq                       
000041ed  __aeabi_fcmpge                       
00004201  __aeabi_fcmpgt                       
000041d9  __aeabi_fcmple                       
000041c5  __aeabi_fcmplt                       
000039b9  __aeabi_fdiv                         
00003825  __aeabi_fmul                         
00003041  __aeabi_fsub                         
00005351  __aeabi_i2d                          
00004e3d  __aeabi_i2f                          
000044f1  __aeabi_idiv                         
0000173f  __aeabi_idiv0                        
000044f1  __aeabi_idivmod                      
00002bd3  __aeabi_ldiv0                        
00005759  __aeabi_llsl                         
00005695  __aeabi_lmul                         
00006029  __aeabi_memclr                       
00006029  __aeabi_memclr4                      
00006029  __aeabi_memclr8                      
0000607d  __aeabi_memcpy                       
0000607d  __aeabi_memcpy4                      
0000607d  __aeabi_memcpy8                      
00005ff1  __aeabi_memset                       
00005ff1  __aeabi_memset4                      
00005ff1  __aeabi_memset8                      
00005671  __aeabi_ui2d                         
00004c15  __aeabi_uidiv                        
00004c15  __aeabi_uidivmod                     
00005ee9  __aeabi_uldivmod                     
00005759  __ashldi3                            
ffffffff  __binit__                            
00004019  __cmpdf2                             
00004f2d  __cmpsf2                             
000027c9  __divdf3                             
000039b9  __divsf3                             
00004019  __eqdf2                              
00004f2d  __eqsf2                              
00004c55  __extendsfdf2                        
0000495d  __fixdfsi                            
00005015  __fixsfsi                            
00005351  __floatsidf                          
00004e3d  __floatsisf                          
00005671  __floatunsidf                        
00003c99  __gedf2                              
00004e79  __gesf2                              
00003c99  __gtdf2                              
00004e79  __gtsf2                              
00004019  __ledf2                              
00004f2d  __lesf2                              
00004019  __ltdf2                              
00004f2d  __ltsf2                              
UNDEFED   __mpu_init                           
00002da9  __muldf3                             
00005695  __muldi3                             
00004f69  __muldsi3                            
00003825  __mulsf3                             
00004019  __nedf2                              
00004f2d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000015ad  __subdf3                             
00003041  __subsf3                             
00003d11  __truncdfsf2                         
00003405  __udivmoddi4                         
00005541  _c_int00_noargs                      
202002a4  _ftable                              
2020046c  _lock                                
00003403  _nop                                 
UNDEFED   _system_post_cinit                   
000060a9  _system_pre_init                     
20200470  _unlock                              
000060ad  abort                                
000049a7  adc_getValue                         
00004eb5  angele_control                       
20200474  angle_basic_speed                    
2020017c  angle_pid                            
000066a0  asc2_0806                            
000060b0  asc2_1608                            
00004c95  atoi                                 
20200478  bee_time                             
ffffffff  binit                                
20200448  black                                
00003ed9  convertAnalogToDigital               
00005fc1  delay_ms                             
00002bd5  detect_trace_state_change            
20200488  encoder_A_count                      
2020048a  encoder_B_count                      
2020048c  encoder_count                        
20200493  encoder_count_flag                   
0000537d  fabs_zzk                             
20200284  first_angle                          
000053a9  fputc                                
000047dd  fputs                                
000042d5  frexp                                
000042d5  frexpl                               
202000b0  gPWM_MOTORBackup                     
00004a39  gray_init                            
000034a9  gray_task                            
2020029e  grayscale_count                      
20200278  grayscale_data                       
00000000  interruptVectors                     
00002f69  ldexp                                
00002f69  ldexpl                               
000045f1  main                                 
000056b9  memccpy                              
000031e9  motor_direction                      
00005215  mspm0_delay_ms                       
000053d5  mspm0_get_clock_ms                   
00003359  normalizeAnalogValues                
000040e9  oled_i2c_sda_unlock                  
00005245  oled_pow                             
00005efd  pid_set_angle_target                 
0000438d  printf                               
000032ad  pwm_set                              
20200494  q1_first_flag                        
20200495  q2_first_flag                        
20200496  q3_first_flag                        
00002f69  scalbn                               
00002f69  scalbnl                              
20200000  sensor                               
202001a4  speedA_pid                           
202001cc  speedB_pid                           
20200480  speed_basic                          
0000504d  sprintf                              
20200288  start_time                           
2020028c  target_angle                         
2020029f  task_num                             
20200290  tick_ms                              
20200484  tracing_basic_speed                  
202001f4  tracing_pid                          
20200294  tracing_val                          
20200394  uart_rx_buffer                       
202002a0  uart_rx_index                        
20200298  uart_rx_ticks                        
00004645  uart_task                            
00005fd1  wcslen                               
20200458  white                                
20200150  wit_data                             
2020021c  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Question_Task_3                      
00000e45  UART2_IRQHandler                     
000015ad  __aeabi_dsub                         
000015ad  __subdf3                             
000015b7  __adddf3                             
000015b7  __aeabi_dadd                         
0000173f  __aeabi_idiv0                        
00001741  Question_Task_2                      
000018d1  Yaw_error_zzk                        
00001a61  No_MCU_Ganv_Sensor_Init              
00001be9  SYSCFG_DL_GPIO_init                  
00001d6d  PID_angle_realize                    
00001ff9  Tracing_Value_Get                    
00002125  PID_speed_realize                    
0000224d  GROUP1_IRQHandler                    
00002491  OLED_ShowChar                        
000025a9  OLED_Init                            
000026b9  Oled_Task                            
000027c9  __aeabi_ddiv                         
000027c9  __divdf3                             
000028d5  DL_Timer_initFourCCPWMMode           
000029d9  PID_tracing_realize                  
00002add  OLED_ShowNum                         
00002bd3  __aeabi_ldiv0                        
00002bd5  detect_trace_state_change            
00002cc1  DL_Timer_initTimerMode               
00002da9  __aeabi_dmul                         
00002da9  __muldf3                             
00002e8d  DL_SYSCTL_configSYSPLL               
00002f69  ldexp                                
00002f69  ldexpl                               
00002f69  scalbn                               
00002f69  scalbnl                              
00003041  __aeabi_fsub                         
00003041  __subsf3                             
0000304b  __addsf3                             
0000304b  __aeabi_fadd                         
00003119  Get_Analog_value                     
000031e9  motor_direction                      
000032ad  pwm_set                              
00003359  normalizeAnalogValues                
00003403  _nop                                 
00003405  __udivmoddi4                         
000034a9  gray_task                            
00003549  SYSCFG_DL_initPower                  
000035e5  OLED_WR_Byte                         
0000367d  PID_init                             
0000370d  Key_Proc                             
00003799  SYSCFG_DL_PWM_MOTOR_init             
00003825  __aeabi_fmul                         
00003825  __mulsf3                             
000039b9  __aeabi_fdiv                         
000039b9  __divsf3                             
00003a3d  TIMG0_IRQHandler                     
00003abd  __TI_decompress_lzss                 
00003b39  WIT_Get_Relative_Yaw                 
00003bb1  Encoder_Get                          
00003c25  Scheduler_Run                        
00003c99  __gedf2                              
00003c99  __gtdf2                              
00003d0d  ADC0_IRQHandler                      
00003d0d  ADC1_IRQHandler                      
00003d0d  AES_IRQHandler                       
00003d0d  CANFD0_IRQHandler                    
00003d0d  DAC0_IRQHandler                      
00003d0d  DMA_IRQHandler                       
00003d0d  Default_Handler                      
00003d0d  GROUP0_IRQHandler                    
00003d0d  HardFault_Handler                    
00003d0d  I2C0_IRQHandler                      
00003d0d  I2C1_IRQHandler                      
00003d0d  NMI_Handler                          
00003d0d  PendSV_Handler                       
00003d0d  RTC_IRQHandler                       
00003d0d  SPI0_IRQHandler                      
00003d0d  SPI1_IRQHandler                      
00003d0d  SVC_Handler                          
00003d0d  TIMA0_IRQHandler                     
00003d0d  TIMA1_IRQHandler                     
00003d0d  TIMG12_IRQHandler                    
00003d0d  TIMG6_IRQHandler                     
00003d0d  TIMG7_IRQHandler                     
00003d0d  TIMG8_IRQHandler                     
00003d0d  UART1_IRQHandler                     
00003d0d  UART3_IRQHandler                     
00003d11  __aeabi_d2f                          
00003d11  __truncdfsf2                         
00003d85  No_MCU_Ganv_Sensor_Init_Frist        
00003df7  OLED_ShowString                      
00003e69  Question_Task_1                      
00003ed9  convertAnalogToDigital               
00003f45  OLED_Clear                           
00003fb1  SYSCFG_DL_UART_WIT_init              
00004019  __cmpdf2                             
00004019  __eqdf2                              
00004019  __ledf2                              
00004019  __ltdf2                              
00004019  __nedf2                              
000040e9  oled_i2c_sda_unlock                  
0000414d  __aeabi_dcmpeq                       
00004161  __aeabi_dcmplt                       
00004175  __aeabi_dcmple                       
00004189  __aeabi_dcmpge                       
0000419d  __aeabi_dcmpgt                       
000041b1  __aeabi_fcmpeq                       
000041c5  __aeabi_fcmplt                       
000041d9  __aeabi_fcmple                       
000041ed  __aeabi_fcmpge                       
00004201  __aeabi_fcmpgt                       
00004215  Key_Read                             
00004275  DL_I2C_fillControllerTXFIFO          
000042d5  frexp                                
000042d5  frexpl                               
0000438d  printf                               
000043e9  SYSCFG_DL_I2C_OLED_init              
00004441  __TI_ltoa                            
000044f1  __aeabi_idiv                         
000044f1  __aeabi_idivmod                      
00004549  DL_UART_drainRXFIFO                  
0000459d  SYSCFG_DL_UART_0_init                
000045f1  main                                 
00004645  uart_task                            
0000478d  WIT_Init                             
000047dd  fputs                                
0000482d  DL_DMA_initChannel                   
000048c5  SYSCFG_DL_ADC1_init                  
0000495d  __aeabi_d2iz                         
0000495d  __fixdfsi                            
000049a7  adc_getValue                         
000049f1  DL_UART_init                         
00004a39  gray_init                            
00004ac9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004b0d  UART0_IRQHandler                     
00004b51  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004b95  DL_ADC12_setClockConfig              
00004bd5  Tracing_Control                      
00004c15  __aeabi_uidiv                        
00004c15  __aeabi_uidivmod                     
00004c55  __aeabi_f2d                          
00004c55  __extendsfdf2                        
00004c95  atoi                                 
00004d89  Get_Anolog_Value                     
00004dc5  OLED_Set_Pos                         
00004e01  SYSCFG_DL_SYSCTL_init                
00004e3d  __aeabi_i2f                          
00004e3d  __floatsisf                          
00004e79  __gesf2                              
00004e79  __gtsf2                              
00004eb5  angele_control                       
00004ef1  __TI_auto_init_nobinit_nopinit       
00004f2d  __cmpsf2                             
00004f2d  __eqsf2                              
00004f2d  __lesf2                              
00004f2d  __ltsf2                              
00004f2d  __nesf2                              
00004f69  __muldsi3                            
00004fa3  Get_Normalize_For_User               
00004fdd  SYSCFG_DL_init                       
00005015  __aeabi_f2iz                         
00005015  __fixsfsi                            
0000504d  sprintf                              
00005085  Beep_Time_Control                    
00005121  SYSCFG_DL_FOR_CONTROL_init           
00005215  mspm0_delay_ms                       
00005245  oled_pow                             
00005351  __aeabi_i2d                          
00005351  __floatsidf                          
0000537d  fabs_zzk                             
000053a9  fputc                                
000053d5  mspm0_get_clock_ms                   
00005541  _c_int00_noargs                      
00005601  DL_I2C_setClockConfig                
00005671  __aeabi_ui2d                         
00005671  __floatunsidf                        
00005695  __aeabi_lmul                         
00005695  __muldi3                             
000056b9  memccpy                              
00005759  __aeabi_llsl                         
00005759  __ashldi3                            
000058e5  DL_Timer_setCaptCompUpdateMethod     
00005901  DL_Timer_setClockConfig              
00005bf1  DL_Timer_setCaptureCompareOutCtl     
00005c99  SYSCFG_DL_DMA_WIT_init               
00005cb1  SysTick_Init                         
00005d91  Encoder_Init                         
00005da7  __TI_zero_init_nomemset              
00005dbd  Beep_ms                              
00005ec1  State_Machine_init                   
00005ed5  WIT_Calibrate_Yaw                    
00005ee9  __aeabi_uldivmod                     
00005efd  pid_set_angle_target                 
00005f49  DL_UART_setClockConfig               
00005f5b  TI_memcpy_small                      
00005f6d  __TI_decompress_none                 
00005fa1  DL_Timer_setCaptureCompareValue      
00005fb1  SysTick_Handler                      
00005fc1  delay_ms                             
00005fd1  wcslen                               
00005fe1  Get_Digtal_For_User                  
00005ff1  __aeabi_memset                       
00005ff1  __aeabi_memset4                      
00005ff1  __aeabi_memset8                      
0000600d  TI_memset_small                      
0000601d  Scheduler_Init                       
00006029  __aeabi_memclr                       
00006029  __aeabi_memclr4                      
00006029  __aeabi_memclr8                      
00006035  DL_Common_delayCycles                
0000606d  SYSCFG_DL_DMA_init                   
00006075  __aeabi_errno_addr                   
0000607d  __aeabi_memcpy                       
0000607d  __aeabi_memcpy4                      
0000607d  __aeabi_memcpy8                      
00006095  Reset_Handler                        
000060a9  _system_pre_init                     
000060ac  C$$EXIT                              
000060ad  abort                                
000060b0  asc2_1608                            
000066a0  asc2_0806                            
000068d0  __aeabi_ctype_table_                 
000068d0  __aeabi_ctype_table_C                
00006b1c  __TI_Handler_Table_Base              
00006b28  __TI_Handler_Table_Limit             
00006b30  __TI_CINIT_Base                      
00006b40  __TI_CINIT_Limit                     
00006b40  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  wit_data                             
2020017c  angle_pid                            
202001a4  speedA_pid                           
202001cc  speedB_pid                           
202001f4  tracing_pid                          
2020021c  wit_dmaBuffer                        
2020023d  OLED_String                          
20200251  Digtal                               
20200252  Key_Down                             
20200253  Key_Old                              
20200254  State_Machine                        
20200268  Normal                               
20200278  grayscale_data                       
20200284  first_angle                          
20200288  start_time                           
2020028c  target_angle                         
20200290  tick_ms                              
20200294  tracing_val                          
20200298  uart_rx_ticks                        
2020029c  Key_Up                               
2020029d  Key_Val                              
2020029e  grayscale_count                      
2020029f  task_num                             
202002a0  uart_rx_index                        
202002a4  _ftable                              
20200394  uart_rx_buffer                       
20200438  Anolog                               
20200448  black                                
20200458  white                                
20200468  __aeabi_errno                        
2020046c  _lock                                
20200470  _unlock                              
20200474  angle_basic_speed                    
20200478  bee_time                             
20200480  speed_basic                          
20200484  tracing_basic_speed                  
20200488  encoder_A_count                      
2020048a  encoder_B_count                      
2020048c  encoder_count                        
20200493  encoder_count_flag                   
20200494  q1_first_flag                        
20200495  q2_first_flag                        
20200496  q3_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[297 symbols]
