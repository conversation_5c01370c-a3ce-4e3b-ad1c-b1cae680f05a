#include "app_tracing_check.h"

TraceState_t detect_trace_state_change(uint8_t reset_request)
{
    static TraceState_t current_state = TRACE_STATE_HAS_BLACK;  // 默认初始状态为全黑线
    static TraceState_t last_detected_state = TRACE_STATE_HAS_BLACK;
    static uint32_t state_change_start_time = 0;
    static uint8_t state_confirmed = 1;  // 初始状态已确认

    // 如果请求重置
    if(reset_request) {
        current_state = TRACE_STATE_HAS_BLACK;
        last_detected_state = TRACE_STATE_HAS_BLACK;
        state_change_start_time = tick_ms;  // 重置为当前时间
        state_confirmed = 1;
        return current_state;  // 直接返回重置后的状态
    }
    
    //首先，检测当前瞬时状态
    TraceState_t instant_state;
    uint8_t has_black = 0;
    for(uint8_t i = 0; i < 8; i++) 
    {
        if(grayscale_data[i] == 1) 
        {  // 检测到黑线
            has_black = 1;
            break;  // 只要有一个检测到就够了
        }
    }
    instant_state = has_black ? TRACE_STATE_HAS_BLACK : TRACE_STATE_WHITE_LINE; // 判断瞬时状态

    // 步骤2：状态变化检测与50ms确认
    if(instant_state != last_detected_state) 
    {
        // 状态发生变化，记录变化开始时间
        last_detected_state = instant_state;
        state_change_start_time = tick_ms;  // 您的滴答定时器
        state_confirmed = 0;  // 重置确认标志
    }

    // 检查新状态是否持续200ms以上
    if(!state_confirmed && (tick_ms - state_change_start_time >= 100)) 
    {
        // 新状态持续超过50ms，确认状态切换
        current_state = last_detected_state;
        state_confirmed = 1;
    }

    return current_state;
}


