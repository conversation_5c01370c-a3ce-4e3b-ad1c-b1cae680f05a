# 25_Year_E_V5.0 API参考文档

## 核心API接口

### 1. 状态机管理 API

#### State_Machine_init()
```c
void State_Machine_init(void);
```
**功能**: 初始化状态机，将所有状态设置为STOP_STATE  
**参数**: 无  
**返回值**: 无  
**使用示例**:
```c
State_Machine_init();  // 系统启动时调用
```

#### Question_Task_X()
```c
void Question_Task_1(void);
void Question_Task_2(void);
void Question_Task_3(void);
void Question_Task_4(void);
```
**功能**: 执行对应题目的任务逻辑  
**调用时机**: 在10ms控制中断中根据Main_State调用  
**状态依赖**: 需要State_Machine.Main_State设置为对应的QUESTION_X

### 2. PID控制 API

#### PID_init()
```c
void PID_init(void);
```
**功能**: 初始化所有PID控制器参数  
**参数配置**:
- angle_pid: Kp=0.35, Ki=0.0, Kd=0.6
- tracing_pid: Kp=2.0, Ki=0.0, Kd=0
- speedA_pid/speedB_pid: Kp=15.0, Ki=3.0, Kd=0.0

#### pid_set_angle_target()
```c
void pid_set_angle_target(float target);
```
**功能**: 设置角度控制目标值  
**参数**: target - 目标角度 (-180° ~ +180°)  
**使用示例**:
```c
pid_set_angle_target(90.0);  // 设置目标角度为90度
```

#### PID_angle_realize()
```c
float PID_angle_realize(tPid * pid, float current_angle);
```
**功能**: 执行角度PID控制计算  
**参数**: 
- pid - 角度PID控制器指针
- current_angle - 当前角度值
**返回值**: PID控制输出 (-30.0 ~ +30.0)

#### PID_tracing_realize()
```c
float PID_tracing_realize(tPid * pid, float line_actual);
```
**功能**: 执行循迹PID控制计算  
**参数**:
- pid - 循迹PID控制器指针  
- line_actual - 当前线位置值
**返回值**: PID控制输出 (-30.0 ~ +30.0)

#### PID_speed_realize()
```c
float PID_speed_realize(tPid * pid, uint8_t encoder);
```
**功能**: 执行速度PID控制计算  
**参数**:
- pid - 速度PID控制器指针
- encoder - 编码器编号 (1=A轮, 2=B轮)
**返回值**: PID控制输出 (-900.0 ~ +900.0)

### 3. 电机控制 API

#### pwm_set()
```c
void pwm_set(float motorA, float motorB);
```
**功能**: 设置双电机PWM输出  
**参数**:
- motorA - A电机PWM值 (-100 ~ +100)
- motorB - B电机PWM值 (-100 ~ +100)
**说明**: 正值正转，负值反转，自动处理方向控制

#### motor_direction()
```c
void motor_direction(uint8_t motor, uint8_t direction);
```
**功能**: 设置电机转向  
**参数**:
- motor - 电机编号 (1=A电机, 2=B电机)
- direction - 方向 (1=正转, 2=反转)

### 4. 编码器 API

#### Encoder_Init()
```c
void Encoder_Init(void);
```
**功能**: 初始化编码器中断和GPIO配置  
**说明**: 配置双编码器的外部中断

#### Encoder_Get()
```c
int16_t Encoder_Get(uint8_t which_encoder);
```
**功能**: 获取编码器计数值并清零  
**参数**: which_encoder - 编码器编号 (1=A轮, 2=B轮)  
**返回值**: 自上次调用以来的编码器增量  
**说明**: B轮返回值自动取反以匹配机械安装

### 5. 灰度传感器 API

#### gray_init()
```c
void gray_init(void);
```
**功能**: 初始化灰度传感器系统

#### gray_task()
```c
void gray_task(void);
```
**功能**: 灰度传感器数据采集任务  
**调用周期**: 50ms (由调度器管理)

#### Tracing_Value_Get()
```c
void Tracing_Value_Get(void);
```
**功能**: 计算循迹控制值  
**输出**: 更新全局变量tracing_val  
**算法**: 8路传感器加权平均，范围 -4.0 ~ +4.0

### 6. 状态检测 API

#### detect_trace_state_change()
```c
TraceState_t detect_trace_state_change(uint8_t reset_request);
```
**功能**: 检测循迹状态变化  
**参数**: reset_request - 是否重置状态 (1=重置, 0=正常检测)  
**返回值**: 
- TRACE_STATE_WHITE_LINE - 检测到白线
- TRACE_STATE_HAS_BLACK - 检测到黑线
**特点**: 50ms确认机制，避免误判

### 7. 陀螺仪 API

#### WIT_Init()
```c
void WIT_Init(void);
```
**功能**: 初始化WIT陀螺仪通信

#### WIT_Get_Relative_Yaw()
```c
void WIT_Get_Relative_Yaw(void);
```
**功能**: 计算相对偏航角  
**输出**: 更新wit_data.relative_yaw

#### WIT_Calibrate_Yaw()
```c
void WIT_Calibrate_Yaw(void);
```
**功能**: 校准偏航角零点

### 8. 任务调度 API

#### Scheduler_Init()
```c
void Scheduler_Init(void);
```
**功能**: 初始化任务调度器

#### Scheduler_Run()
```c
void Scheduler_Run(void);
```
**功能**: 运行任务调度器  
**调用位置**: 主循环中持续调用  
**调度任务**:
- uart_task (10ms)
- gray_task (50ms)  
- Oled_Task (100ms)

### 9. 按键处理 API

#### Key_Read()
```c
uint8_t Key_Read(void);
```
**功能**: 读取按键状态  
**返回值**: 1-4对应KEY1-KEY4，0表示无按键

#### Key_Proc()
```c
void Key_Proc(void);
```
**功能**: 按键处理，包含按键去抖和状态切换  
**功能映射**:
- KEY1 → QUESTION_1
- KEY2 → QUESTION_2  
- KEY3 → QUESTION_3
- KEY4 → QUESTION_4

### 10. 控制应用 API

#### angele_control()
```c
void angele_control(void);
```
**功能**: 执行角度控制  
**流程**: 角度PID计算 → 速度目标设定 → 速度PID → PWM输出

#### Tracing_Control()
```c
void Tracing_Control(void);
```
**功能**: 执行循迹控制  
**流程**: 线位置计算 → 循迹PID → 速度目标设定 → 速度PID → PWM输出

## 数据结构

### tPid结构体
```c
typedef struct {
    float target_val;    // 目标值
    float actual_val;    // 实际值
    float actual_last;   // 上次实际值
    float err;           // 当前误差
    float err_last;      // 上次误差
    float err_sum;       // 误差累积
    float Kp, Ki, Kd;    // PID参数
    float output;        // 输出值
} tPid;
```

### state_machine结构体
```c
struct state_machine {
    int Main_State;      // 主状态
    int Q1_State;        // 题目1子状态
    int Q2_State;        // 题目2子状态
    int Q3_State;        // 题目3子状态
    int Q4_State;        // 题目4子状态
};
```

### WIT_Data_t结构体
```c
typedef struct {
    float pitch, roll, yaw;      // 姿态角
    float temperature;           // 温度
    int16_t ax, ay, az;         // 加速度
    int16_t gx, gy, gz;         // 角速度
    float yaw_offset;           // 偏航角偏移
    float relative_yaw;         // 相对偏航角
    uint8_t is_calibrated;      // 校准标志
} WIT_Data_t;
```

## 全局变量

### 控制相关
- `State_Machine` - 状态机实例
- `angle_pid, tracing_pid, speedA_pid, speedB_pid` - PID控制器
- `wit_data` - 陀螺仪数据
- `grayscale_data[8]` - 灰度传感器数据
- `tracing_val` - 循迹控制值

### 配置参数
- `speed_basic` - 基础速度 (默认200)
- `angle_basic_speed` - 角度控制基础速度 (默认20.0)
- `tracing_basic_speed` - 循迹控制基础速度 (默认20.0)

## 使用示例

### 基本初始化流程
```c
int main(void) {
    SYSCFG_DL_init();
    SysTick_Init();
    PID_init();
    Scheduler_Init();
    State_Machine_init();
    OLED_Init();
    gray_init();
    Encoder_Init();
    Servo_init();
    
    // 使能中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(FOR_CONTROL_INST_INT_IRQN);
    
    while(1) {
        Scheduler_Run();
        Key_Proc();
    }
}
```

### 角度控制示例
```c
// 设置目标角度
pid_set_angle_target(90.0);

// 在控制循环中执行角度控制
angele_control();
```

### 循迹控制示例
```c
// 在控制循环中执行循迹控制
Tracing_Control();

// 检测状态变化
TraceState_t state = detect_trace_state_change(0);
if(state == TRACE_STATE_HAS_BLACK) {
    // 检测到黑线，执行相应动作
}
```
