#include "app_tracing_control.h"

void servo_tracing()
{
    Servo_SetAngle(tracing_val);
}

void Tracing_Control()
{

    //Tracing_Value_Get();
    //PID_tracing_realize(&tracing_pid,tracing_val);
    //pwm_set(PID_speed_realize(&speedA_pid,1),PID_speed_realize(&speedB_pid,2));
    Tracing_Value_Get();
    servo_tracing();
   // pwm_set(200, 200);
}
void speed_control() //interrupt 10ms 一直刷新 调用改变目标值即可(速度环)
{
   //print("%.3f,%.3f,%.3f\r\n",speedA_pid.target_val,speedA_pid.actual_val,speedA_pid.output);
   pwm_set(PID_speed_realize(&speedA_pid,1),PID_speed_realize(&speedB_pid,2));
}
 
