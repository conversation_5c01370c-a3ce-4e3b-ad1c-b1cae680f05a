******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Jul 19 20:48:31 2025

OUTPUT FILE NAME:   <24_Year_H_V4.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005a0d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00007010  00018ff0  R  X
  SRAM                  20200000   00008000  00000697  00007969  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007010   00007010    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000064c0   000064c0    r-x .text
  00006580    00006580    000009f0   000009f0    r-- .rodata
  00006f70    00006f70    000000a0   000000a0    r-- .cinit
20200000    20200000    0000049a   00000000    rw-
  20200000    20200000    000002a1   00000000    rw- .bss
  202002a4    202002a4    000001f6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000064c0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000458     app_question_task.o (.text.Question_Task_4)
                  00000ee8    000003cc     app_question_task.o (.text.Question_Task_3)
                  000012b4    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00001620    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001840    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001a1c    000001ac     app_question_task.o (.text.Question_Task_2)
                  00001bc8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001d5a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001d5c    00000190     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001eec    00000190     pid.o (.text.Yaw_error_zzk)
                  0000207c    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002204    00000150     pid.o (.text.PID_angle_realize)
                  00002354    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002490    0000012c     pid.o (.text.Tracing_Value_Get)
                  000025bc    00000128     pid.o (.text.PID_speed_realize)
                  000026e4    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00002808    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002928    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00002a40    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00002b50    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  00002c60    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002d6c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002e70    00000104     pid.o (.text.PID_tracing_realize)
                  00002f74    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  0000306a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000306c    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  00003158    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00003240    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003324    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003400    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000034d8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000035b0    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00003680    000000c4     motor.o (.text.motor_direction)
                  00003744    000000ac     motor.o (.text.pwm_set)
                  000037f0    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  0000389a    00000002     libc.a : _lock.c.obj (.text._nop)
                  0000389c    000000a8     key.o (.text.Key_Proc)
                  00003944    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000039e6    00000002     --HOLE-- [fill = 0]
                  000039e8    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00003a88    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003b24    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00003bbc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003c48    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003cd4    00000088     pid.o (.text.PID_init)
                  00003d5c    00000084     interrupt.o (.text.TIMG0_IRQHandler)
                  00003de0    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003e64    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003ee8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003f6a    00000002     --HOLE-- [fill = 0]
                  00003f6c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003fe8    00000078     key.o (.text.Key_Read)
                  00004060    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  000040d8    00000074     encoder.o (.text.Encoder_Get)
                  0000414c    00000074     Scheduler.o (.text.Scheduler_Run)
                  000041c0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004234    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00004240    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000042b4    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004326    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00004396    00000002     --HOLE-- [fill = 0]
                  00004398    00000070     app_question_task.o (.text.Question_Task_1)
                  00004408    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00004474    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  000044de    00000002     --HOLE-- [fill = 0]
                  000044e0    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  00004548    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000045b0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004616    00000002     --HOLE-- [fill = 0]
                  00004618    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  0000467c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000046de    00000002     --HOLE-- [fill = 0]
                  000046e0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00004742    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000047a0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000047fc    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00004858    0000005c     libc.a : printf.c.obj (.text.printf)
                  000048b4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000490c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004964    00000058            : _printfi.c.obj (.text._pconv_f)
                  000049bc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004a12    00000002     --HOLE-- [fill = 0]
                  00004a14    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00004a68    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004abc    00000054     main.o (.text.main)
                  00004b10    00000054     usart_app.o (.text.uart_task)
                  00004b64    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004bb6    00000002     --HOLE-- [fill = 0]
                  00004bb8    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00004c08    00000050     clock.o (.text.SysTick_Config)
                  00004c58    00000050     wit.o (.text.WIT_Init)
                  00004ca8    00000050     usart_app.o (.text.fputs)
                  00004cf8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004d44    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004d90    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004ddc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004e26    00000002     --HOLE-- [fill = 0]
                  00004e28    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004e72    0000004a     adc_app.o (.text.adc_getValue)
                  00004ebc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004f04    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004f4c    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00004f94    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004fd8    00000044     usart_app.o (.text.UART0_IRQHandler)
                  0000501c    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000505e    00000002     --HOLE-- [fill = 0]
                  00005060    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000050a0    00000040     app_tracing_control.o (.text.Tracing_Control)
                  000050e0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005120    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005160    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000051a0    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000051dc    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00005218    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00005254    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00005290    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000052cc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005308    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00005344    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00005380    0000003c     app_angle_control.o (.text.angele_control)
                  000053bc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000053f8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00005432    00000002     --HOLE-- [fill = 0]
                  00005434    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000546e    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000054a6    00000002     --HOLE-- [fill = 0]
                  000054a8    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000054e0    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005518    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005550    00000034     beep.o (.text.Beep_Time_Control)
                  00005584    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000055b8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000055ec    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00005620    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00005650    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00005680    00000030     wit.o (.text.DL_DMA_setTransferSize)
                  000056b0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000056e0    00000030     clock.o (.text.mspm0_delay_ms)
                  00005710    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00005740    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  0000576c    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00005798    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  000057c4    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  000057f0    0000002c     wit.o (.text.__NVIC_EnableIRQ)
                  0000581c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00005848    0000002c     pid.o (.text.fabs_zzk)
                  00005874    0000002c     usart_app.o (.text.fputc)
                  000058a0    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  000058cc    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  000058f4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000591c    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00005944    00000028     wit.o (.text.DL_DMA_setDestAddr)
                  0000596c    00000028     wit.o (.text.DL_DMA_setSrcAddr)
                  00005994    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000059bc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000059e4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005a0c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005a34    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00005a5a    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00005a80    00000026     wit.o (.text.DL_DMA_enableChannel)
                  00005aa6    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005acc    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005af2    00000002     --HOLE-- [fill = 0]
                  00005af4    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00005b18    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005b3c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005b60    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005b84    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005ba6    00000002     --HOLE-- [fill = 0]
                  00005ba8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005bc8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005be8    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00005c06    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005c24    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005c42    00000002     --HOLE-- [fill = 0]
                  00005c44    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00005c60    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00005c7c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00005c98    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005cb4    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00005cd0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005cec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005d08    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005d24    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005d40    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005d5c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005d78    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005d94    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005db0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005dcc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005de8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005e04    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005e1c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005e34    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005e4c    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005e64    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005e7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005e94    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005eac    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00005ec4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005edc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005ef4    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005f0c    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005f24    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005f3c    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005f54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005f6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005f84    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005f9c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005fb4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005fcc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005fe4    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005ffc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00006014    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  0000602c    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00006044    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000605c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00006074    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000608c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000060a4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000060bc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000060d4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000060ec    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00006104    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000611c    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00006134    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  0000614c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00006164    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  0000617c    00000018     clock.o (.text.SysTick_Init)
                  00006194    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000061ac    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  000061c2    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  000061d8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000061ee    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00006204    00000016     key.o (.text.DL_GPIO_readPins)
                  0000621a    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00006230    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00006246    00000016     usart_app.o (.text.DL_UART_transmitData)
                  0000625c    00000016     encoder.o (.text.Encoder_Init)
                  00006272    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00006288    00000014     beep.o (.text.Beep_ms)
                  0000629c    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  000062b0    00000014     beep.o (.text.DL_GPIO_clearPins)
                  000062c4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000062d8    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  000062ec    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00006300    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00006314    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00006328    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000633c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00006350    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00006364    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00006378    00000014     usart_app.o (.text.DL_UART_receiveData)
                  0000638c    00000014     app_question_task.o (.text.State_Machine_init)
                  000063a0    00000014     wit.o (.text.WIT_Calibrate_Yaw)
                  000063b4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000063c8    00000014     pid.o (.text.pid_set_angle_target)
                  000063dc    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000063f0    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00006402    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00006414    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00006426    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00006438    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000644a    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  0000645a    00000002     --HOLE-- [fill = 0]
                  0000645c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000646c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000647c    00000010     interrupt.o (.text.SysTick_Handler)
                  0000648c    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  0000649c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000064ac    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  000064ba    00000002     --HOLE-- [fill = 0]
                  000064bc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000064ca    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000064d8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000064e6    00000002     --HOLE-- [fill = 0]
                  000064e8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000064f4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000064fe    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006508    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00006518    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006522    0000000a            : sprintf.c.obj (.text._outc)
                  0000652c    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006534    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000653c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006544    00000008     libc.a : printf.c.obj (.text._outc)
                  0000654c    00000008            : printf.c.obj (.text._outs)
                  00006554    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00006558    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000655c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000656c    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00006570    00000004            : exit.c.obj (.text:abort)
                  00006574    0000000c     --HOLE-- [fill = 0]

.cinit     0    00006f70    000000a0     
                  00006f70    0000007a     (.cinit..data.load) [load image, compression = lzss]
                  00006fea    00000002     --HOLE-- [fill = 0]
                  00006fec    0000000c     (__TI_handler_table)
                  00006ff8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00007000    00000010     (__TI_cinit_table)

.rodata    0    00006580    000009f0     
                  00006580    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00006b70    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006d98    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006da0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006ea1    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006ea4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006ecc    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006ee4    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006ef8    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006f09    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00006f1a    00000010     encoder.o (.rodata.encoder_table)
                  00006f2a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006f39    00000001     --HOLE-- [fill = 0]
                  00006f3a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006f44    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00006f4e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006f50    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006f58    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  00006f5d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006f60    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006f62    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006f64    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000002a1     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    0000002c     (.common:wit_data)
                  2020017c    00000028     (.common:angle_pid)
                  202001a4    00000028     (.common:speedA_pid)
                  202001cc    00000028     (.common:speedB_pid)
                  202001f4    00000028     (.common:tracing_pid)
                  2020021c    00000021     (.common:wit_dmaBuffer)
                  2020023d    00000014     (.common:OLED_String)
                  20200251    00000001     (.common:Digtal)
                  20200252    00000001     (.common:Key_Down)
                  20200253    00000001     (.common:Key_Old)
                  20200254    00000014     (.common:State_Machine)
                  20200268    00000010     (.common:Normal)
                  20200278    00000008     (.common:grayscale_data)
                  20200280    00000004     pid.o (.bss.Yaw_error_zzk.error)
                  20200284    00000004     (.common:first_angle)
                  20200288    00000004     (.common:start_time)
                  2020028c    00000004     (.common:target_angle)
                  20200290    00000004     (.common:tick_ms)
                  20200294    00000004     (.common:tracing_val)
                  20200298    00000004     (.common:uart_rx_ticks)
                  2020029c    00000001     (.common:Key_Up)
                  2020029d    00000001     (.common:Key_Val)
                  2020029e    00000001     (.common:grayscale_count)
                  2020029f    00000001     (.common:task_num)
                  202002a0    00000001     (.common:uart_rx_index)

.data      0    202002a4    000001f6     UNINITIALIZED
                  202002a4    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200394    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200414    00000024     Scheduler.o (.data.scheduler_task)
                  20200438    00000010     Ganv_Grayscale.o (.data.Anolog)
                  20200448    00000010     Ganv_Grayscale.o (.data.black)
                  20200458    00000010     Ganv_Grayscale.o (.data.white)
                  20200468    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020046c    00000004            : _lock.c.obj (.data._lock)
                  20200470    00000004            : _lock.c.obj (.data._unlock)
                  20200474    00000004     pid.o (.data.angle_basic_speed)
                  20200478    00000004     beep.o (.data.bee_time)
                  2020047c    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200480    00000004     motor.o (.data.speed_basic)
                  20200484    00000004     pid.o (.data.tracing_basic_speed)
                  20200488    00000002     app_question_task.o (.data.Question_Task_4.q4_count)
                  2020048a    00000002     encoder.o (.data.encoder_A_count)
                  2020048c    00000002     encoder.o (.data.encoder_B_count)
                  2020048e    00000002     encoder.o (.data.encoder_count)
                  20200490    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200491    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200492    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200493    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200494    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200495    00000001     encoder.o (.data.encoder_count_flag)
                  20200496    00000001     app_question_task.o (.data.q1_first_flag)
                  20200497    00000001     app_question_task.o (.data.q2_first_flag)
                  20200498    00000001     app_question_task.o (.data.q3_first_flag)
                  20200499    00000001     app_question_task.o (.data.q4_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3000    132       160    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3180    324       160    
                                                               
    .\APP\
       app_question_task.o            2644    0         30     
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          64      0         0      
       app_angle_control.o            60      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3004    0         37     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         116     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         116     0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          310     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         310     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1286    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1614    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         4      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          1792    0         176    
    +--+------------------------------+-------+---------+---------+
       Total:                         1792    0         176    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          430     0         81     
    +--+------------------------------+-------+---------+---------+
       Total:                         430     0         81     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       158       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   25744   2881      1687   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00007000 records: 2, size/record: 8, table size: 16
	.data: load addr=00006f70, load size=0000007a bytes, run addr=202002a4, run size=000001f6 bytes, compression=lzss
	.bss: load addr=00006ff8, load size=00000008 bytes, run addr=20200000, run size=000002a1 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006fec records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001bc9     00006508     00006506   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005a0d     0000655c     00006558   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00006555  ADC0_IRQHandler                      
00006555  ADC1_IRQHandler                      
00006555  AES_IRQHandler                       
20200438  Anolog                               
00005551  Beep_Time_Control                    
00006289  Beep_ms                              
00006570  C$$EXIT                              
00006555  CANFD0_IRQHandler                    
00006555  DAC0_IRQHandler                      
00005061  DL_ADC12_setClockConfig              
000064f5  DL_Common_delayCycles                
00004cf9  DL_DMA_initChannel                   
00004743  DL_I2C_fillControllerTXFIFO          
00005acd  DL_I2C_setClockConfig                
00003325  DL_SYSCTL_configSYSPLL               
00004f95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002d6d  DL_Timer_initFourCCPWMMode           
00003159  DL_Timer_initTimerMode               
00005db1  DL_Timer_setCaptCompUpdateMethod     
000060bd  DL_Timer_setCaptureCompareOutCtl     
0000646d  DL_Timer_setCaptureCompareValue      
00005dcd  DL_Timer_setClockConfig              
00004a15  DL_UART_drainRXFIFO                  
00004ebd  DL_UART_init                         
00006415  DL_UART_setClockConfig               
00006555  DMA_IRQHandler                       
00006555  Default_Handler                      
20200251  Digtal                               
000040d9  Encoder_Get                          
0000625d  Encoder_Init                         
00006555  GROUP0_IRQHandler                    
000026e5  GROUP1_IRQHandler                    
000035b1  Get_Analog_value                     
00005255  Get_Anolog_Value                     
000064ad  Get_Digtal_For_User                  
0000546f  Get_Normalize_For_User               
00006555  HardFault_Handler                    
00006555  I2C0_IRQHandler                      
00006555  I2C1_IRQHandler                      
20200252  Key_Down                             
20200253  Key_Old                              
0000389d  Key_Proc                             
00003fe9  Key_Read                             
2020029c  Key_Up                               
2020029d  Key_Val                              
00006555  NMI_Handler                          
0000207d  No_MCU_Ganv_Sensor_Init              
000042b5  No_MCU_Ganv_Sensor_Init_Frist        
0000501d  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200268  Normal                               
00004475  OLED_Clear                           
00002a41  OLED_Init                            
00005291  OLED_Set_Pos                         
00002929  OLED_ShowChar                        
00002f75  OLED_ShowNum                         
00004327  OLED_ShowString                      
2020023d  OLED_String                          
00003b25  OLED_WR_Byte                         
00002b51  Oled_Task                            
00002205  PID_angle_realize                    
00003cd5  PID_init                             
000025bd  PID_speed_realize                    
00002e71  PID_tracing_realize                  
00006555  PendSV_Handler                       
00004399  Question_Task_1                      
00001a1d  Question_Task_2                      
00000ee9  Question_Task_3                      
00000a91  Question_Task_4                      
00006555  RTC_IRQHandler                       
00006559  Reset_Handler                        
00006555  SPI0_IRQHandler                      
00006555  SPI1_IRQHandler                      
00006555  SVC_Handler                          
00004d91  SYSCFG_DL_ADC1_init                  
00006165  SYSCFG_DL_DMA_WIT_init               
0000652d  SYSCFG_DL_DMA_init                   
000055ed  SYSCFG_DL_FOR_CONTROL_init           
00001d5d  SYSCFG_DL_GPIO_init                  
000048b5  SYSCFG_DL_I2C_OLED_init              
00003bbd  SYSCFG_DL_PWM_MOTOR_init             
000052cd  SYSCFG_DL_SYSCTL_init                
00004a69  SYSCFG_DL_UART_0_init                
000044e1  SYSCFG_DL_UART_WIT_init              
000054a9  SYSCFG_DL_init                       
00003a89  SYSCFG_DL_initPower                  
00004235  Scheduler_Init                       
0000414d  Scheduler_Run                        
20200254  State_Machine                        
0000638d  State_Machine_init                   
0000647d  SysTick_Handler                      
0000617d  SysTick_Init                         
00006555  TIMA0_IRQHandler                     
00006555  TIMA1_IRQHandler                     
00003d5d  TIMG0_IRQHandler                     
00006555  TIMG12_IRQHandler                    
00006555  TIMG6_IRQHandler                     
00006555  TIMG7_IRQHandler                     
00006555  TIMG8_IRQHandler                     
00006427  TI_memcpy_small                      
000064d9  TI_memset_small                      
000050a1  Tracing_Control                      
00002491  Tracing_Value_Get                    
00004fd9  UART0_IRQHandler                     
00006555  UART1_IRQHandler                     
000012b5  UART2_IRQHandler                     
00006555  UART3_IRQHandler                     
000063a1  WIT_Calibrate_Yaw                    
00004061  WIT_Get_Relative_Yaw                 
00004c59  WIT_Init                             
00001eed  Yaw_error_zzk                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00007000  __TI_CINIT_Base                      
00007010  __TI_CINIT_Limit                     
00007010  __TI_CINIT_Warm                      
00006fec  __TI_Handler_Table_Base              
00006ff8  __TI_Handler_Table_Limit             
000053bd  __TI_auto_init_nobinit_nopinit       
00003f6d  __TI_decompress_lzss                 
00006439  __TI_decompress_none                 
0000490d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00006273  __TI_zero_init_nomemset              
00001bd3  __adddf3                             
000034e3  __addsf3                             
00006da0  __aeabi_ctype_table_                 
00006da0  __aeabi_ctype_table_C                
00004241  __aeabi_d2f                          
00004e29  __aeabi_d2iz                         
00001bd3  __aeabi_dadd                         
0000467d  __aeabi_dcmpeq                       
000046b9  __aeabi_dcmpge                       
000046cd  __aeabi_dcmpgt                       
000046a5  __aeabi_dcmple                       
00004691  __aeabi_dcmplt                       
00002c61  __aeabi_ddiv                         
00003241  __aeabi_dmul                         
00001bc9  __aeabi_dsub                         
20200468  __aeabi_errno                        
00006535  __aeabi_errno_addr                   
00005121  __aeabi_f2d                          
000054e1  __aeabi_f2iz                         
000034e3  __aeabi_fadd                         
000046e1  __aeabi_fcmpeq                       
0000471d  __aeabi_fcmpge                       
00004731  __aeabi_fcmpgt                       
00004709  __aeabi_fcmple                       
000046f5  __aeabi_fcmplt                       
00003ee9  __aeabi_fdiv                         
00003c49  __aeabi_fmul                         
000034d9  __aeabi_fsub                         
0000581d  __aeabi_i2d                          
00005309  __aeabi_i2f                          
000049bd  __aeabi_idiv                         
00001d5b  __aeabi_idiv0                        
000049bd  __aeabi_idivmod                      
0000306b  __aeabi_ldiv0                        
00005c25  __aeabi_llsl                         
00005b61  __aeabi_lmul                         
000064e9  __aeabi_memclr                       
000064e9  __aeabi_memclr4                      
000064e9  __aeabi_memclr8                      
0000653d  __aeabi_memcpy                       
0000653d  __aeabi_memcpy4                      
0000653d  __aeabi_memcpy8                      
000064bd  __aeabi_memset                       
000064bd  __aeabi_memset4                      
000064bd  __aeabi_memset8                      
00005b3d  __aeabi_ui2d                         
000050e1  __aeabi_uidiv                        
000050e1  __aeabi_uidivmod                     
000063b5  __aeabi_uldivmod                     
00005c25  __ashldi3                            
ffffffff  __binit__                            
00004549  __cmpdf2                             
000053f9  __cmpsf2                             
00002c61  __divdf3                             
00003ee9  __divsf3                             
00004549  __eqdf2                              
000053f9  __eqsf2                              
00005121  __extendsfdf2                        
00004e29  __fixdfsi                            
000054e1  __fixsfsi                            
0000581d  __floatsidf                          
00005309  __floatsisf                          
00005b3d  __floatunsidf                        
000041c1  __gedf2                              
00005345  __gesf2                              
000041c1  __gtdf2                              
00005345  __gtsf2                              
00004549  __ledf2                              
000053f9  __lesf2                              
00004549  __ltdf2                              
000053f9  __ltsf2                              
UNDEFED   __mpu_init                           
00003241  __muldf3                             
00005b61  __muldi3                             
00005435  __muldsi3                            
00003c49  __mulsf3                             
00004549  __nedf2                              
000053f9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001bc9  __subdf3                             
000034d9  __subsf3                             
00004241  __truncdfsf2                         
00003945  __udivmoddi4                         
00005a0d  _c_int00_noargs                      
202002a4  _ftable                              
2020046c  _lock                                
0000389b  _nop                                 
UNDEFED   _system_post_cinit                   
0000656d  _system_pre_init                     
20200470  _unlock                              
00006571  abort                                
00004e73  adc_getValue                         
00005381  angele_control                       
20200474  angle_basic_speed                    
2020017c  angle_pid                            
00006b70  asc2_0806                            
00006580  asc2_1608                            
00005161  atoi                                 
20200478  bee_time                             
ffffffff  binit                                
20200448  black                                
00004409  convertAnalogToDigital               
0000648d  delay_ms                             
0000306d  detect_trace_state_change            
2020048a  encoder_A_count                      
2020048c  encoder_B_count                      
2020048e  encoder_count                        
20200495  encoder_count_flag                   
00005849  fabs_zzk                             
20200284  first_angle                          
00005875  fputc                                
00004ca9  fputs                                
000047a1  frexp                                
000047a1  frexpl                               
202000b0  gPWM_MOTORBackup                     
00004f05  gray_init                            
000039e9  gray_task                            
2020029e  grayscale_count                      
20200278  grayscale_data                       
00000000  interruptVectors                     
00003401  ldexp                                
00003401  ldexpl                               
00004abd  main                                 
00005b85  memccpy                              
00003681  motor_direction                      
000056e1  mspm0_delay_ms                       
000058a1  mspm0_get_clock_ms                   
000037f1  normalizeAnalogValues                
00004619  oled_i2c_sda_unlock                  
00005711  oled_pow                             
000063c9  pid_set_angle_target                 
00004859  printf                               
00003745  pwm_set                              
20200496  q1_first_flag                        
20200497  q2_first_flag                        
20200498  q3_first_flag                        
20200499  q4_first_flag                        
00003401  scalbn                               
00003401  scalbnl                              
20200000  sensor                               
202001a4  speedA_pid                           
202001cc  speedB_pid                           
20200480  speed_basic                          
00005519  sprintf                              
20200288  start_time                           
2020028c  target_angle                         
2020029f  task_num                             
20200290  tick_ms                              
20200484  tracing_basic_speed                  
202001f4  tracing_pid                          
20200294  tracing_val                          
20200394  uart_rx_buffer                       
202002a0  uart_rx_index                        
20200298  uart_rx_ticks                        
00004b11  uart_task                            
0000649d  wcslen                               
20200458  white                                
20200150  wit_data                             
2020021c  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Question_Task_4                      
00000ee9  Question_Task_3                      
000012b5  UART2_IRQHandler                     
00001a1d  Question_Task_2                      
00001bc9  __aeabi_dsub                         
00001bc9  __subdf3                             
00001bd3  __adddf3                             
00001bd3  __aeabi_dadd                         
00001d5b  __aeabi_idiv0                        
00001d5d  SYSCFG_DL_GPIO_init                  
00001eed  Yaw_error_zzk                        
0000207d  No_MCU_Ganv_Sensor_Init              
00002205  PID_angle_realize                    
00002491  Tracing_Value_Get                    
000025bd  PID_speed_realize                    
000026e5  GROUP1_IRQHandler                    
00002929  OLED_ShowChar                        
00002a41  OLED_Init                            
00002b51  Oled_Task                            
00002c61  __aeabi_ddiv                         
00002c61  __divdf3                             
00002d6d  DL_Timer_initFourCCPWMMode           
00002e71  PID_tracing_realize                  
00002f75  OLED_ShowNum                         
0000306b  __aeabi_ldiv0                        
0000306d  detect_trace_state_change            
00003159  DL_Timer_initTimerMode               
00003241  __aeabi_dmul                         
00003241  __muldf3                             
00003325  DL_SYSCTL_configSYSPLL               
00003401  ldexp                                
00003401  ldexpl                               
00003401  scalbn                               
00003401  scalbnl                              
000034d9  __aeabi_fsub                         
000034d9  __subsf3                             
000034e3  __addsf3                             
000034e3  __aeabi_fadd                         
000035b1  Get_Analog_value                     
00003681  motor_direction                      
00003745  pwm_set                              
000037f1  normalizeAnalogValues                
0000389b  _nop                                 
0000389d  Key_Proc                             
00003945  __udivmoddi4                         
000039e9  gray_task                            
00003a89  SYSCFG_DL_initPower                  
00003b25  OLED_WR_Byte                         
00003bbd  SYSCFG_DL_PWM_MOTOR_init             
00003c49  __aeabi_fmul                         
00003c49  __mulsf3                             
00003cd5  PID_init                             
00003d5d  TIMG0_IRQHandler                     
00003ee9  __aeabi_fdiv                         
00003ee9  __divsf3                             
00003f6d  __TI_decompress_lzss                 
00003fe9  Key_Read                             
00004061  WIT_Get_Relative_Yaw                 
000040d9  Encoder_Get                          
0000414d  Scheduler_Run                        
000041c1  __gedf2                              
000041c1  __gtdf2                              
00004235  Scheduler_Init                       
00004241  __aeabi_d2f                          
00004241  __truncdfsf2                         
000042b5  No_MCU_Ganv_Sensor_Init_Frist        
00004327  OLED_ShowString                      
00004399  Question_Task_1                      
00004409  convertAnalogToDigital               
00004475  OLED_Clear                           
000044e1  SYSCFG_DL_UART_WIT_init              
00004549  __cmpdf2                             
00004549  __eqdf2                              
00004549  __ledf2                              
00004549  __ltdf2                              
00004549  __nedf2                              
00004619  oled_i2c_sda_unlock                  
0000467d  __aeabi_dcmpeq                       
00004691  __aeabi_dcmplt                       
000046a5  __aeabi_dcmple                       
000046b9  __aeabi_dcmpge                       
000046cd  __aeabi_dcmpgt                       
000046e1  __aeabi_fcmpeq                       
000046f5  __aeabi_fcmplt                       
00004709  __aeabi_fcmple                       
0000471d  __aeabi_fcmpge                       
00004731  __aeabi_fcmpgt                       
00004743  DL_I2C_fillControllerTXFIFO          
000047a1  frexp                                
000047a1  frexpl                               
00004859  printf                               
000048b5  SYSCFG_DL_I2C_OLED_init              
0000490d  __TI_ltoa                            
000049bd  __aeabi_idiv                         
000049bd  __aeabi_idivmod                      
00004a15  DL_UART_drainRXFIFO                  
00004a69  SYSCFG_DL_UART_0_init                
00004abd  main                                 
00004b11  uart_task                            
00004c59  WIT_Init                             
00004ca9  fputs                                
00004cf9  DL_DMA_initChannel                   
00004d91  SYSCFG_DL_ADC1_init                  
00004e29  __aeabi_d2iz                         
00004e29  __fixdfsi                            
00004e73  adc_getValue                         
00004ebd  DL_UART_init                         
00004f05  gray_init                            
00004f95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004fd9  UART0_IRQHandler                     
0000501d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005061  DL_ADC12_setClockConfig              
000050a1  Tracing_Control                      
000050e1  __aeabi_uidiv                        
000050e1  __aeabi_uidivmod                     
00005121  __aeabi_f2d                          
00005121  __extendsfdf2                        
00005161  atoi                                 
00005255  Get_Anolog_Value                     
00005291  OLED_Set_Pos                         
000052cd  SYSCFG_DL_SYSCTL_init                
00005309  __aeabi_i2f                          
00005309  __floatsisf                          
00005345  __gesf2                              
00005345  __gtsf2                              
00005381  angele_control                       
000053bd  __TI_auto_init_nobinit_nopinit       
000053f9  __cmpsf2                             
000053f9  __eqsf2                              
000053f9  __lesf2                              
000053f9  __ltsf2                              
000053f9  __nesf2                              
00005435  __muldsi3                            
0000546f  Get_Normalize_For_User               
000054a9  SYSCFG_DL_init                       
000054e1  __aeabi_f2iz                         
000054e1  __fixsfsi                            
00005519  sprintf                              
00005551  Beep_Time_Control                    
000055ed  SYSCFG_DL_FOR_CONTROL_init           
000056e1  mspm0_delay_ms                       
00005711  oled_pow                             
0000581d  __aeabi_i2d                          
0000581d  __floatsidf                          
00005849  fabs_zzk                             
00005875  fputc                                
000058a1  mspm0_get_clock_ms                   
00005a0d  _c_int00_noargs                      
00005acd  DL_I2C_setClockConfig                
00005b3d  __aeabi_ui2d                         
00005b3d  __floatunsidf                        
00005b61  __aeabi_lmul                         
00005b61  __muldi3                             
00005b85  memccpy                              
00005c25  __aeabi_llsl                         
00005c25  __ashldi3                            
00005db1  DL_Timer_setCaptCompUpdateMethod     
00005dcd  DL_Timer_setClockConfig              
000060bd  DL_Timer_setCaptureCompareOutCtl     
00006165  SYSCFG_DL_DMA_WIT_init               
0000617d  SysTick_Init                         
0000625d  Encoder_Init                         
00006273  __TI_zero_init_nomemset              
00006289  Beep_ms                              
0000638d  State_Machine_init                   
000063a1  WIT_Calibrate_Yaw                    
000063b5  __aeabi_uldivmod                     
000063c9  pid_set_angle_target                 
00006415  DL_UART_setClockConfig               
00006427  TI_memcpy_small                      
00006439  __TI_decompress_none                 
0000646d  DL_Timer_setCaptureCompareValue      
0000647d  SysTick_Handler                      
0000648d  delay_ms                             
0000649d  wcslen                               
000064ad  Get_Digtal_For_User                  
000064bd  __aeabi_memset                       
000064bd  __aeabi_memset4                      
000064bd  __aeabi_memset8                      
000064d9  TI_memset_small                      
000064e9  __aeabi_memclr                       
000064e9  __aeabi_memclr4                      
000064e9  __aeabi_memclr8                      
000064f5  DL_Common_delayCycles                
0000652d  SYSCFG_DL_DMA_init                   
00006535  __aeabi_errno_addr                   
0000653d  __aeabi_memcpy                       
0000653d  __aeabi_memcpy4                      
0000653d  __aeabi_memcpy8                      
00006555  ADC0_IRQHandler                      
00006555  ADC1_IRQHandler                      
00006555  AES_IRQHandler                       
00006555  CANFD0_IRQHandler                    
00006555  DAC0_IRQHandler                      
00006555  DMA_IRQHandler                       
00006555  Default_Handler                      
00006555  GROUP0_IRQHandler                    
00006555  HardFault_Handler                    
00006555  I2C0_IRQHandler                      
00006555  I2C1_IRQHandler                      
00006555  NMI_Handler                          
00006555  PendSV_Handler                       
00006555  RTC_IRQHandler                       
00006555  SPI0_IRQHandler                      
00006555  SPI1_IRQHandler                      
00006555  SVC_Handler                          
00006555  TIMA0_IRQHandler                     
00006555  TIMA1_IRQHandler                     
00006555  TIMG12_IRQHandler                    
00006555  TIMG6_IRQHandler                     
00006555  TIMG7_IRQHandler                     
00006555  TIMG8_IRQHandler                     
00006555  UART1_IRQHandler                     
00006555  UART3_IRQHandler                     
00006559  Reset_Handler                        
0000656d  _system_pre_init                     
00006570  C$$EXIT                              
00006571  abort                                
00006580  asc2_1608                            
00006b70  asc2_0806                            
00006da0  __aeabi_ctype_table_                 
00006da0  __aeabi_ctype_table_C                
00006fec  __TI_Handler_Table_Base              
00006ff8  __TI_Handler_Table_Limit             
00007000  __TI_CINIT_Base                      
00007010  __TI_CINIT_Limit                     
00007010  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  wit_data                             
2020017c  angle_pid                            
202001a4  speedA_pid                           
202001cc  speedB_pid                           
202001f4  tracing_pid                          
2020021c  wit_dmaBuffer                        
2020023d  OLED_String                          
20200251  Digtal                               
20200252  Key_Down                             
20200253  Key_Old                              
20200254  State_Machine                        
20200268  Normal                               
20200278  grayscale_data                       
20200284  first_angle                          
20200288  start_time                           
2020028c  target_angle                         
20200290  tick_ms                              
20200294  tracing_val                          
20200298  uart_rx_ticks                        
2020029c  Key_Up                               
2020029d  Key_Val                              
2020029e  grayscale_count                      
2020029f  task_num                             
202002a0  uart_rx_index                        
202002a4  _ftable                              
20200394  uart_rx_buffer                       
20200438  Anolog                               
20200448  black                                
20200458  white                                
20200468  __aeabi_errno                        
2020046c  _lock                                
20200470  _unlock                              
20200474  angle_basic_speed                    
20200478  bee_time                             
20200480  speed_basic                          
20200484  tracing_basic_speed                  
2020048a  encoder_A_count                      
2020048c  encoder_B_count                      
2020048e  encoder_count                        
20200495  encoder_count_flag                   
20200496  q1_first_flag                        
20200497  q2_first_flag                        
20200498  q3_first_flag                        
20200499  q4_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[299 symbols]
