################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
APP/Control/%.o: ../APP/Control/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/APP" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/APP/Control" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/BEEP" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/ADC" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/Grayscale" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/WIT" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/PID" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/Motor" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/Uart" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/Scheduler" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/OLED" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Hardware/MSPM0" -I"C:/Users/<USER>/workspace_ccstheia/24_Year_H/Debug" -I"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"APP/Control/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


