******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 09:41:08 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005a49


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000070e0  00018f20  R  X
  SRAM                  20200000   00008000  00000739  000078c7  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000070e0   000070e0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00006520   00006520    r-x .text
  000065e0    000065e0    00000a60   00000a60    r-- .rodata
  00007040    00007040    000000a0   000000a0    r-- .cinit
20200000    20200000    0000053c   00000000    rw-
  20200000    20200000    00000341   00000000    rw- .bss
  20200344    20200344    000001f8   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00006520     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000474     app_question_task.o (.text.Question_Task_4)
                  00000f04    000003cc     app_question_task.o (.text.Question_Task_3)
                  000012d0    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  0000163c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000185c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001a38    000001ac     app_question_task.o (.text.Question_Task_2)
                  00001be4    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001d88    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001f1a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001f1c    00000190     pid.o (.text.Yaw_error_zzk)
                  000020ac    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002234    00000150     pid.o (.text.PID_angle_realize)
                  00002384    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000024c0    0000012c     pid.o (.text.Tracing_Value_Get)
                  000025ec    00000128     pid.o (.text.PID_speed_realize)
                  00002714    00000128     Ganv_Grayscale.o (.text.gray_task)
                  0000283c    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00002960    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002a80    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00002b98    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00002ca8    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  00002db8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002ec4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002fc8    00000104     pid.o (.text.PID_tracing_realize)
                  000030cc    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  000031c2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000031c4    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  000032b0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00003398    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000347c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003558    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003630    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003708    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  000037d8    000000c4     motor.o (.text.motor_direction)
                  0000389c    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000394c    000000ac     motor.o (.text.pwm_set)
                  000039f8    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00003aa2    00000002     libc.a : _lock.c.obj (.text._nop)
                  00003aa4    000000a8     key.o (.text.Key_Proc)
                  00003b4c    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003bee    00000002     --HOLE-- [fill = 0]
                  00003bf0    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00003c88    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003d14    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003da0    00000088     pid.o (.text.PID_init)
                  00003e28    00000084     interrupt.o (.text.TIMG0_IRQHandler)
                  00003eac    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003f30    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003fb4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00004036    00000002     --HOLE-- [fill = 0]
                  00004038    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000040b4    00000078     key.o (.text.Key_Read)
                  0000412c    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  000041a4    00000074     encoder.o (.text.Encoder_Get)
                  00004218    00000074     Scheduler.o (.text.Scheduler_Run)
                  0000428c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004300    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004374    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000043e6    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00004456    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  000044c2    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000452c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00004594    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000045fc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00004664    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000046ca    00000002     --HOLE-- [fill = 0]
                  000046cc    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00004730    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00004792    00000002     --HOLE-- [fill = 0]
                  00004794    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000047f6    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00004854    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000048b0    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  0000490c    0000005c     libc.a : printf.c.obj (.text.printf)
                  00004968    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000049c0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004a18    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004a70    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004ac6    00000002     --HOLE-- [fill = 0]
                  00004ac8    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00004b1c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004b70    00000054     main.o (.text.main)
                  00004bc4    00000054     usart_app.o (.text.uart_task)
                  00004c18    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004c6a    00000002     --HOLE-- [fill = 0]
                  00004c6c    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00004cbc    00000050     clock.o (.text.SysTick_Config)
                  00004d0c    00000050     usart_app.o (.text.fputs)
                  00004d5c    00000050     servo.o (.text.set_angle)
                  00004dac    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004df8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004e44    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004e90    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004eda    00000002     --HOLE-- [fill = 0]
                  00004edc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004f26    0000004a     adc_app.o (.text.adc_getValue)
                  00004f70    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004fb8    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00005000    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00005048    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000508c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000050d0    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00005114    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00005156    00000002     --HOLE-- [fill = 0]
                  00005158    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00005198    00000040     app_tracing_control.o (.text.Tracing_Control)
                  000051d8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005218    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005258    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00005298    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000052d4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00005310    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000534c    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00005388    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000053c4    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005400    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000543c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00005478    0000003c     app_angle_control.o (.text.angele_control)
                  000054b4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000054f0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000552a    00000002     --HOLE-- [fill = 0]
                  0000552c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00005566    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  0000559e    00000002     --HOLE-- [fill = 0]
                  000055a0    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000055d8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005610    00000034     beep.o (.text.Beep_Time_Control)
                  00005644    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005678    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000056ac    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000056e0    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00005710    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00005740    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005770    00000030     clock.o (.text.mspm0_delay_ms)
                  000057a0    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  000057d0    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  000057fc    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00005828    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00005854    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00005880    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000058ac    0000002c     pid.o (.text.fabs_zzk)
                  000058d8    0000002c     usart_app.o (.text.fputc)
                  00005904    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00005930    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00005958    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005980    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  000059a8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000059d0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000059f8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005a20    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00005a48    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005a70    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00005a96    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00005abc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005ae2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005b08    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00005b2c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005b50    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005b74    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005b98    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005bba    00000002     --HOLE-- [fill = 0]
                  00005bbc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005bdc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005bfc    00000020     app_question_task.o (.text.Question_Task_1)
                  00005c1c    00000020     servo.o (.text.Servo_init)
                  00005c3c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00005c5a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005c78    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005c96    00000002     --HOLE-- [fill = 0]
                  00005c98    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00005cb4    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00005cd0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00005cec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005d08    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00005d24    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005d40    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005d5c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005d78    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005d94    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005db0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005dcc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005de8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005e04    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005e20    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005e3c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005e58    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005e70    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005e88    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005ea0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005eb8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005ed0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005ee8    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005f00    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00005f18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005f30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005f48    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005f60    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005f78    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005f90    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005fa8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005fc0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005fd8    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005ff0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00006008    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00006020    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00006038    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00006050    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00006068    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00006080    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00006098    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000060b0    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000060c8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000060e0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000060f8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00006110    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00006128    00000018     servo.o (.text.DL_Timer_startCounter)
                  00006140    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00006158    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00006170    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00006188    00000018     usart_app.o (.text.DL_UART_isBusy)
                  000061a0    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000061b8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000061d0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  000061e8    00000018     clock.o (.text.SysTick_Init)
                  00006200    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00006218    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  0000622e    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  00006244    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000625a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00006270    00000016     key.o (.text.DL_GPIO_readPins)
                  00006286    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  0000629c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000062b2    00000016     usart_app.o (.text.DL_UART_transmitData)
                  000062c8    00000016     encoder.o (.text.Encoder_Init)
                  000062de    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000062f4    00000014     beep.o (.text.Beep_ms)
                  00006308    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  0000631c    00000014     beep.o (.text.DL_GPIO_clearPins)
                  00006330    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00006344    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00006358    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000636c    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00006380    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00006394    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000063a8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000063bc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000063d0    00000014     interrupt.o (.text.DL_UART_receiveData)
                  000063e4    00000014     usart_app.o (.text.DL_UART_receiveData)
                  000063f8    00000014     app_question_task.o (.text.State_Machine_init)
                  0000640c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00006420    00000014     pid.o (.text.pid_set_angle_target)
                  00006434    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00006448    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  0000645a    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  0000646c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000647e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00006490    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000064a2    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000064b2    00000002     --HOLE-- [fill = 0]
                  000064b4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000064c4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000064d4    00000010     interrupt.o (.text.SysTick_Handler)
                  000064e4    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  000064f4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00006504    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  00006512    00000002     --HOLE-- [fill = 0]
                  00006514    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00006522    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00006530    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000653e    00000002     --HOLE-- [fill = 0]
                  00006540    0000000c     Scheduler.o (.text.Scheduler_Init)
                  0000654c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00006558    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00006562    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000656c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000657c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006586    0000000a            : sprintf.c.obj (.text._outc)
                  00006590    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006598    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000065a0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000065a8    00000008     libc.a : printf.c.obj (.text._outc)
                  000065b0    00000008            : printf.c.obj (.text._outs)
                  000065b8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000065bc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000065c0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000065d0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000065d4    00000004            : exit.c.obj (.text:abort)
                  000065d8    00000008     --HOLE-- [fill = 0]

.cinit     0    00007040    000000a0     
                  00007040    0000007c     (.cinit..data.load) [load image, compression = lzss]
                  000070bc    0000000c     (__TI_handler_table)
                  000070c8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000070d0    00000010     (__TI_cinit_table)

.rodata    0    000065e0    00000a60     
                  000065e0    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00006bd0    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006df8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006e00    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006f01    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006f04    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006f2c    00000024     Ganv_Grayscale.o (.rodata.str1.8350192368951116151)
                  00006f50    00000021     Ganv_Grayscale.o (.rodata.str1.36112290702919017061)
                  00006f71    00000021     Ganv_Grayscale.o (.rodata.str1.97773892569755152051)
                  00006f92    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006f94    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006fac    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006fc0    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006fd1    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00006fe2    00000010     encoder.o (.rodata.encoder_table)
                  00006ff2    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00007001    00000001     --HOLE-- [fill = 0]
                  00007002    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000700c    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00007016    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00007018    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00007020    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  00007028    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  0000702d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00007030    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00007033    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00007035    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000341     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000014     (.common:OLED_String)
                  202002f1    00000001     (.common:Digtal)
                  202002f2    00000001     (.common:Key_Down)
                  202002f3    00000001     (.common:Key_Old)
                  202002f4    00000014     (.common:State_Machine)
                  20200308    00000010     (.common:Normal)
                  20200318    00000008     (.common:grayscale_data)
                  20200320    00000004     pid.o (.bss.Yaw_error_zzk.error)
                  20200324    00000004     (.common:first_angle)
                  20200328    00000004     (.common:start_time)
                  2020032c    00000004     (.common:target_angle)
                  20200330    00000004     (.common:tick_ms)
                  20200334    00000004     (.common:tracing_val)
                  20200338    00000004     (.common:uart_rx_ticks)
                  2020033c    00000001     (.common:Key_Up)
                  2020033d    00000001     (.common:Key_Val)
                  2020033e    00000001     (.common:grayscale_count)
                  2020033f    00000001     (.common:task_num)
                  20200340    00000001     (.common:uart_rx_index)

.data      0    20200344    000001f8     UNINITIALIZED
                  20200344    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200434    00000080     usart_app.o (.data.uart_rx_buffer)
                  202004b4    00000024     Scheduler.o (.data.scheduler_task)
                  202004d8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004e8    00000010     Ganv_Grayscale.o (.data.black)
                  202004f8    00000010     Ganv_Grayscale.o (.data.white)
                  20200508    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020050c    00000004            : _lock.c.obj (.data._lock)
                  20200510    00000004            : _lock.c.obj (.data._unlock)
                  20200514    00000004     pid.o (.data.angle_basic_speed)
                  20200518    00000004     beep.o (.data.bee_time)
                  2020051c    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200520    00000004     motor.o (.data.speed_basic)
                  20200524    00000004     pid.o (.data.tracing_basic_speed)
                  20200528    00000002     app_question_task.o (.data.Question_Task_4.q4_count)
                  2020052a    00000002     encoder.o (.data.encoder_A_count)
                  2020052c    00000002     encoder.o (.data.encoder_B_count)
                  2020052e    00000002     encoder.o (.data.encoder_count)
                  20200530    00000002     servo.o (.data.init_angle)
                  20200532    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200533    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200534    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200535    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200536    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200537    00000001     encoder.o (.data.encoder_count_flag)
                  20200538    00000001     app_question_task.o (.data.q1_first_flag)
                  20200539    00000001     app_question_task.o (.data.q2_first_flag)
                  2020053a    00000001     app_question_task.o (.data.q3_first_flag)
                  2020053b    00000001     app_question_task.o (.data.q4_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3336    335       320    
                                                               
    .\APP\
       app_question_task.o            2592    0         30     
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          64      0         0      
       app_angle_control.o            60      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2952    0         37     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         116     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         116     0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1600    102       250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1600    102       250    
                                                               
    .\Hardware\Key\
       key.o                          310     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         310     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1286    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1614    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         4      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          1792    0         176    
    +--+------------------------------+-------+---------+---------+
       Total:                         1792    0         176    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        136     0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         136     0         2      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         81     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         81     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2918    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       160       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   25850   2996      1849   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000070d0 records: 2, size/record: 8, table size: 16
	.data: load addr=00007040, load size=0000007c bytes, run addr=20200344, run size=000001f8 bytes, compression=lzss
	.bss: load addr=000070c8, load size=00000008 bytes, run addr=20200000, run size=00000341 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000070bc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001d89     0000656c     0000656a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005a49     000065c0     000065bc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000065b9  ADC0_IRQHandler                      
000065b9  ADC1_IRQHandler                      
000065b9  AES_IRQHandler                       
202004d8  Anolog                               
00005611  Beep_Time_Control                    
000062f5  Beep_ms                              
000065d4  C$$EXIT                              
000065b9  CANFD0_IRQHandler                    
000065b9  DAC0_IRQHandler                      
00005159  DL_ADC12_setClockConfig              
00006559  DL_Common_delayCycles                
00004dad  DL_DMA_initChannel                   
000047f7  DL_I2C_fillControllerTXFIFO          
00005ae3  DL_I2C_setClockConfig                
0000347d  DL_SYSCTL_configSYSPLL               
00005049  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002ec5  DL_Timer_initFourCCPWMMode           
000032b1  DL_Timer_initTimerMode               
00005e05  DL_Timer_setCaptCompUpdateMethod     
00006111  DL_Timer_setCaptureCompareOutCtl     
000064c5  DL_Timer_setCaptureCompareValue      
00005e21  DL_Timer_setClockConfig              
00004ac9  DL_UART_drainRXFIFO                  
00004f71  DL_UART_init                         
0000646d  DL_UART_setClockConfig               
000065b9  DMA_IRQHandler                       
000065b9  Default_Handler                      
202002f1  Digtal                               
000041a5  Encoder_Get                          
000062c9  Encoder_Init                         
000065b9  GROUP0_IRQHandler                    
0000283d  GROUP1_IRQHandler                    
00003709  Get_Analog_value                     
0000534d  Get_Anolog_Value                     
00006505  Get_Digtal_For_User                  
00005567  Get_Normalize_For_User               
000065b9  HardFault_Handler                    
000065b9  I2C0_IRQHandler                      
000065b9  I2C1_IRQHandler                      
202002f2  Key_Down                             
202002f3  Key_Old                              
00003aa5  Key_Proc                             
000040b5  Key_Read                             
2020033c  Key_Up                               
2020033d  Key_Val                              
000065b9  NMI_Handler                          
000020ad  No_MCU_Ganv_Sensor_Init              
00004375  No_MCU_Ganv_Sensor_Init_Frist        
00005115  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200308  Normal                               
000044c3  OLED_Clear                           
00002b99  OLED_Init                            
00005389  OLED_Set_Pos                         
00002a81  OLED_ShowChar                        
000030cd  OLED_ShowNum                         
000043e7  OLED_ShowString                      
202002dd  OLED_String                          
00003bf1  OLED_WR_Byte                         
00002ca9  Oled_Task                            
00002235  PID_angle_realize                    
00003da1  PID_init                             
000025ed  PID_speed_realize                    
00002fc9  PID_tracing_realize                  
000065b9  PendSV_Handler                       
00005bfd  Question_Task_1                      
00001a39  Question_Task_2                      
00000f05  Question_Task_3                      
00000a91  Question_Task_4                      
000065b9  RTC_IRQHandler                       
000065bd  Reset_Handler                        
000065b9  SPI0_IRQHandler                      
000065b9  SPI1_IRQHandler                      
000065b9  SVC_Handler                          
00004e45  SYSCFG_DL_ADC1_init                  
000061d1  SYSCFG_DL_DMA_WIT_init               
00006591  SYSCFG_DL_DMA_init                   
000056ad  SYSCFG_DL_FOR_CONTROL_init           
00001be5  SYSCFG_DL_GPIO_init                  
00004969  SYSCFG_DL_I2C_OLED_init              
00003c89  SYSCFG_DL_PWM_MOTOR_init             
0000452d  SYSCFG_DL_PWM_SERVO_init             
000053c5  SYSCFG_DL_SYSCTL_init                
00004b1d  SYSCFG_DL_UART_0_init                
00004595  SYSCFG_DL_UART_WIT_init              
0000508d  SYSCFG_DL_init                       
0000389d  SYSCFG_DL_initPower                  
00006541  Scheduler_Init                       
00004219  Scheduler_Run                        
00005c1d  Servo_init                           
202002f4  State_Machine                        
000063f9  State_Machine_init                   
000064d5  SysTick_Handler                      
000061e9  SysTick_Init                         
000065b9  TIMA0_IRQHandler                     
000065b9  TIMA1_IRQHandler                     
00003e29  TIMG0_IRQHandler                     
000065b9  TIMG12_IRQHandler                    
000065b9  TIMG6_IRQHandler                     
000065b9  TIMG7_IRQHandler                     
000065b9  TIMG8_IRQHandler                     
0000647f  TI_memcpy_small                      
00006531  TI_memset_small                      
00005199  Tracing_Control                      
000024c1  Tracing_Value_Get                    
000050d1  UART0_IRQHandler                     
000065b9  UART1_IRQHandler                     
000012d1  UART2_IRQHandler                     
000065b9  UART3_IRQHandler                     
0000412d  WIT_Get_Relative_Yaw                 
00001f1d  Yaw_error_zzk                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000070d0  __TI_CINIT_Base                      
000070e0  __TI_CINIT_Limit                     
000070e0  __TI_CINIT_Warm                      
000070bc  __TI_Handler_Table_Base              
000070c8  __TI_Handler_Table_Limit             
000054b5  __TI_auto_init_nobinit_nopinit       
00004039  __TI_decompress_lzss                 
00006491  __TI_decompress_none                 
000049c1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000062df  __TI_zero_init_nomemset              
00001d93  __adddf3                             
0000363b  __addsf3                             
00006e00  __aeabi_ctype_table_                 
00006e00  __aeabi_ctype_table_C                
00004301  __aeabi_d2f                          
00004edd  __aeabi_d2iz                         
00001d93  __aeabi_dadd                         
00004731  __aeabi_dcmpeq                       
0000476d  __aeabi_dcmpge                       
00004781  __aeabi_dcmpgt                       
00004759  __aeabi_dcmple                       
00004745  __aeabi_dcmplt                       
00002db9  __aeabi_ddiv                         
00003399  __aeabi_dmul                         
00001d89  __aeabi_dsub                         
20200508  __aeabi_errno                        
00006599  __aeabi_errno_addr                   
00005219  __aeabi_f2d                          
000055a1  __aeabi_f2iz                         
0000363b  __aeabi_fadd                         
00004795  __aeabi_fcmpeq                       
000047d1  __aeabi_fcmpge                       
000047e5  __aeabi_fcmpgt                       
000047bd  __aeabi_fcmple                       
000047a9  __aeabi_fcmplt                       
00003fb5  __aeabi_fdiv                         
00003d15  __aeabi_fmul                         
00003631  __aeabi_fsub                         
00005881  __aeabi_i2d                          
00005401  __aeabi_i2f                          
00004a71  __aeabi_idiv                         
00001f1b  __aeabi_idiv0                        
00004a71  __aeabi_idivmod                      
000031c3  __aeabi_ldiv0                        
00005c79  __aeabi_llsl                         
00005b75  __aeabi_lmul                         
0000654d  __aeabi_memclr                       
0000654d  __aeabi_memclr4                      
0000654d  __aeabi_memclr8                      
000065a1  __aeabi_memcpy                       
000065a1  __aeabi_memcpy4                      
000065a1  __aeabi_memcpy8                      
00006515  __aeabi_memset                       
00006515  __aeabi_memset4                      
00006515  __aeabi_memset8                      
00005b51  __aeabi_ui2d                         
00005a21  __aeabi_ui2f                         
000051d9  __aeabi_uidiv                        
000051d9  __aeabi_uidivmod                     
0000640d  __aeabi_uldivmod                     
00005c79  __ashldi3                            
ffffffff  __binit__                            
000045fd  __cmpdf2                             
000054f1  __cmpsf2                             
00002db9  __divdf3                             
00003fb5  __divsf3                             
000045fd  __eqdf2                              
000054f1  __eqsf2                              
00005219  __extendsfdf2                        
00004edd  __fixdfsi                            
000055a1  __fixsfsi                            
00005881  __floatsidf                          
00005401  __floatsisf                          
00005b51  __floatunsidf                        
00005a21  __floatunsisf                        
0000428d  __gedf2                              
0000543d  __gesf2                              
0000428d  __gtdf2                              
0000543d  __gtsf2                              
000045fd  __ledf2                              
000054f1  __lesf2                              
000045fd  __ltdf2                              
000054f1  __ltsf2                              
UNDEFED   __mpu_init                           
00003399  __muldf3                             
00005b75  __muldi3                             
0000552d  __muldsi3                            
00003d15  __mulsf3                             
000045fd  __nedf2                              
000054f1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001d89  __subdf3                             
00003631  __subsf3                             
00004301  __truncdfsf2                         
00003b4d  __udivmoddi4                         
00005a49  _c_int00_noargs                      
20200344  _ftable                              
2020050c  _lock                                
00003aa3  _nop                                 
UNDEFED   _system_post_cinit                   
000065d1  _system_pre_init                     
20200510  _unlock                              
000065d5  abort                                
00004f27  adc_getValue                         
00005479  angele_control                       
20200514  angle_basic_speed                    
2020021c  angle_pid                            
00006bd0  asc2_0806                            
000065e0  asc2_1608                            
00005259  atoi                                 
20200518  bee_time                             
ffffffff  binit                                
202004e8  black                                
00004457  convertAnalogToDigital               
000064e5  delay_ms                             
000031c5  detect_trace_state_change            
2020052a  encoder_A_count                      
2020052c  encoder_B_count                      
2020052e  encoder_count                        
20200537  encoder_count_flag                   
000058ad  fabs_zzk                             
20200324  first_angle                          
000058d9  fputc                                
00004d0d  fputs                                
00004855  frexp                                
00004855  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
00004fb9  gray_init                            
00002715  gray_task                            
2020033e  grayscale_count                      
20200318  grayscale_data                       
20200530  init_angle                           
00000000  interruptVectors                     
00003559  ldexp                                
00003559  ldexpl                               
00004b71  main                                 
00005b99  memccpy                              
000037d9  motor_direction                      
00005771  mspm0_delay_ms                       
00005905  mspm0_get_clock_ms                   
000039f9  normalizeAnalogValues                
000046cd  oled_i2c_sda_unlock                  
000057a1  oled_pow                             
00006421  pid_set_angle_target                 
0000490d  printf                               
0000394d  pwm_set                              
20200538  q1_first_flag                        
20200539  q2_first_flag                        
2020053a  q3_first_flag                        
2020053b  q4_first_flag                        
00003559  scalbn                               
00003559  scalbnl                              
20200000  sensor                               
00004d5d  set_angle                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200520  speed_basic                          
000055d9  sprintf                              
20200328  start_time                           
2020032c  target_angle                         
2020033f  task_num                             
20200330  tick_ms                              
20200524  tracing_basic_speed                  
20200294  tracing_pid                          
20200334  tracing_val                          
20200434  uart_rx_buffer                       
20200340  uart_rx_index                        
20200338  uart_rx_ticks                        
00004bc5  uart_task                            
000064f5  wcslen                               
202004f8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  Question_Task_4                      
00000f05  Question_Task_3                      
000012d1  UART2_IRQHandler                     
00001a39  Question_Task_2                      
00001be5  SYSCFG_DL_GPIO_init                  
00001d89  __aeabi_dsub                         
00001d89  __subdf3                             
00001d93  __adddf3                             
00001d93  __aeabi_dadd                         
00001f1b  __aeabi_idiv0                        
00001f1d  Yaw_error_zzk                        
000020ad  No_MCU_Ganv_Sensor_Init              
00002235  PID_angle_realize                    
000024c1  Tracing_Value_Get                    
000025ed  PID_speed_realize                    
00002715  gray_task                            
0000283d  GROUP1_IRQHandler                    
00002a81  OLED_ShowChar                        
00002b99  OLED_Init                            
00002ca9  Oled_Task                            
00002db9  __aeabi_ddiv                         
00002db9  __divdf3                             
00002ec5  DL_Timer_initFourCCPWMMode           
00002fc9  PID_tracing_realize                  
000030cd  OLED_ShowNum                         
000031c3  __aeabi_ldiv0                        
000031c5  detect_trace_state_change            
000032b1  DL_Timer_initTimerMode               
00003399  __aeabi_dmul                         
00003399  __muldf3                             
0000347d  DL_SYSCTL_configSYSPLL               
00003559  ldexp                                
00003559  ldexpl                               
00003559  scalbn                               
00003559  scalbnl                              
00003631  __aeabi_fsub                         
00003631  __subsf3                             
0000363b  __addsf3                             
0000363b  __aeabi_fadd                         
00003709  Get_Analog_value                     
000037d9  motor_direction                      
0000389d  SYSCFG_DL_initPower                  
0000394d  pwm_set                              
000039f9  normalizeAnalogValues                
00003aa3  _nop                                 
00003aa5  Key_Proc                             
00003b4d  __udivmoddi4                         
00003bf1  OLED_WR_Byte                         
00003c89  SYSCFG_DL_PWM_MOTOR_init             
00003d15  __aeabi_fmul                         
00003d15  __mulsf3                             
00003da1  PID_init                             
00003e29  TIMG0_IRQHandler                     
00003fb5  __aeabi_fdiv                         
00003fb5  __divsf3                             
00004039  __TI_decompress_lzss                 
000040b5  Key_Read                             
0000412d  WIT_Get_Relative_Yaw                 
000041a5  Encoder_Get                          
00004219  Scheduler_Run                        
0000428d  __gedf2                              
0000428d  __gtdf2                              
00004301  __aeabi_d2f                          
00004301  __truncdfsf2                         
00004375  No_MCU_Ganv_Sensor_Init_Frist        
000043e7  OLED_ShowString                      
00004457  convertAnalogToDigital               
000044c3  OLED_Clear                           
0000452d  SYSCFG_DL_PWM_SERVO_init             
00004595  SYSCFG_DL_UART_WIT_init              
000045fd  __cmpdf2                             
000045fd  __eqdf2                              
000045fd  __ledf2                              
000045fd  __ltdf2                              
000045fd  __nedf2                              
000046cd  oled_i2c_sda_unlock                  
00004731  __aeabi_dcmpeq                       
00004745  __aeabi_dcmplt                       
00004759  __aeabi_dcmple                       
0000476d  __aeabi_dcmpge                       
00004781  __aeabi_dcmpgt                       
00004795  __aeabi_fcmpeq                       
000047a9  __aeabi_fcmplt                       
000047bd  __aeabi_fcmple                       
000047d1  __aeabi_fcmpge                       
000047e5  __aeabi_fcmpgt                       
000047f7  DL_I2C_fillControllerTXFIFO          
00004855  frexp                                
00004855  frexpl                               
0000490d  printf                               
00004969  SYSCFG_DL_I2C_OLED_init              
000049c1  __TI_ltoa                            
00004a71  __aeabi_idiv                         
00004a71  __aeabi_idivmod                      
00004ac9  DL_UART_drainRXFIFO                  
00004b1d  SYSCFG_DL_UART_0_init                
00004b71  main                                 
00004bc5  uart_task                            
00004d0d  fputs                                
00004d5d  set_angle                            
00004dad  DL_DMA_initChannel                   
00004e45  SYSCFG_DL_ADC1_init                  
00004edd  __aeabi_d2iz                         
00004edd  __fixdfsi                            
00004f27  adc_getValue                         
00004f71  DL_UART_init                         
00004fb9  gray_init                            
00005049  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000508d  SYSCFG_DL_init                       
000050d1  UART0_IRQHandler                     
00005115  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005159  DL_ADC12_setClockConfig              
00005199  Tracing_Control                      
000051d9  __aeabi_uidiv                        
000051d9  __aeabi_uidivmod                     
00005219  __aeabi_f2d                          
00005219  __extendsfdf2                        
00005259  atoi                                 
0000534d  Get_Anolog_Value                     
00005389  OLED_Set_Pos                         
000053c5  SYSCFG_DL_SYSCTL_init                
00005401  __aeabi_i2f                          
00005401  __floatsisf                          
0000543d  __gesf2                              
0000543d  __gtsf2                              
00005479  angele_control                       
000054b5  __TI_auto_init_nobinit_nopinit       
000054f1  __cmpsf2                             
000054f1  __eqsf2                              
000054f1  __lesf2                              
000054f1  __ltsf2                              
000054f1  __nesf2                              
0000552d  __muldsi3                            
00005567  Get_Normalize_For_User               
000055a1  __aeabi_f2iz                         
000055a1  __fixsfsi                            
000055d9  sprintf                              
00005611  Beep_Time_Control                    
000056ad  SYSCFG_DL_FOR_CONTROL_init           
00005771  mspm0_delay_ms                       
000057a1  oled_pow                             
00005881  __aeabi_i2d                          
00005881  __floatsidf                          
000058ad  fabs_zzk                             
000058d9  fputc                                
00005905  mspm0_get_clock_ms                   
00005a21  __aeabi_ui2f                         
00005a21  __floatunsisf                        
00005a49  _c_int00_noargs                      
00005ae3  DL_I2C_setClockConfig                
00005b51  __aeabi_ui2d                         
00005b51  __floatunsidf                        
00005b75  __aeabi_lmul                         
00005b75  __muldi3                             
00005b99  memccpy                              
00005bfd  Question_Task_1                      
00005c1d  Servo_init                           
00005c79  __aeabi_llsl                         
00005c79  __ashldi3                            
00005e05  DL_Timer_setCaptCompUpdateMethod     
00005e21  DL_Timer_setClockConfig              
00006111  DL_Timer_setCaptureCompareOutCtl     
000061d1  SYSCFG_DL_DMA_WIT_init               
000061e9  SysTick_Init                         
000062c9  Encoder_Init                         
000062df  __TI_zero_init_nomemset              
000062f5  Beep_ms                              
000063f9  State_Machine_init                   
0000640d  __aeabi_uldivmod                     
00006421  pid_set_angle_target                 
0000646d  DL_UART_setClockConfig               
0000647f  TI_memcpy_small                      
00006491  __TI_decompress_none                 
000064c5  DL_Timer_setCaptureCompareValue      
000064d5  SysTick_Handler                      
000064e5  delay_ms                             
000064f5  wcslen                               
00006505  Get_Digtal_For_User                  
00006515  __aeabi_memset                       
00006515  __aeabi_memset4                      
00006515  __aeabi_memset8                      
00006531  TI_memset_small                      
00006541  Scheduler_Init                       
0000654d  __aeabi_memclr                       
0000654d  __aeabi_memclr4                      
0000654d  __aeabi_memclr8                      
00006559  DL_Common_delayCycles                
00006591  SYSCFG_DL_DMA_init                   
00006599  __aeabi_errno_addr                   
000065a1  __aeabi_memcpy                       
000065a1  __aeabi_memcpy4                      
000065a1  __aeabi_memcpy8                      
000065b9  ADC0_IRQHandler                      
000065b9  ADC1_IRQHandler                      
000065b9  AES_IRQHandler                       
000065b9  CANFD0_IRQHandler                    
000065b9  DAC0_IRQHandler                      
000065b9  DMA_IRQHandler                       
000065b9  Default_Handler                      
000065b9  GROUP0_IRQHandler                    
000065b9  HardFault_Handler                    
000065b9  I2C0_IRQHandler                      
000065b9  I2C1_IRQHandler                      
000065b9  NMI_Handler                          
000065b9  PendSV_Handler                       
000065b9  RTC_IRQHandler                       
000065b9  SPI0_IRQHandler                      
000065b9  SPI1_IRQHandler                      
000065b9  SVC_Handler                          
000065b9  TIMA0_IRQHandler                     
000065b9  TIMA1_IRQHandler                     
000065b9  TIMG12_IRQHandler                    
000065b9  TIMG6_IRQHandler                     
000065b9  TIMG7_IRQHandler                     
000065b9  TIMG8_IRQHandler                     
000065b9  UART1_IRQHandler                     
000065b9  UART3_IRQHandler                     
000065bd  Reset_Handler                        
000065d1  _system_pre_init                     
000065d4  C$$EXIT                              
000065d5  abort                                
000065e0  asc2_1608                            
00006bd0  asc2_0806                            
00006e00  __aeabi_ctype_table_                 
00006e00  __aeabi_ctype_table_C                
000070bc  __TI_Handler_Table_Base              
000070c8  __TI_Handler_Table_Limit             
000070d0  __TI_CINIT_Base                      
000070e0  __TI_CINIT_Limit                     
000070e0  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  OLED_String                          
202002f1  Digtal                               
202002f2  Key_Down                             
202002f3  Key_Old                              
202002f4  State_Machine                        
20200308  Normal                               
20200318  grayscale_data                       
20200324  first_angle                          
20200328  start_time                           
2020032c  target_angle                         
20200330  tick_ms                              
20200334  tracing_val                          
20200338  uart_rx_ticks                        
2020033c  Key_Up                               
2020033d  Key_Val                              
2020033e  grayscale_count                      
2020033f  task_num                             
20200340  uart_rx_index                        
20200344  _ftable                              
20200434  uart_rx_buffer                       
202004d8  Anolog                               
202004e8  black                                
202004f8  white                                
20200508  __aeabi_errno                        
2020050c  _lock                                
20200510  _unlock                              
20200514  angle_basic_speed                    
20200518  bee_time                             
20200520  speed_basic                          
20200524  tracing_basic_speed                  
2020052a  encoder_A_count                      
2020052c  encoder_B_count                      
2020052e  encoder_count                        
20200530  init_angle                           
20200537  encoder_count_flag                   
20200538  q1_first_flag                        
20200539  q2_first_flag                        
2020053a  q3_first_flag                        
2020053b  q4_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[304 symbols]
