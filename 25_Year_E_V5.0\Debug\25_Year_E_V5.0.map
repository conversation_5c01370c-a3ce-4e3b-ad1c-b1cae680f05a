******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 18:21:16 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003a0d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004e50  0001b1b0  R  X
  SRAM                  20200000   00008000  00000707  000078f9  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004e50   00004e50    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004420   00004420    r-x .text
  000044e0    000044e0    000008d8   000008d8    r-- .rodata
  00004db8    00004db8    00000098   00000098    r-- .cinit
20200000    20200000    0000050a   00000000    rw-
  20200000    20200000    00000325   00000000    rw- .bss
  20200328    20200328    000001e2   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004420     
                  000000c0    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  0000042c    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  000006b0    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000854    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000009e6    00000002     app_question_task.o (.text.Question_Task_2)
                  000009e8    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000b70    00000138     pid.o (.text.Tracing_Value_Get)
                  00000ca8    00000128     pid.o (.text.PID_speed_realize)
                  00000dd0    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00000ef4    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  0000100c    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  0000111c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001228    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000132c    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  00001422    00000002     app_question_task.o (.text.Question_Task_3)
                  00001424    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  00001510    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000015f8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000016dc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000017b8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001890    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00001960    000000d0     oled_hardware_i2c.o (.text.Oled_Task)
                  00001a30    000000c4     key.o (.text.Key_Proc)
                  00001af4    000000c4     motor.o (.text.motor_direction)
                  00001bb8    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001c68    000000ac     motor.o (.text.pwm_set)
                  00001d14    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00001dbe    00000002     app_question_task.o (.text.Question_Task_4)
                  00001dc0    000000a8     app_question_task.o (.text.Question_Task_1)
                  00001e68    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00001f08    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00001fa0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  0000202c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000020b8    00000088     pid.o (.text.PID_init)
                  00002140    00000088     interrupt.o (.text.TIMG0_IRQHandler)
                  000021c8    00000084     clock.o (.text.__NVIC_SetPriority)
                  0000224c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000022d0    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002352    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002354    0000007c     servo.o (.text.Servo_SetAngle)
                  000023d0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000244c    00000078     key.o (.text.Key_Read)
                  000024c4    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  0000253c    00000074     encoder.o (.text.Encoder_Get)
                  000025b0    00000074     Scheduler.o (.text.Scheduler_Run)
                  00002624    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002698    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000026a0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002714    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002786    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  000027f2    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000285c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  000028c4    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  0000292c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002994    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  000029f8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002a5a    00000002     libc.a : _lock.c.obj (.text._nop)
                  00002a5c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002abe    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002b1c    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00002b78    0000005c     libc.a : printf.c.obj (.text.printf)
                  00002bd4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002c2c    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00002c80    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002cd4    00000054     usart_app.o (.text.uart_task)
                  00002d28    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00002d78    00000050     clock.o (.text.SysTick_Config)
                  00002dc8    00000050     usart_app.o (.text.fputs)
                  00002e18    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00002e64    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002eb0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00002efc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002f46    00000002     --HOLE-- [fill = 0]
                  00002f48    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002f92    0000004a     adc_app.o (.text.adc_getValue)
                  00002fdc    0000004a     main.o (.text.main)
                  00003026    00000002     --HOLE-- [fill = 0]
                  00003028    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003070    00000048     Ganv_Grayscale.o (.text.gray_init)
                  000030b8    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00003100    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003144    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003188    00000044     usart_app.o (.text.UART0_IRQHandler)
                  000031cc    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000320e    00000002     --HOLE-- [fill = 0]
                  00003210    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003250    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003290    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000032d0    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000330c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003348    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003384    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000033c0    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000033fc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003438    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003474    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000034b0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000034ec    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003526    00000002     --HOLE-- [fill = 0]
                  00003528    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003562    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  0000359a    00000002     --HOLE-- [fill = 0]
                  0000359c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000035d4    00000034     beep.o (.text.Beep_Time_Control)
                  00003608    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000363c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003670    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000036a4    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  000036d4    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00003704    00000030     clock.o (.text.mspm0_delay_ms)
                  00003734    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00003764    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00003790    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  000037bc    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  000037e8    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00003814    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00003840    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  0000386c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003898    0000002c     usart_app.o (.text.fputc)
                  000038c4    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  000038f0    0000002c     app_tracing_control.o (.text.speed_control)
                  0000391c    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00003944    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000396c    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00003994    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000039bc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000039e4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003a0c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003a34    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00003a5a    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00003a80    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003aa6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003acc    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00003af0    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003b14    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003b38    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003b5a    00000002     --HOLE-- [fill = 0]
                  00003b5c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003b7c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003b9c    00000020     pid.o (.text.pid_set_speed_target)
                  00003bbc    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003bda    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003bf8    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00003c14    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00003c30    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003c4c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003c68    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00003c84    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003ca0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003cbc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003cd8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003cf4    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00003d10    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003d2c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003d48    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003d64    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003d80    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003d9c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003db8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003dd0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003de8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003e00    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00003e18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003e30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003e48    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003e60    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00003e78    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003e90    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003ea8    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00003ec0    00000018     beep.o (.text.DL_GPIO_setPins)
                  00003ed8    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003ef0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00003f08    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003f20    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003f38    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00003f50    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003f68    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003f80    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003f98    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00003fb0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003fc8    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00003fe0    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00003ff8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004010    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004028    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004040    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004058    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004070    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004088    00000018     servo.o (.text.DL_Timer_startCounter)
                  000040a0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000040b8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000040d0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000040e8    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00004100    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00004118    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004130    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00004148    00000018     clock.o (.text.SysTick_Init)
                  00004160    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00004176    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  0000418c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000041a2    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000041b8    00000016     key.o (.text.DL_GPIO_readPins)
                  000041ce    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  000041e4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000041fa    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00004210    00000016     encoder.o (.text.Encoder_Init)
                  00004226    00000016     interrupt.o (.text.Timer_Init)
                  0000423c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004252    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00004266    00000014     beep.o (.text.DL_GPIO_clearPins)
                  0000427a    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000428e    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  000042a2    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000042b6    00000002     --HOLE-- [fill = 0]
                  000042b8    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  000042cc    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000042e0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000042f4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004308    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000431c    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00004330    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00004344    00000014     servo.o (.text.Servo_init)
                  00004358    00000014     app_question_task.o (.text.State_Machine_init)
                  0000436c    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  0000437e    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00004390    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000043a2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000043b4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000043c6    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000043d6    00000002     --HOLE-- [fill = 0]
                  000043d8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000043e8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000043f8    00000010     interrupt.o (.text.SysTick_Handler)
                  00004408    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00004418    00000010     app_tracing_control.o (.text.servo_tracing)
                  00004428    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  00004436    00000002     --HOLE-- [fill = 0]
                  00004438    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004446    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004454    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004462    00000002     --HOLE-- [fill = 0]
                  00004464    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00004470    0000000c     app_tracing_control.o (.text.Tracing_Control)
                  0000447c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00004488    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004492    0000000a     servo.o (.text.Servo_SetCenter)
                  0000449c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000044a4    00000008     libc.a : printf.c.obj (.text._outc)
                  000044ac    00000008            : printf.c.obj (.text._outs)
                  000044b4    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000044b8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000044bc    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000044cc    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000044d0    00000004            : exit.c.obj (.text:abort)
                  000044d4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00004db8    00000098     
                  00004db8    00000070     (.cinit..data.load) [load image, compression = lzss]
                  00004e28    0000000c     (__TI_handler_table)
                  00004e34    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004e3c    00000010     (__TI_cinit_table)
                  00004e4c    00000004     --HOLE-- [fill = 0]

.rodata    0    000044e0    000008d8     
                  000044e0    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00004ad0    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00004cf8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004d20    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00004d38    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00004d4c    00000011     libc.a : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00004d5d    00000010     encoder.o (.rodata.encoder_table)
                  00004d6d    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00004d7c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004d86    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00004d90    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00004d98    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00004da0    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  00004da8    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00004dab    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00004dae    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00004db1    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004db3    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004db5    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00004db7    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000325     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:time_last)
                  20200318    00000004     (.common:tracing_val)
                  2020031c    00000004     (.common:uart_rx_ticks)
                  20200320    00000001     (.common:Key_Up)
                  20200321    00000001     (.common:Key_Val)
                  20200322    00000001     (.common:grayscale_count)
                  20200323    00000001     (.common:task_num)
                  20200324    00000001     (.common:uart_rx_index)

.data      0    20200328    000001e2     UNINITIALIZED
                  20200328    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200418    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200498    00000024     Scheduler.o (.data.scheduler_task)
                  202004bc    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004cc    00000010     Ganv_Grayscale.o (.data.black)
                  202004dc    00000010     Ganv_Grayscale.o (.data.white)
                  202004ec    00000004     libc.a : _lock.c.obj (.data._lock)
                  202004f0    00000004            : _lock.c.obj (.data._unlock)
                  202004f4    00000004     beep.o (.data.bee_time)
                  202004f8    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004fc    00000002     encoder.o (.data.encoder_A_count)
                  202004fe    00000002     encoder.o (.data.encoder_B_count)
                  20200500    00000002     encoder.o (.data.encoder_count)
                  20200502    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200503    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200504    00000001     app_question_task.o (.data.circle_num)
                  20200505    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200506    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200507    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200508    00000001     encoder.o (.data.encoder_count_flag)
                  20200509    00000001     app_question_task.o (.data.q1_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_tracing_check.o            236     0         7      
       app_question_task.o            194     0         26     
       app_tracing_control.o          72      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         502     0         33     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1400    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1728    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2170    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2170    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          776     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         776     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658     17        0      
       defs.c.obj                     0       0         240    
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       memset16.S.obj                 14      0         0      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1122    17        248    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2526    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       148       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17408   2603      1799   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004e3c records: 2, size/record: 8, table size: 16
	.data: load addr=00004db8, load size=00000070 bytes, run addr=20200328, run size=000001e2 bytes, compression=lzss
	.bss: load addr=00004e34, load size=00000008 bytes, run addr=20200000, run size=00000325 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004e28 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003a0d     000044bc     000044b8   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000044b5  ADC0_IRQHandler                      
000044b5  ADC1_IRQHandler                      
000044b5  AES_IRQHandler                       
202004bc  Anolog                               
000035d5  Beep_Time_Control                    
000044d0  C$$EXIT                              
000044b5  CANFD0_IRQHandler                    
000044b5  DAC0_IRQHandler                      
00003211  DL_ADC12_setClockConfig              
00004489  DL_Common_delayCycles                
00002e19  DL_DMA_initChannel                   
00002abf  DL_I2C_fillControllerTXFIFO          
00003aa7  DL_I2C_setClockConfig                
000016dd  DL_SYSCTL_configSYSPLL               
00003101  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001229  DL_Timer_initFourCCPWMMode           
00001511  DL_Timer_initTimerMode               
00003d65  DL_Timer_setCaptCompUpdateMethod     
00004071  DL_Timer_setCaptureCompareOutCtl     
000043e9  DL_Timer_setCaptureCompareValue      
00003d81  DL_Timer_setClockConfig              
00002c2d  DL_UART_drainRXFIFO                  
00003029  DL_UART_init                         
00004391  DL_UART_setClockConfig               
000044b5  DMA_IRQHandler                       
000044b5  Default_Handler                      
202002dd  Digtal                               
0000253d  Encoder_Get                          
00004211  Encoder_Init                         
000044b5  GROUP0_IRQHandler                    
00000dd1  GROUP1_IRQHandler                    
00001891  Get_Analog_value                     
00003385  Get_Anolog_Value                     
00004429  Get_Digtal_For_User                  
00003563  Get_Normalize_For_User               
000044b5  HardFault_Handler                    
000044b5  I2C0_IRQHandler                      
000044b5  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
00001a31  Key_Proc                             
0000244d  Key_Read                             
20200320  Key_Up                               
20200321  Key_Val                              
000044b5  NMI_Handler                          
000009e9  No_MCU_Ganv_Sensor_Init              
00002715  No_MCU_Ganv_Sensor_Init_Frist        
000031cd  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
000027f3  OLED_Clear                           
0000100d  OLED_Init                            
000033c1  OLED_Set_Pos                         
00000ef5  OLED_ShowChar                        
0000132d  OLED_ShowNum                         
00001f09  OLED_WR_Byte                         
00001961  Oled_Task                            
000020b9  PID_init                             
00000ca9  PID_speed_realize                    
000044b5  PendSV_Handler                       
00001dc1  Question_Task_1                      
000009e7  Question_Task_2                      
00001423  Question_Task_3                      
00001dbf  Question_Task_4                      
000044b5  RTC_IRQHandler                       
000044b9  Reset_Handler                        
000044b5  SPI0_IRQHandler                      
000044b5  SPI1_IRQHandler                      
000044b5  SVC_Handler                          
00002eb1  SYSCFG_DL_ADC1_init                  
00004131  SYSCFG_DL_DMA_WIT_init               
00002699  SYSCFG_DL_DMA_init                   
00003671  SYSCFG_DL_FOR_CONTROL_init           
000006b1  SYSCFG_DL_GPIO_init                  
00002bd5  SYSCFG_DL_I2C_OLED_init              
00001fa1  SYSCFG_DL_PWM_MOTOR_init             
0000285d  SYSCFG_DL_PWM_SERVO_init             
000033fd  SYSCFG_DL_SYSCTL_init                
00002c81  SYSCFG_DL_UART_0_init                
000028c5  SYSCFG_DL_UART_WIT_init              
00003145  SYSCFG_DL_init                       
00001bb9  SYSCFG_DL_initPower                  
00004465  Scheduler_Init                       
000025b1  Scheduler_Run                        
00002355  Servo_SetAngle                       
00004493  Servo_SetCenter                      
00004345  Servo_init                           
202002e0  State_Machine                        
00004359  State_Machine_init                   
000043f9  SysTick_Handler                      
00004149  SysTick_Init                         
000044b5  TIMA0_IRQHandler                     
000044b5  TIMA1_IRQHandler                     
00002141  TIMG0_IRQHandler                     
000044b5  TIMG12_IRQHandler                    
000044b5  TIMG6_IRQHandler                     
000044b5  TIMG7_IRQHandler                     
000044b5  TIMG8_IRQHandler                     
000043a3  TI_memcpy_small                      
00004455  TI_memset_small                      
00004227  Timer_Init                           
00004471  Tracing_Control                      
00000b71  Tracing_Value_Get                    
00003189  UART0_IRQHandler                     
000044b5  UART1_IRQHandler                     
000000c1  UART2_IRQHandler                     
000044b5  UART3_IRQHandler                     
000024c5  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004e3c  __TI_CINIT_Base                      
00004e4c  __TI_CINIT_Limit                     
00004e4c  __TI_CINIT_Warm                      
00004e28  __TI_Handler_Table_Base              
00004e34  __TI_Handler_Table_Limit             
000034b1  __TI_auto_init_nobinit_nopinit       
000023d1  __TI_decompress_lzss                 
000043b5  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
0000042d  __TI_printfi_minimal                 
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000423d  __TI_zero_init_nomemset              
0000085f  __adddf3                             
000017c3  __addsf3                             
000026a1  __aeabi_d2f                          
00002f49  __aeabi_d2iz                         
0000085f  __aeabi_dadd                         
000029f9  __aeabi_dcmpeq                       
00002a35  __aeabi_dcmpge                       
00002a49  __aeabi_dcmpgt                       
00002a21  __aeabi_dcmple                       
00002a0d  __aeabi_dcmplt                       
0000111d  __aeabi_ddiv                         
000015f9  __aeabi_dmul                         
00000855  __aeabi_dsub                         
00003291  __aeabi_f2d                          
0000359d  __aeabi_f2iz                         
000017c3  __aeabi_fadd                         
00002a5d  __aeabi_fcmpeq                       
00002a99  __aeabi_fcmpge                       
00002aad  __aeabi_fcmpgt                       
00002a85  __aeabi_fcmple                       
00002a71  __aeabi_fcmplt                       
000022d1  __aeabi_fdiv                         
0000202d  __aeabi_fmul                         
000017b9  __aeabi_fsub                         
0000386d  __aeabi_i2d                          
00003439  __aeabi_i2f                          
00002353  __aeabi_idiv0                        
0000447d  __aeabi_memclr                       
0000447d  __aeabi_memclr4                      
0000447d  __aeabi_memclr8                      
0000449d  __aeabi_memcpy                       
0000449d  __aeabi_memcpy4                      
0000449d  __aeabi_memcpy8                      
00004439  __aeabi_memset                       
00004439  __aeabi_memset4                      
00004439  __aeabi_memset8                      
00003b15  __aeabi_ui2d                         
00003251  __aeabi_uidiv                        
00003251  __aeabi_uidivmod                     
ffffffff  __binit__                            
0000292d  __cmpdf2                             
000034ed  __cmpsf2                             
0000111d  __divdf3                             
000022d1  __divsf3                             
0000292d  __eqdf2                              
000034ed  __eqsf2                              
00003291  __extendsfdf2                        
00002f49  __fixdfsi                            
0000359d  __fixsfsi                            
0000386d  __floatsidf                          
00003439  __floatsisf                          
00003b15  __floatunsidf                        
00002625  __gedf2                              
00003475  __gesf2                              
00002625  __gtdf2                              
00003475  __gtsf2                              
0000292d  __ledf2                              
000034ed  __lesf2                              
0000292d  __ltdf2                              
000034ed  __ltsf2                              
UNDEFED   __mpu_init                           
000015f9  __muldf3                             
00003529  __muldsi3                            
0000202d  __mulsf3                             
0000292d  __nedf2                              
000034ed  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000855  __subdf3                             
000017b9  __subsf3                             
000026a1  __truncdfsf2                         
00003a0d  _c_int00_noargs                      
20200328  _ftable                              
202004ec  _lock                                
00002a5b  _nop                                 
UNDEFED   _system_post_cinit                   
000044cd  _system_pre_init                     
202004f0  _unlock                              
000044d1  abort                                
00002f93  adc_getValue                         
2020021c  angle_pid                            
00004ad0  asc2_0806                            
000044e0  asc2_1608                            
202004f4  bee_time                             
ffffffff  binit                                
202004cc  black                                
20200504  circle_num                           
00002787  convertAnalogToDigital               
00004409  delay_ms                             
00001425  detect_trace_state_change            
202004fc  encoder_A_count                      
202004fe  encoder_B_count                      
20200500  encoder_count                        
20200508  encoder_count_flag                   
00003899  fputc                                
00002dc9  fputs                                
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
00003071  gray_init                            
00001e69  gray_task                            
20200322  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
00002fdd  main                                 
00003b39  memccpy                              
00001af5  motor_direction                      
00003705  mspm0_delay_ms                       
000038c5  mspm0_get_clock_ms                   
00001d15  normalizeAnalogValues                
00002995  oled_i2c_sda_unlock                  
00003735  oled_pow                             
00003b9d  pid_set_speed_target                 
00002b79  printf                               
00001c69  pwm_set                              
20200509  q1_first_flag                        
20200000  sensor                               
00004419  servo_tracing                        
20200244  speedA_pid                           
2020026c  speedB_pid                           
000038f1  speed_control                        
2020030c  start_time                           
20200323  task_num                             
20200310  tick_ms                              
20200314  time_last                            
20200294  tracing_pid                          
20200318  tracing_val                          
20200418  uart_rx_buffer                       
20200324  uart_rx_index                        
2020031c  uart_rx_ticks                        
00002cd5  uart_task                            
202004dc  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  UART2_IRQHandler                     
00000200  __STACK_SIZE                         
0000042d  __TI_printfi_minimal                 
000006b1  SYSCFG_DL_GPIO_init                  
00000855  __aeabi_dsub                         
00000855  __subdf3                             
0000085f  __adddf3                             
0000085f  __aeabi_dadd                         
000009e7  Question_Task_2                      
000009e9  No_MCU_Ganv_Sensor_Init              
00000b71  Tracing_Value_Get                    
00000ca9  PID_speed_realize                    
00000dd1  GROUP1_IRQHandler                    
00000ef5  OLED_ShowChar                        
0000100d  OLED_Init                            
0000111d  __aeabi_ddiv                         
0000111d  __divdf3                             
00001229  DL_Timer_initFourCCPWMMode           
0000132d  OLED_ShowNum                         
00001423  Question_Task_3                      
00001425  detect_trace_state_change            
00001511  DL_Timer_initTimerMode               
000015f9  __aeabi_dmul                         
000015f9  __muldf3                             
000016dd  DL_SYSCTL_configSYSPLL               
000017b9  __aeabi_fsub                         
000017b9  __subsf3                             
000017c3  __addsf3                             
000017c3  __aeabi_fadd                         
00001891  Get_Analog_value                     
00001961  Oled_Task                            
00001a31  Key_Proc                             
00001af5  motor_direction                      
00001bb9  SYSCFG_DL_initPower                  
00001c69  pwm_set                              
00001d15  normalizeAnalogValues                
00001dbf  Question_Task_4                      
00001dc1  Question_Task_1                      
00001e69  gray_task                            
00001f09  OLED_WR_Byte                         
00001fa1  SYSCFG_DL_PWM_MOTOR_init             
0000202d  __aeabi_fmul                         
0000202d  __mulsf3                             
000020b9  PID_init                             
00002141  TIMG0_IRQHandler                     
000022d1  __aeabi_fdiv                         
000022d1  __divsf3                             
00002353  __aeabi_idiv0                        
00002355  Servo_SetAngle                       
000023d1  __TI_decompress_lzss                 
0000244d  Key_Read                             
000024c5  WIT_Get_Relative_Yaw                 
0000253d  Encoder_Get                          
000025b1  Scheduler_Run                        
00002625  __gedf2                              
00002625  __gtdf2                              
00002699  SYSCFG_DL_DMA_init                   
000026a1  __aeabi_d2f                          
000026a1  __truncdfsf2                         
00002715  No_MCU_Ganv_Sensor_Init_Frist        
00002787  convertAnalogToDigital               
000027f3  OLED_Clear                           
0000285d  SYSCFG_DL_PWM_SERVO_init             
000028c5  SYSCFG_DL_UART_WIT_init              
0000292d  __cmpdf2                             
0000292d  __eqdf2                              
0000292d  __ledf2                              
0000292d  __ltdf2                              
0000292d  __nedf2                              
00002995  oled_i2c_sda_unlock                  
000029f9  __aeabi_dcmpeq                       
00002a0d  __aeabi_dcmplt                       
00002a21  __aeabi_dcmple                       
00002a35  __aeabi_dcmpge                       
00002a49  __aeabi_dcmpgt                       
00002a5b  _nop                                 
00002a5d  __aeabi_fcmpeq                       
00002a71  __aeabi_fcmplt                       
00002a85  __aeabi_fcmple                       
00002a99  __aeabi_fcmpge                       
00002aad  __aeabi_fcmpgt                       
00002abf  DL_I2C_fillControllerTXFIFO          
00002b79  printf                               
00002bd5  SYSCFG_DL_I2C_OLED_init              
00002c2d  DL_UART_drainRXFIFO                  
00002c81  SYSCFG_DL_UART_0_init                
00002cd5  uart_task                            
00002dc9  fputs                                
00002e19  DL_DMA_initChannel                   
00002eb1  SYSCFG_DL_ADC1_init                  
00002f49  __aeabi_d2iz                         
00002f49  __fixdfsi                            
00002f93  adc_getValue                         
00002fdd  main                                 
00003029  DL_UART_init                         
00003071  gray_init                            
00003101  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003145  SYSCFG_DL_init                       
00003189  UART0_IRQHandler                     
000031cd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003211  DL_ADC12_setClockConfig              
00003251  __aeabi_uidiv                        
00003251  __aeabi_uidivmod                     
00003291  __aeabi_f2d                          
00003291  __extendsfdf2                        
00003385  Get_Anolog_Value                     
000033c1  OLED_Set_Pos                         
000033fd  SYSCFG_DL_SYSCTL_init                
00003439  __aeabi_i2f                          
00003439  __floatsisf                          
00003475  __gesf2                              
00003475  __gtsf2                              
000034b1  __TI_auto_init_nobinit_nopinit       
000034ed  __cmpsf2                             
000034ed  __eqsf2                              
000034ed  __lesf2                              
000034ed  __ltsf2                              
000034ed  __nesf2                              
00003529  __muldsi3                            
00003563  Get_Normalize_For_User               
0000359d  __aeabi_f2iz                         
0000359d  __fixsfsi                            
000035d5  Beep_Time_Control                    
00003671  SYSCFG_DL_FOR_CONTROL_init           
00003705  mspm0_delay_ms                       
00003735  oled_pow                             
0000386d  __aeabi_i2d                          
0000386d  __floatsidf                          
00003899  fputc                                
000038c5  mspm0_get_clock_ms                   
000038f1  speed_control                        
00003a0d  _c_int00_noargs                      
00003aa7  DL_I2C_setClockConfig                
00003b15  __aeabi_ui2d                         
00003b15  __floatunsidf                        
00003b39  memccpy                              
00003b9d  pid_set_speed_target                 
00003d65  DL_Timer_setCaptCompUpdateMethod     
00003d81  DL_Timer_setClockConfig              
00004071  DL_Timer_setCaptureCompareOutCtl     
00004131  SYSCFG_DL_DMA_WIT_init               
00004149  SysTick_Init                         
00004211  Encoder_Init                         
00004227  Timer_Init                           
0000423d  __TI_zero_init_nomemset              
00004345  Servo_init                           
00004359  State_Machine_init                   
00004391  DL_UART_setClockConfig               
000043a3  TI_memcpy_small                      
000043b5  __TI_decompress_none                 
000043e9  DL_Timer_setCaptureCompareValue      
000043f9  SysTick_Handler                      
00004409  delay_ms                             
00004419  servo_tracing                        
00004429  Get_Digtal_For_User                  
00004439  __aeabi_memset                       
00004439  __aeabi_memset4                      
00004439  __aeabi_memset8                      
00004455  TI_memset_small                      
00004465  Scheduler_Init                       
00004471  Tracing_Control                      
0000447d  __aeabi_memclr                       
0000447d  __aeabi_memclr4                      
0000447d  __aeabi_memclr8                      
00004489  DL_Common_delayCycles                
00004493  Servo_SetCenter                      
0000449d  __aeabi_memcpy                       
0000449d  __aeabi_memcpy4                      
0000449d  __aeabi_memcpy8                      
000044b5  ADC0_IRQHandler                      
000044b5  ADC1_IRQHandler                      
000044b5  AES_IRQHandler                       
000044b5  CANFD0_IRQHandler                    
000044b5  DAC0_IRQHandler                      
000044b5  DMA_IRQHandler                       
000044b5  Default_Handler                      
000044b5  GROUP0_IRQHandler                    
000044b5  HardFault_Handler                    
000044b5  I2C0_IRQHandler                      
000044b5  I2C1_IRQHandler                      
000044b5  NMI_Handler                          
000044b5  PendSV_Handler                       
000044b5  RTC_IRQHandler                       
000044b5  SPI0_IRQHandler                      
000044b5  SPI1_IRQHandler                      
000044b5  SVC_Handler                          
000044b5  TIMA0_IRQHandler                     
000044b5  TIMA1_IRQHandler                     
000044b5  TIMG12_IRQHandler                    
000044b5  TIMG6_IRQHandler                     
000044b5  TIMG7_IRQHandler                     
000044b5  TIMG8_IRQHandler                     
000044b5  UART1_IRQHandler                     
000044b5  UART3_IRQHandler                     
000044b9  Reset_Handler                        
000044cd  _system_pre_init                     
000044d0  C$$EXIT                              
000044d1  abort                                
000044e0  asc2_1608                            
00004ad0  asc2_0806                            
00004e28  __TI_Handler_Table_Base              
00004e34  __TI_Handler_Table_Limit             
00004e3c  __TI_CINIT_Base                      
00004e4c  __TI_CINIT_Limit                     
00004e4c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  time_last                            
20200318  tracing_val                          
2020031c  uart_rx_ticks                        
20200320  Key_Up                               
20200321  Key_Val                              
20200322  grayscale_count                      
20200323  task_num                             
20200324  uart_rx_index                        
20200328  _ftable                              
20200418  uart_rx_buffer                       
202004bc  Anolog                               
202004cc  black                                
202004dc  white                                
202004ec  _lock                                
202004f0  _unlock                              
202004f4  bee_time                             
202004fc  encoder_A_count                      
202004fe  encoder_B_count                      
20200500  encoder_count                        
20200504  circle_num                           
20200508  encoder_count_flag                   
20200509  q1_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[268 symbols]
