******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Aug  2 01:52:30 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004c49


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006238  00019dc8  R  X
  SRAM                  20200000   00008000  00000708  000078f8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006238   00006238    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000056e0   000056e0    r-x .text
  000057a0    000057a0    00000a00   00000a00    r-- .rodata
  000061a0    000061a0    00000098   00000098    r-- .cinit
20200000    20200000    0000050b   00000000    rw-
  20200000    20200000    00000321   00000000    rw- .bss
  20200324    20200324    000001e7   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000056e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000152e    00000002     app_question_task.o (.text.Question_Task_2)
                  00001530    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000016b8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000017f4    00000134     pid.o (.text.Tracing_Value_Get)
                  00001928    00000128     pid.o (.text.PID_speed_realize)
                  00001a50    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001b74    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c94    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001dac    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00001ebc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001fc8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000020cc    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  000021c2    00000002     app_question_task.o (.text.Question_Task_3)
                  000021c4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000022ac    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  00002394    000000e8     app_tracing_check.o (.text.detect_trace_state_change)
                  0000247c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002560    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000263c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002714    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000027ec    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  000028bc    000000c4     key.o (.text.Key_Proc)
                  00002980    000000c4     motor.o (.text.motor_direction)
                  00002a44    000000b8     interrupt.o (.text.TIMG0_IRQHandler)
                  00002afc    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002bac    000000ac     motor.o (.text.pwm_set)
                  00002c58    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002d02    00000002     app_question_task.o (.text.Question_Task_4)
                  00002d04    000000a8     app_question_task.o (.text.Question_Task_1)
                  00002dac    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002e4e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002e50    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002ef0    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002f88    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003014    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000030a0    00000088     pid.o (.text.PID_init)
                  00003128    00000084     clock.o (.text.__NVIC_SetPriority)
                  000031ac    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003230    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000032b2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000032b4    0000007c     servo.o (.text.Servo_SetAngle)
                  00003330    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000033ac    00000078     key.o (.text.Key_Read)
                  00003424    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  0000349c    00000074     encoder.o (.text.Encoder_Get)
                  00003510    00000074     Scheduler.o (.text.Scheduler_Run)
                  00003584    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000035f8    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00003600    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003674    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000036e6    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003752    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  000037bc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00003824    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  0000388c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000038f4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000395a    00000002            : _lock.c.obj (.text._nop)
                  0000395c    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  000039c0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003a22    00000002     --HOLE-- [fill = 0]
                  00003a24    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003a86    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003ae4    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003b40    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003b9c    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003bf8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003c50    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003ca8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003d00    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003d56    00000002     --HOLE-- [fill = 0]
                  00003d58    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003dac    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003e00    00000054     usart_app.o (.text.uart_task)
                  00003e54    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003ea6    00000002     --HOLE-- [fill = 0]
                  00003ea8    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003ef8    00000050     clock.o (.text.SysTick_Config)
                  00003f48    00000050     usart_app.o (.text.fputs)
                  00003f98    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003fe4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004030    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  0000407c    0000004c     app_tracing_control.o (.text.Tracing_Control)
                  000040c8    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004112    00000002     --HOLE-- [fill = 0]
                  00004114    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000415e    0000004a     adc_app.o (.text.adc_getValue)
                  000041a8    0000004a     main.o (.text.main)
                  000041f2    00000002     --HOLE-- [fill = 0]
                  000041f4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000423c    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004284    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  000042cc    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004310    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004354    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00004398    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000043da    00000002     --HOLE-- [fill = 0]
                  000043dc    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0000441c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000445c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000449c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000044dc    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004518    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004554    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004590    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000045cc    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  00004608    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004644    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004680    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000046bc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000046f8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004732    00000002     --HOLE-- [fill = 0]
                  00004734    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000476e    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000047a6    00000002     --HOLE-- [fill = 0]
                  000047a8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000047e0    00000034     beep.o (.text.Beep_Time_Control)
                  00004814    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004848    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000487c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000048b0    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  000048e0    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00004910    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004940    00000030     clock.o (.text.mspm0_delay_ms)
                  00004970    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  000049a0    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  000049cc    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  000049f8    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00004a24    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004a50    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004a7c    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004aa8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004ad4    0000002c     usart_app.o (.text.fputc)
                  00004b00    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004b2c    0000002c     app_tracing_control.o (.text.speed_control)
                  00004b58    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004b80    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004ba8    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004bd0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004bf8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004c20    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004c48    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004c70    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004c96    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004cbc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004ce2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004d08    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004d2c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004d50    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004d74    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004d98    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004dba    00000002     --HOLE-- [fill = 0]
                  00004dbc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004ddc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004dfc    00000020     pid.o (.text.pid_set_speed_target)
                  00004e1c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004e3a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004e58    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004e76    00000002     --HOLE-- [fill = 0]
                  00004e78    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004e94    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004eb0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004ecc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004ee8    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004f04    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004f20    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004f3c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004f58    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004f74    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00004f90    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004fac    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004fc8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004fe4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005000    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000501c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005038    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005050    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005068    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005080    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005098    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000050b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000050c8    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000050e0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  000050f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005110    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005128    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005140    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005158    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005170    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005188    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000051a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000051b8    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  000051d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000051e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005200    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005218    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005230    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005248    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005260    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00005278    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005290    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000052a8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000052c0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000052d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000052f0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005308    00000018     servo.o (.text.DL_Timer_startCounter)
                  00005320    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005338    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005350    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005368    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00005380    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005398    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000053b0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  000053c8    00000018     clock.o (.text.SysTick_Init)
                  000053e0    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  000053f6    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  0000540c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005422    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00005438    00000016     key.o (.text.DL_GPIO_readPins)
                  0000544e    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00005464    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000547a    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00005490    00000016     encoder.o (.text.Encoder_Init)
                  000054a6    00000016     interrupt.o (.text.Timer_Init)
                  000054bc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000054d2    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  000054e6    00000014     beep.o (.text.DL_GPIO_clearPins)
                  000054fa    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000550e    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00005522    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005536    00000002     --HOLE-- [fill = 0]
                  00005538    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  0000554c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005560    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005574    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005588    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000559c    00000014     interrupt.o (.text.DL_UART_receiveData)
                  000055b0    00000014     usart_app.o (.text.DL_UART_receiveData)
                  000055c4    00000014     servo.o (.text.Servo_init)
                  000055d8    00000014     app_question_task.o (.text.State_Machine_init)
                  000055ec    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005600    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005614    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00005626    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00005638    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000564a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000565c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000566e    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  0000567e    00000002     --HOLE-- [fill = 0]
                  00005680    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005690    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000056a0    00000010     interrupt.o (.text.SysTick_Handler)
                  000056b0    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  000056c0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000056d0    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  000056de    00000002     --HOLE-- [fill = 0]
                  000056e0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000056ee    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000056fc    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000570a    00000002     --HOLE-- [fill = 0]
                  0000570c    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00005718    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005724    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000572e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005738    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005748    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005752    0000000a     servo.o (.text.Servo_SetCenter)
                  0000575c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005764    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000576c    00000008     libc.a : printf.c.obj (.text._outc)
                  00005774    00000008            : printf.c.obj (.text._outs)
                  0000577c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005780    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005784    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005794    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005798    00000004            : exit.c.obj (.text:abort)
                  0000579c    00000004     --HOLE-- [fill = 0]

.cinit     0    000061a0    00000098     
                  000061a0    0000006f     (.cinit..data.load) [load image, compression = lzss]
                  0000620f    00000001     --HOLE-- [fill = 0]
                  00006210    0000000c     (__TI_handler_table)
                  0000621c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006224    00000010     (__TI_cinit_table)
                  00006234    00000004     --HOLE-- [fill = 0]

.rodata    0    000057a0    00000a00     
                  000057a0    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005d90    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00005fb8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005fc0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000060c1    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  000060c4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000060ec    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006104    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006118    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006129    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  0000613a    00000010     encoder.o (.rodata.encoder_table)
                  0000614a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006159    0000000c     interrupt.o (.rodata.str1.146019215406595515531)
                  00006165    00000001     --HOLE-- [fill = 0]
                  00006166    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006170    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  0000617a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000617c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006184    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  0000618c    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  0000618f    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00006192    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006194    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006196    0000000a     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000321     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:tracing_val)
                  20200318    00000004     (.common:uart_rx_ticks)
                  2020031c    00000001     (.common:Key_Up)
                  2020031d    00000001     (.common:Key_Val)
                  2020031e    00000001     (.common:grayscale_count)
                  2020031f    00000001     (.common:task_num)
                  20200320    00000001     (.common:uart_rx_index)

.data      0    20200324    000001e7     UNINITIALIZED
                  20200324    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200414    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200494    00000024     Scheduler.o (.data.scheduler_task)
                  202004b8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004c8    00000010     Ganv_Grayscale.o (.data.black)
                  202004d8    00000010     Ganv_Grayscale.o (.data.white)
                  202004e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004ec    00000004            : _lock.c.obj (.data._lock)
                  202004f0    00000004            : _lock.c.obj (.data._unlock)
                  202004f4    00000004     beep.o (.data.bee_time)
                  202004f8    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004fc    00000002     encoder.o (.data.encoder_A_count)
                  202004fe    00000002     encoder.o (.data.encoder_B_count)
                  20200500    00000002     encoder.o (.data.encoder_count)
                  20200502    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200503    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200504    00000001     app_question_task.o (.data.circle_num)
                  20200505    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200506    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200507    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200508    00000001     encoder.o (.data.encoder_count_flag)
                  20200509    00000001     app_question_task.o (.data.q1_first_flag)
                  2020050a    00000001     app_question_task.o (.data.turn_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_tracing_check.o            232     0         7      
       app_question_task.o            194     0         23     
       app_tracing_control.o          120     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         546     0         30     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1448    12        0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1776    12        8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2194    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2194    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          772     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         772     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5458    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       147       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22208   2888      1800   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006224 records: 2, size/record: 8, table size: 16
	.data: load addr=000061a0, load size=0000006f bytes, run addr=20200324, run size=000001e7 bytes, compression=lzss
	.bss: load addr=0000621c, load size=00000008 bytes, run addr=20200000, run size=00000321 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006210 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000139d     00005738     00005736   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004c49     00005784     00005780   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000577d  ADC0_IRQHandler                      
0000577d  ADC1_IRQHandler                      
0000577d  AES_IRQHandler                       
202004b8  Anolog                               
000047e1  Beep_Time_Control                    
00005798  C$$EXIT                              
0000577d  CANFD0_IRQHandler                    
0000577d  DAC0_IRQHandler                      
000043dd  DL_ADC12_setClockConfig              
00005725  DL_Common_delayCycles                
00003f99  DL_DMA_initChannel                   
00003a87  DL_I2C_fillControllerTXFIFO          
00004ce3  DL_I2C_setClockConfig                
00002561  DL_SYSCTL_configSYSPLL               
000042cd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001fc9  DL_Timer_initFourCCPWMMode           
000021c5  DL_Timer_initTimerMode               
00004fe5  DL_Timer_setCaptCompUpdateMethod     
000052f1  DL_Timer_setCaptureCompareOutCtl     
00005691  DL_Timer_setCaptureCompareValue      
00005001  DL_Timer_setClockConfig              
00003d59  DL_UART_drainRXFIFO                  
000041f5  DL_UART_init                         
00005639  DL_UART_setClockConfig               
0000577d  DMA_IRQHandler                       
0000577d  Default_Handler                      
202002dd  Digtal                               
0000349d  Encoder_Get                          
00005491  Encoder_Init                         
0000577d  GROUP0_IRQHandler                    
00001a51  GROUP1_IRQHandler                    
000027ed  Get_Analog_value                     
00004591  Get_Anolog_Value                     
000056d1  Get_Digtal_For_User                  
0000476f  Get_Normalize_For_User               
0000577d  HardFault_Handler                    
0000577d  I2C0_IRQHandler                      
0000577d  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
000028bd  Key_Proc                             
000033ad  Key_Read                             
2020031c  Key_Up                               
2020031d  Key_Val                              
0000577d  NMI_Handler                          
00001531  No_MCU_Ganv_Sensor_Init              
00003675  No_MCU_Ganv_Sensor_Init_Frist        
00004399  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
00003753  OLED_Clear                           
00001dad  OLED_Init                            
000045cd  OLED_Set_Pos                         
00001c95  OLED_ShowChar                        
000020cd  OLED_ShowNum                         
00002ef1  OLED_WR_Byte                         
000022ad  Oled_Task                            
000030a1  PID_init                             
00001929  PID_speed_realize                    
0000577d  PendSV_Handler                       
00002d05  Question_Task_1                      
0000152f  Question_Task_2                      
000021c3  Question_Task_3                      
00002d03  Question_Task_4                      
0000577d  RTC_IRQHandler                       
00005781  Reset_Handler                        
0000577d  SPI0_IRQHandler                      
0000577d  SPI1_IRQHandler                      
0000577d  SVC_Handler                          
00004031  SYSCFG_DL_ADC1_init                  
000053b1  SYSCFG_DL_DMA_WIT_init               
000035f9  SYSCFG_DL_DMA_init                   
0000487d  SYSCFG_DL_FOR_CONTROL_init           
000011f9  SYSCFG_DL_GPIO_init                  
00003bf9  SYSCFG_DL_I2C_OLED_init              
00002f89  SYSCFG_DL_PWM_MOTOR_init             
000037bd  SYSCFG_DL_PWM_SERVO_init             
00004609  SYSCFG_DL_SYSCTL_init                
00003dad  SYSCFG_DL_UART_0_init                
00003825  SYSCFG_DL_UART_WIT_init              
00004311  SYSCFG_DL_init                       
00002afd  SYSCFG_DL_initPower                  
0000570d  Scheduler_Init                       
00003511  Scheduler_Run                        
000032b5  Servo_SetAngle                       
00005753  Servo_SetCenter                      
000055c5  Servo_init                           
202002e0  State_Machine                        
000055d9  State_Machine_init                   
000056a1  SysTick_Handler                      
000053c9  SysTick_Init                         
0000577d  TIMA0_IRQHandler                     
0000577d  TIMA1_IRQHandler                     
00002a45  TIMG0_IRQHandler                     
0000577d  TIMG12_IRQHandler                    
0000577d  TIMG6_IRQHandler                     
0000577d  TIMG7_IRQHandler                     
0000577d  TIMG8_IRQHandler                     
0000564b  TI_memcpy_small                      
000056fd  TI_memset_small                      
000054a7  Timer_Init                           
0000407d  Tracing_Control                      
000017f5  Tracing_Value_Get                    
00004355  UART0_IRQHandler                     
0000577d  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
0000577d  UART3_IRQHandler                     
00003425  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006224  __TI_CINIT_Base                      
00006234  __TI_CINIT_Limit                     
00006234  __TI_CINIT_Warm                      
00006210  __TI_Handler_Table_Base              
0000621c  __TI_Handler_Table_Limit             
000046bd  __TI_auto_init_nobinit_nopinit       
00003331  __TI_decompress_lzss                 
0000565d  __TI_decompress_none                 
00003c51  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000054bd  __TI_zero_init_nomemset              
000013a7  __adddf3                             
0000271f  __addsf3                             
00005fc0  __aeabi_ctype_table_                 
00005fc0  __aeabi_ctype_table_C                
00003601  __aeabi_d2f                          
00004115  __aeabi_d2iz                         
000013a7  __aeabi_dadd                         
000039c1  __aeabi_dcmpeq                       
000039fd  __aeabi_dcmpge                       
00003a11  __aeabi_dcmpgt                       
000039e9  __aeabi_dcmple                       
000039d5  __aeabi_dcmplt                       
00001ebd  __aeabi_ddiv                         
0000247d  __aeabi_dmul                         
0000139d  __aeabi_dsub                         
202004e8  __aeabi_errno                        
0000575d  __aeabi_errno_addr                   
0000445d  __aeabi_f2d                          
000047a9  __aeabi_f2iz                         
0000271f  __aeabi_fadd                         
00003a25  __aeabi_fcmpeq                       
00003a61  __aeabi_fcmpge                       
00003a75  __aeabi_fcmpgt                       
00003a4d  __aeabi_fcmple                       
00003a39  __aeabi_fcmplt                       
00003231  __aeabi_fdiv                         
00003015  __aeabi_fmul                         
00002715  __aeabi_fsub                         
00004aa9  __aeabi_i2d                          
00004645  __aeabi_i2f                          
00003d01  __aeabi_idiv                         
00002e4f  __aeabi_idiv0                        
00003d01  __aeabi_idivmod                      
000032b3  __aeabi_ldiv0                        
00004e59  __aeabi_llsl                         
00004d75  __aeabi_lmul                         
00005719  __aeabi_memclr                       
00005719  __aeabi_memclr4                      
00005719  __aeabi_memclr8                      
00005765  __aeabi_memcpy                       
00005765  __aeabi_memcpy4                      
00005765  __aeabi_memcpy8                      
000056e1  __aeabi_memset                       
000056e1  __aeabi_memset4                      
000056e1  __aeabi_memset8                      
00004d51  __aeabi_ui2d                         
0000441d  __aeabi_uidiv                        
0000441d  __aeabi_uidivmod                     
000055ed  __aeabi_uldivmod                     
00004e59  __ashldi3                            
ffffffff  __binit__                            
0000388d  __cmpdf2                             
000046f9  __cmpsf2                             
00001ebd  __divdf3                             
00003231  __divsf3                             
0000388d  __eqdf2                              
000046f9  __eqsf2                              
0000445d  __extendsfdf2                        
00004115  __fixdfsi                            
000047a9  __fixsfsi                            
00004aa9  __floatsidf                          
00004645  __floatsisf                          
00004d51  __floatunsidf                        
00003585  __gedf2                              
00004681  __gesf2                              
00003585  __gtdf2                              
00004681  __gtsf2                              
0000388d  __ledf2                              
000046f9  __lesf2                              
0000388d  __ltdf2                              
000046f9  __ltsf2                              
UNDEFED   __mpu_init                           
0000247d  __muldf3                             
00004d75  __muldi3                             
00004735  __muldsi3                            
00003015  __mulsf3                             
0000388d  __nedf2                              
000046f9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000139d  __subdf3                             
00002715  __subsf3                             
00003601  __truncdfsf2                         
00002dad  __udivmoddi4                         
00004c49  _c_int00_noargs                      
20200324  _ftable                              
202004ec  _lock                                
0000395b  _nop                                 
UNDEFED   _system_post_cinit                   
00005795  _system_pre_init                     
202004f0  _unlock                              
00005799  abort                                
0000415f  adc_getValue                         
2020021c  angle_pid                            
00005d90  asc2_0806                            
000057a0  asc2_1608                            
0000449d  atoi                                 
202004f4  bee_time                             
ffffffff  binit                                
202004c8  black                                
20200504  circle_num                           
000036e7  convertAnalogToDigital               
000056b1  delay_ms                             
00002395  detect_trace_state_change            
202004fc  encoder_A_count                      
202004fe  encoder_B_count                      
20200500  encoder_count                        
20200508  encoder_count_flag                   
00004ad5  fputc                                
00003f49  fputs                                
00003ae5  frexp                                
00003ae5  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
0000423d  gray_init                            
00002e51  gray_task                            
2020031e  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
0000263d  ldexp                                
0000263d  ldexpl                               
000041a9  main                                 
00004d99  memccpy                              
00002981  motor_direction                      
00004941  mspm0_delay_ms                       
00004b01  mspm0_get_clock_ms                   
00002c59  normalizeAnalogValues                
0000395d  oled_i2c_sda_unlock                  
00004971  oled_pow                             
00004dfd  pid_set_speed_target                 
00003b9d  printf                               
00002bad  pwm_set                              
20200509  q1_first_flag                        
0000263d  scalbn                               
0000263d  scalbnl                              
20200000  sensor                               
20200244  speedA_pid                           
2020026c  speedB_pid                           
00004b2d  speed_control                        
2020030c  start_time                           
2020031f  task_num                             
20200310  tick_ms                              
20200294  tracing_pid                          
20200314  tracing_val                          
2020050a  turn_num                             
20200414  uart_rx_buffer                       
20200320  uart_rx_index                        
20200318  uart_rx_ticks                        
00003e01  uart_task                            
000056c1  wcslen                               
202004d8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  SYSCFG_DL_GPIO_init                  
0000139d  __aeabi_dsub                         
0000139d  __subdf3                             
000013a7  __adddf3                             
000013a7  __aeabi_dadd                         
0000152f  Question_Task_2                      
00001531  No_MCU_Ganv_Sensor_Init              
000017f5  Tracing_Value_Get                    
00001929  PID_speed_realize                    
00001a51  GROUP1_IRQHandler                    
00001c95  OLED_ShowChar                        
00001dad  OLED_Init                            
00001ebd  __aeabi_ddiv                         
00001ebd  __divdf3                             
00001fc9  DL_Timer_initFourCCPWMMode           
000020cd  OLED_ShowNum                         
000021c3  Question_Task_3                      
000021c5  DL_Timer_initTimerMode               
000022ad  Oled_Task                            
00002395  detect_trace_state_change            
0000247d  __aeabi_dmul                         
0000247d  __muldf3                             
00002561  DL_SYSCTL_configSYSPLL               
0000263d  ldexp                                
0000263d  ldexpl                               
0000263d  scalbn                               
0000263d  scalbnl                              
00002715  __aeabi_fsub                         
00002715  __subsf3                             
0000271f  __addsf3                             
0000271f  __aeabi_fadd                         
000027ed  Get_Analog_value                     
000028bd  Key_Proc                             
00002981  motor_direction                      
00002a45  TIMG0_IRQHandler                     
00002afd  SYSCFG_DL_initPower                  
00002bad  pwm_set                              
00002c59  normalizeAnalogValues                
00002d03  Question_Task_4                      
00002d05  Question_Task_1                      
00002dad  __udivmoddi4                         
00002e4f  __aeabi_idiv0                        
00002e51  gray_task                            
00002ef1  OLED_WR_Byte                         
00002f89  SYSCFG_DL_PWM_MOTOR_init             
00003015  __aeabi_fmul                         
00003015  __mulsf3                             
000030a1  PID_init                             
00003231  __aeabi_fdiv                         
00003231  __divsf3                             
000032b3  __aeabi_ldiv0                        
000032b5  Servo_SetAngle                       
00003331  __TI_decompress_lzss                 
000033ad  Key_Read                             
00003425  WIT_Get_Relative_Yaw                 
0000349d  Encoder_Get                          
00003511  Scheduler_Run                        
00003585  __gedf2                              
00003585  __gtdf2                              
000035f9  SYSCFG_DL_DMA_init                   
00003601  __aeabi_d2f                          
00003601  __truncdfsf2                         
00003675  No_MCU_Ganv_Sensor_Init_Frist        
000036e7  convertAnalogToDigital               
00003753  OLED_Clear                           
000037bd  SYSCFG_DL_PWM_SERVO_init             
00003825  SYSCFG_DL_UART_WIT_init              
0000388d  __cmpdf2                             
0000388d  __eqdf2                              
0000388d  __ledf2                              
0000388d  __ltdf2                              
0000388d  __nedf2                              
0000395b  _nop                                 
0000395d  oled_i2c_sda_unlock                  
000039c1  __aeabi_dcmpeq                       
000039d5  __aeabi_dcmplt                       
000039e9  __aeabi_dcmple                       
000039fd  __aeabi_dcmpge                       
00003a11  __aeabi_dcmpgt                       
00003a25  __aeabi_fcmpeq                       
00003a39  __aeabi_fcmplt                       
00003a4d  __aeabi_fcmple                       
00003a61  __aeabi_fcmpge                       
00003a75  __aeabi_fcmpgt                       
00003a87  DL_I2C_fillControllerTXFIFO          
00003ae5  frexp                                
00003ae5  frexpl                               
00003b9d  printf                               
00003bf9  SYSCFG_DL_I2C_OLED_init              
00003c51  __TI_ltoa                            
00003d01  __aeabi_idiv                         
00003d01  __aeabi_idivmod                      
00003d59  DL_UART_drainRXFIFO                  
00003dad  SYSCFG_DL_UART_0_init                
00003e01  uart_task                            
00003f49  fputs                                
00003f99  DL_DMA_initChannel                   
00004031  SYSCFG_DL_ADC1_init                  
0000407d  Tracing_Control                      
00004115  __aeabi_d2iz                         
00004115  __fixdfsi                            
0000415f  adc_getValue                         
000041a9  main                                 
000041f5  DL_UART_init                         
0000423d  gray_init                            
000042cd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004311  SYSCFG_DL_init                       
00004355  UART0_IRQHandler                     
00004399  No_Mcu_Ganv_Sensor_Task_Without_tick 
000043dd  DL_ADC12_setClockConfig              
0000441d  __aeabi_uidiv                        
0000441d  __aeabi_uidivmod                     
0000445d  __aeabi_f2d                          
0000445d  __extendsfdf2                        
0000449d  atoi                                 
00004591  Get_Anolog_Value                     
000045cd  OLED_Set_Pos                         
00004609  SYSCFG_DL_SYSCTL_init                
00004645  __aeabi_i2f                          
00004645  __floatsisf                          
00004681  __gesf2                              
00004681  __gtsf2                              
000046bd  __TI_auto_init_nobinit_nopinit       
000046f9  __cmpsf2                             
000046f9  __eqsf2                              
000046f9  __lesf2                              
000046f9  __ltsf2                              
000046f9  __nesf2                              
00004735  __muldsi3                            
0000476f  Get_Normalize_For_User               
000047a9  __aeabi_f2iz                         
000047a9  __fixsfsi                            
000047e1  Beep_Time_Control                    
0000487d  SYSCFG_DL_FOR_CONTROL_init           
00004941  mspm0_delay_ms                       
00004971  oled_pow                             
00004aa9  __aeabi_i2d                          
00004aa9  __floatsidf                          
00004ad5  fputc                                
00004b01  mspm0_get_clock_ms                   
00004b2d  speed_control                        
00004c49  _c_int00_noargs                      
00004ce3  DL_I2C_setClockConfig                
00004d51  __aeabi_ui2d                         
00004d51  __floatunsidf                        
00004d75  __aeabi_lmul                         
00004d75  __muldi3                             
00004d99  memccpy                              
00004dfd  pid_set_speed_target                 
00004e59  __aeabi_llsl                         
00004e59  __ashldi3                            
00004fe5  DL_Timer_setCaptCompUpdateMethod     
00005001  DL_Timer_setClockConfig              
000052f1  DL_Timer_setCaptureCompareOutCtl     
000053b1  SYSCFG_DL_DMA_WIT_init               
000053c9  SysTick_Init                         
00005491  Encoder_Init                         
000054a7  Timer_Init                           
000054bd  __TI_zero_init_nomemset              
000055c5  Servo_init                           
000055d9  State_Machine_init                   
000055ed  __aeabi_uldivmod                     
00005639  DL_UART_setClockConfig               
0000564b  TI_memcpy_small                      
0000565d  __TI_decompress_none                 
00005691  DL_Timer_setCaptureCompareValue      
000056a1  SysTick_Handler                      
000056b1  delay_ms                             
000056c1  wcslen                               
000056d1  Get_Digtal_For_User                  
000056e1  __aeabi_memset                       
000056e1  __aeabi_memset4                      
000056e1  __aeabi_memset8                      
000056fd  TI_memset_small                      
0000570d  Scheduler_Init                       
00005719  __aeabi_memclr                       
00005719  __aeabi_memclr4                      
00005719  __aeabi_memclr8                      
00005725  DL_Common_delayCycles                
00005753  Servo_SetCenter                      
0000575d  __aeabi_errno_addr                   
00005765  __aeabi_memcpy                       
00005765  __aeabi_memcpy4                      
00005765  __aeabi_memcpy8                      
0000577d  ADC0_IRQHandler                      
0000577d  ADC1_IRQHandler                      
0000577d  AES_IRQHandler                       
0000577d  CANFD0_IRQHandler                    
0000577d  DAC0_IRQHandler                      
0000577d  DMA_IRQHandler                       
0000577d  Default_Handler                      
0000577d  GROUP0_IRQHandler                    
0000577d  HardFault_Handler                    
0000577d  I2C0_IRQHandler                      
0000577d  I2C1_IRQHandler                      
0000577d  NMI_Handler                          
0000577d  PendSV_Handler                       
0000577d  RTC_IRQHandler                       
0000577d  SPI0_IRQHandler                      
0000577d  SPI1_IRQHandler                      
0000577d  SVC_Handler                          
0000577d  TIMA0_IRQHandler                     
0000577d  TIMA1_IRQHandler                     
0000577d  TIMG12_IRQHandler                    
0000577d  TIMG6_IRQHandler                     
0000577d  TIMG7_IRQHandler                     
0000577d  TIMG8_IRQHandler                     
0000577d  UART1_IRQHandler                     
0000577d  UART3_IRQHandler                     
00005781  Reset_Handler                        
00005795  _system_pre_init                     
00005798  C$$EXIT                              
00005799  abort                                
000057a0  asc2_1608                            
00005d90  asc2_0806                            
00005fc0  __aeabi_ctype_table_                 
00005fc0  __aeabi_ctype_table_C                
00006210  __TI_Handler_Table_Base              
0000621c  __TI_Handler_Table_Limit             
00006224  __TI_CINIT_Base                      
00006234  __TI_CINIT_Limit                     
00006234  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  tracing_val                          
20200318  uart_rx_ticks                        
2020031c  Key_Up                               
2020031d  Key_Val                              
2020031e  grayscale_count                      
2020031f  task_num                             
20200320  uart_rx_index                        
20200324  _ftable                              
20200414  uart_rx_buffer                       
202004b8  Anolog                               
202004c8  black                                
202004d8  white                                
202004e8  __aeabi_errno                        
202004ec  _lock                                
202004f0  _unlock                              
202004f4  bee_time                             
202004fc  encoder_A_count                      
202004fe  encoder_B_count                      
20200500  encoder_count                        
20200504  circle_num                           
20200508  encoder_count_flag                   
20200509  q1_first_flag                        
2020050a  turn_num                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[289 symbols]
