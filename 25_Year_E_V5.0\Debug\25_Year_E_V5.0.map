******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 11:23:09 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004b61


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000061a8  00019e58  R  X
  SRAM                  20200000   00008000  0000071d  000078e3  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000061a8   000061a8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005660   00005660    r-x .text
  00005720    00005720    000009f0   000009f0    r-- .rodata
  00006110    00006110    00000098   00000098    r-- .cinit
20200000    20200000    00000520   00000000    rw-
  20200000    20200000    00000339   00000000    rw- .bss
  2020033c    2020033c    000001e4   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005660     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000152e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001530    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000016b8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000017f4    00000138     pid.o (.text.Tracing_Value_Get)
                  0000192c    00000128     pid.o (.text.PID_speed_realize)
                  00001a54    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001b78    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c98    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001db0    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00001ec0    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  00001fd0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000020dc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000021e0    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  000022d6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000022d8    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  000023c4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000024ac    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002590    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000266c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002744    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000281c    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  000028ec    000000c4     motor.o (.text.motor_direction)
                  000029b0    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002a60    000000ac     motor.o (.text.pwm_set)
                  00002b0c    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002bb6    00000002     libc.a : _lock.c.obj (.text._nop)
                  00002bb8    000000a8     key.o (.text.Key_Proc)
                  00002c60    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002d02    00000002     --HOLE-- [fill = 0]
                  00002d04    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002da4    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002e3c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00002ec8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002f54    00000088     pid.o (.text.PID_init)
                  00002fdc    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003060    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000030e4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003166    00000002     --HOLE-- [fill = 0]
                  00003168    0000007c     servo.o (.text.Servo_SetAngle)
                  000031e4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003260    00000078     key.o (.text.Key_Read)
                  000032d8    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  00003350    00000074     encoder.o (.text.Encoder_Get)
                  000033c4    00000074     Scheduler.o (.text.Scheduler_Run)
                  00003438    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000034ac    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000034b0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003524    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003596    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00003606    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003672    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  000036dc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00003744    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000037ac    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003814    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000387a    00000002     --HOLE-- [fill = 0]
                  0000387c    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  000038e0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003942    00000002     --HOLE-- [fill = 0]
                  00003944    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000039a6    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003a04    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003a60    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003abc    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003b18    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003b70    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003bc8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003c20    00000058     main.o (.text.main)
                  00003c78    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003cce    00000002     --HOLE-- [fill = 0]
                  00003cd0    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003d24    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003d78    00000054     usart_app.o (.text.uart_task)
                  00003dcc    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003e1e    00000002     --HOLE-- [fill = 0]
                  00003e20    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003e70    00000050     clock.o (.text.SysTick_Config)
                  00003ec0    00000050     usart_app.o (.text.fputs)
                  00003f10    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003f5c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003fa8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003ff4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000403e    00000002     --HOLE-- [fill = 0]
                  00004040    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000408a    0000004a     adc_app.o (.text.adc_getValue)
                  000040d4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000411c    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004164    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  000041ac    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000041f0    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004234    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00004278    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000042ba    00000002     --HOLE-- [fill = 0]
                  000042bc    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000042fc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000433c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000437c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000043bc    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000043f8    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004434    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004470    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000044ac    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000044e8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004524    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004560    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000459c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000045d8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004612    00000002     --HOLE-- [fill = 0]
                  00004614    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000464e    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  00004686    00000002     --HOLE-- [fill = 0]
                  00004688    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000046c0    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000046f8    00000034     beep.o (.text.Beep_Time_Control)
                  0000472c    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004760    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004794    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000047c8    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  000047f8    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00004828    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004858    00000030     clock.o (.text.mspm0_delay_ms)
                  00004888    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  000048b8    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  000048e4    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  00004910    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  0000493c    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004968    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004994    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  000049c0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000049ec    0000002c     usart_app.o (.text.fputc)
                  00004a18    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004a44    0000002c     app_tracing_control.o (.text.speed_control)
                  00004a70    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004a98    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004ac0    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004ae8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004b10    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004b38    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004b60    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004b88    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004bae    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004bd4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004bfa    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004c20    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004c44    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004c68    00000024     interrupt.o (.text.TIMG0_IRQHandler)
                  00004c8c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004cb0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004cd4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004cf6    00000002     --HOLE-- [fill = 0]
                  00004cf8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004d18    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004d38    00000020     pid.o (.text.pid_set_speed_target)
                  00004d58    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004d76    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004d94    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004db2    00000002     --HOLE-- [fill = 0]
                  00004db4    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004dd0    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004dec    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004e08    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004e24    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004e40    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004e5c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004e78    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004e94    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004eb0    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00004ecc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004ee8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004f04    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004f20    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004f3c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004f58    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004f74    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004f8c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00004fa4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00004fbc    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00004fd4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004fec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005004    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000501c    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00005034    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000504c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005064    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  0000507c    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005094    00000018     motor.o (.text.DL_GPIO_setPins)
                  000050ac    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  000050c4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000050dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000050f4    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  0000510c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005124    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000513c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005154    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  0000516c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005184    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  0000519c    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  000051b4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000051cc    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000051e4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000051fc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005214    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000522c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005244    00000018     servo.o (.text.DL_Timer_startCounter)
                  0000525c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005274    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000528c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000052a4    00000018     usart_app.o (.text.DL_UART_isBusy)
                  000052bc    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000052d4    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000052ec    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005304    00000018     clock.o (.text.SysTick_Init)
                  0000531c    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00005334    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  0000534a    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  00005360    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005376    00000016     encoder.o (.text.DL_GPIO_readPins)
                  0000538c    00000016     key.o (.text.DL_GPIO_readPins)
                  000053a2    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  000053b8    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000053ce    00000016     usart_app.o (.text.DL_UART_transmitData)
                  000053e4    00000016     encoder.o (.text.Encoder_Init)
                  000053fa    00000016     interrupt.o (.text.Timer_Init)
                  00005410    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005426    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  0000543a    00000014     beep.o (.text.DL_GPIO_clearPins)
                  0000544e    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00005462    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00005476    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000548a    00000002     --HOLE-- [fill = 0]
                  0000548c    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  000054a0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000054b4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000054c8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000054dc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000054f0    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005504    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005518    00000014     servo.o (.text.Servo_init)
                  0000552c    00000014     app_question_task.o (.text.State_Machine_init)
                  00005540    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005554    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005568    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  0000557a    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  0000558c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000559e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000055b0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000055c2    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000055d2    00000002     --HOLE-- [fill = 0]
                  000055d4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000055e4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000055f4    00000010     interrupt.o (.text.SysTick_Handler)
                  00005604    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005614    00000010     app_tracing_control.o (.text.servo_tracing)
                  00005624    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005634    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  00005642    00000002     --HOLE-- [fill = 0]
                  00005644    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005652    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005660    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000566e    00000002     --HOLE-- [fill = 0]
                  00005670    0000000c     Scheduler.o (.text.Scheduler_Init)
                  0000567c    0000000c     app_tracing_control.o (.text.Tracing_Control)
                  00005688    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005694    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000569e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000056a8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000056b8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000056c2    0000000a     servo.o (.text.Servo_SetCenter)
                  000056cc    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  000056d6    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000056de    00000002     --HOLE-- [fill = 0]
                  000056e0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000056e8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000056f0    00000008     libc.a : printf.c.obj (.text._outc)
                  000056f8    00000008            : printf.c.obj (.text._outs)
                  00005700    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005704    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005714    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005718    00000004            : exit.c.obj (.text:abort)
                  0000571c    00000004     --HOLE-- [fill = 0]

.cinit     0    00006110    00000098     
                  00006110    0000006f     (.cinit..data.load) [load image, compression = lzss]
                  0000617f    00000001     --HOLE-- [fill = 0]
                  00006180    0000000c     (__TI_handler_table)
                  0000618c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006194    00000010     (__TI_cinit_table)
                  000061a4    00000004     --HOLE-- [fill = 0]

.rodata    0    00005720    000009f0     
                  00005720    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005d10    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00005f38    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005f40    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006041    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006044    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000606c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006084    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006098    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000060a9    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000060ba    00000010     encoder.o (.rodata.encoder_table)
                  000060ca    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  000060d9    00000001     --HOLE-- [fill = 0]
                  000060da    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000060e4    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  000060ee    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000060f0    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000060f8    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  00006100    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  00006105    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006108    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  0000610b    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000610d    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  0000610f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000339     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000014     (.common:OLED_String)
                  202002f1    00000001     (.common:Digtal)
                  202002f2    00000001     (.common:Key_Down)
                  202002f3    00000001     (.common:Key_Old)
                  202002f4    00000014     (.common:State_Machine)
                  20200308    00000010     (.common:Normal)
                  20200318    00000008     (.common:grayscale_data)
                  20200320    00000004     (.common:start_time)
                  20200324    00000004     (.common:target_angle)
                  20200328    00000004     (.common:tick_ms)
                  2020032c    00000004     (.common:tracing_val)
                  20200330    00000004     (.common:uart_rx_ticks)
                  20200334    00000001     (.common:Key_Up)
                  20200335    00000001     (.common:Key_Val)
                  20200336    00000001     (.common:grayscale_count)
                  20200337    00000001     (.common:task_num)
                  20200338    00000001     (.common:uart_rx_index)

.data      0    2020033c    000001e4     UNINITIALIZED
                  2020033c    000000f0     libc.a : defs.c.obj (.data._ftable)
                  2020042c    00000080     usart_app.o (.data.uart_rx_buffer)
                  202004ac    00000024     Scheduler.o (.data.scheduler_task)
                  202004d0    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004e0    00000010     Ganv_Grayscale.o (.data.black)
                  202004f0    00000010     Ganv_Grayscale.o (.data.white)
                  20200500    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200504    00000004            : _lock.c.obj (.data._lock)
                  20200508    00000004            : _lock.c.obj (.data._unlock)
                  2020050c    00000004     beep.o (.data.bee_time)
                  20200510    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200514    00000002     encoder.o (.data.encoder_A_count)
                  20200516    00000002     encoder.o (.data.encoder_B_count)
                  20200518    00000002     encoder.o (.data.encoder_count)
                  2020051a    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  2020051b    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  2020051c    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  2020051d    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  2020051e    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  2020051f    00000001     encoder.o (.data.encoder_count_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         176     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3340    335       320    
                                                               
    .\APP\
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          72      0         0      
       app_question_task.o            20      0         24     
    +--+------------------------------+-------+---------+---------+
       Total:                         328     0         31     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          310     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         310     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1300    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1628    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          776     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         776     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       147       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22074   2881      1821   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006194 records: 2, size/record: 8, table size: 16
	.data: load addr=00006110, load size=0000006f bytes, run addr=2020033c, run size=000001e4 bytes, compression=lzss
	.bss: load addr=0000618c, load size=00000008 bytes, run addr=20200000, run size=00000339 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006180 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000139d     000056a8     000056a6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004b61     00005704     00005700   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000034ad  ADC0_IRQHandler                      
000034ad  ADC1_IRQHandler                      
000034ad  AES_IRQHandler                       
202004d0  Anolog                               
000046f9  Beep_Time_Control                    
00005718  C$$EXIT                              
000034ad  CANFD0_IRQHandler                    
000034ad  DAC0_IRQHandler                      
000042bd  DL_ADC12_setClockConfig              
00005695  DL_Common_delayCycles                
00003f11  DL_DMA_initChannel                   
000039a7  DL_I2C_fillControllerTXFIFO          
00004bfb  DL_I2C_setClockConfig                
00002591  DL_SYSCTL_configSYSPLL               
000041ad  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000020dd  DL_Timer_initFourCCPWMMode           
000023c5  DL_Timer_initTimerMode               
00004f21  DL_Timer_setCaptCompUpdateMethod     
0000522d  DL_Timer_setCaptureCompareOutCtl     
000055e5  DL_Timer_setCaptureCompareValue      
00004f3d  DL_Timer_setClockConfig              
00003cd1  DL_UART_drainRXFIFO                  
000040d5  DL_UART_init                         
0000558d  DL_UART_setClockConfig               
000034ad  DMA_IRQHandler                       
000034ad  Default_Handler                      
202002f1  Digtal                               
00003351  Encoder_Get                          
000053e5  Encoder_Init                         
000034ad  GROUP0_IRQHandler                    
00001a55  GROUP1_IRQHandler                    
0000281d  Get_Analog_value                     
00004471  Get_Anolog_Value                     
00005635  Get_Digtal_For_User                  
0000464f  Get_Normalize_For_User               
000034ad  HardFault_Handler                    
000034ad  I2C0_IRQHandler                      
000034ad  I2C1_IRQHandler                      
202002f2  Key_Down                             
202002f3  Key_Old                              
00002bb9  Key_Proc                             
00003261  Key_Read                             
20200334  Key_Up                               
20200335  Key_Val                              
000034ad  NMI_Handler                          
00001531  No_MCU_Ganv_Sensor_Init              
00003525  No_MCU_Ganv_Sensor_Init_Frist        
00004279  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200308  Normal                               
00003673  OLED_Clear                           
00001db1  OLED_Init                            
000044ad  OLED_Set_Pos                         
00001c99  OLED_ShowChar                        
000021e1  OLED_ShowNum                         
00003597  OLED_ShowString                      
202002dd  OLED_String                          
00002da5  OLED_WR_Byte                         
00001ec1  Oled_Task                            
00002f55  PID_init                             
0000192d  PID_speed_realize                    
000034ad  PendSV_Handler                       
000034ad  RTC_IRQHandler                       
00005701  Reset_Handler                        
000034ad  SPI0_IRQHandler                      
000034ad  SPI1_IRQHandler                      
000034ad  SVC_Handler                          
00003fa9  SYSCFG_DL_ADC1_init                  
000052ed  SYSCFG_DL_DMA_WIT_init               
000056d7  SYSCFG_DL_DMA_init                   
00004795  SYSCFG_DL_FOR_CONTROL_init           
000011f9  SYSCFG_DL_GPIO_init                  
00003b19  SYSCFG_DL_I2C_OLED_init              
00002e3d  SYSCFG_DL_PWM_MOTOR_init             
000036dd  SYSCFG_DL_PWM_SERVO_init             
000044e9  SYSCFG_DL_SYSCTL_init                
00003d25  SYSCFG_DL_UART_0_init                
00003745  SYSCFG_DL_UART_WIT_init              
000041f1  SYSCFG_DL_init                       
000029b1  SYSCFG_DL_initPower                  
00005671  Scheduler_Init                       
000033c5  Scheduler_Run                        
00003169  Servo_SetAngle                       
000056c3  Servo_SetCenter                      
00005519  Servo_init                           
202002f4  State_Machine                        
0000552d  State_Machine_init                   
000055f5  SysTick_Handler                      
00005305  SysTick_Init                         
000034ad  TIMA0_IRQHandler                     
000034ad  TIMA1_IRQHandler                     
00004c69  TIMG0_IRQHandler                     
000034ad  TIMG12_IRQHandler                    
000034ad  TIMG6_IRQHandler                     
000034ad  TIMG7_IRQHandler                     
000034ad  TIMG8_IRQHandler                     
0000559f  TI_memcpy_small                      
00005661  TI_memset_small                      
000053fb  Timer_Init                           
0000567d  Tracing_Control                      
000017f5  Tracing_Value_Get                    
00004235  UART0_IRQHandler                     
000034ad  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
000034ad  UART3_IRQHandler                     
000032d9  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006194  __TI_CINIT_Base                      
000061a4  __TI_CINIT_Limit                     
000061a4  __TI_CINIT_Warm                      
00006180  __TI_Handler_Table_Base              
0000618c  __TI_Handler_Table_Limit             
0000459d  __TI_auto_init_nobinit_nopinit       
000031e5  __TI_decompress_lzss                 
000055b1  __TI_decompress_none                 
00003b71  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005411  __TI_zero_init_nomemset              
000013a7  __adddf3                             
0000274f  __addsf3                             
00005f40  __aeabi_ctype_table_                 
00005f40  __aeabi_ctype_table_C                
000034b1  __aeabi_d2f                          
00004041  __aeabi_d2iz                         
000013a7  __aeabi_dadd                         
000038e1  __aeabi_dcmpeq                       
0000391d  __aeabi_dcmpge                       
00003931  __aeabi_dcmpgt                       
00003909  __aeabi_dcmple                       
000038f5  __aeabi_dcmplt                       
00001fd1  __aeabi_ddiv                         
000024ad  __aeabi_dmul                         
0000139d  __aeabi_dsub                         
20200500  __aeabi_errno                        
000056e1  __aeabi_errno_addr                   
0000433d  __aeabi_f2d                          
00004689  __aeabi_f2iz                         
0000274f  __aeabi_fadd                         
00003945  __aeabi_fcmpeq                       
00003981  __aeabi_fcmpge                       
00003995  __aeabi_fcmpgt                       
0000396d  __aeabi_fcmple                       
00003959  __aeabi_fcmplt                       
000030e5  __aeabi_fdiv                         
00002ec9  __aeabi_fmul                         
00002745  __aeabi_fsub                         
000049c1  __aeabi_i2d                          
00004525  __aeabi_i2f                          
00003c79  __aeabi_idiv                         
0000152f  __aeabi_idiv0                        
00003c79  __aeabi_idivmod                      
000022d7  __aeabi_ldiv0                        
00004d95  __aeabi_llsl                         
00004cb1  __aeabi_lmul                         
00005689  __aeabi_memclr                       
00005689  __aeabi_memclr4                      
00005689  __aeabi_memclr8                      
000056e9  __aeabi_memcpy                       
000056e9  __aeabi_memcpy4                      
000056e9  __aeabi_memcpy8                      
00005645  __aeabi_memset                       
00005645  __aeabi_memset4                      
00005645  __aeabi_memset8                      
00004c8d  __aeabi_ui2d                         
000042fd  __aeabi_uidiv                        
000042fd  __aeabi_uidivmod                     
00005541  __aeabi_uldivmod                     
00004d95  __ashldi3                            
ffffffff  __binit__                            
000037ad  __cmpdf2                             
000045d9  __cmpsf2                             
00001fd1  __divdf3                             
000030e5  __divsf3                             
000037ad  __eqdf2                              
000045d9  __eqsf2                              
0000433d  __extendsfdf2                        
00004041  __fixdfsi                            
00004689  __fixsfsi                            
000049c1  __floatsidf                          
00004525  __floatsisf                          
00004c8d  __floatunsidf                        
00003439  __gedf2                              
00004561  __gesf2                              
00003439  __gtdf2                              
00004561  __gtsf2                              
000037ad  __ledf2                              
000045d9  __lesf2                              
000037ad  __ltdf2                              
000045d9  __ltsf2                              
UNDEFED   __mpu_init                           
000024ad  __muldf3                             
00004cb1  __muldi3                             
00004615  __muldsi3                            
00002ec9  __mulsf3                             
000037ad  __nedf2                              
000045d9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000139d  __subdf3                             
00002745  __subsf3                             
000034b1  __truncdfsf2                         
00002c61  __udivmoddi4                         
00004b61  _c_int00_noargs                      
2020033c  _ftable                              
20200504  _lock                                
00002bb7  _nop                                 
UNDEFED   _system_post_cinit                   
00005715  _system_pre_init                     
20200508  _unlock                              
00005719  abort                                
0000408b  adc_getValue                         
2020021c  angle_pid                            
00005d10  asc2_0806                            
00005720  asc2_1608                            
0000437d  atoi                                 
2020050c  bee_time                             
ffffffff  binit                                
202004e0  black                                
00003607  convertAnalogToDigital               
00005605  delay_ms                             
000022d9  detect_trace_state_change            
20200514  encoder_A_count                      
20200516  encoder_B_count                      
20200518  encoder_count                        
2020051f  encoder_count_flag                   
000049ed  fputc                                
00003ec1  fputs                                
00003a05  frexp                                
00003a05  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
0000411d  gray_init                            
00002d05  gray_task                            
20200336  grayscale_count                      
20200318  grayscale_data                       
00000000  interruptVectors                     
0000266d  ldexp                                
0000266d  ldexpl                               
00003c21  main                                 
00004cd5  memccpy                              
000028ed  motor_direction                      
00004859  mspm0_delay_ms                       
00004a19  mspm0_get_clock_ms                   
00002b0d  normalizeAnalogValues                
0000387d  oled_i2c_sda_unlock                  
00004889  oled_pow                             
00004d39  pid_set_speed_target                 
00003abd  printf                               
00002a61  pwm_set                              
0000266d  scalbn                               
0000266d  scalbnl                              
20200000  sensor                               
00005615  servo_tracing                        
20200244  speedA_pid                           
2020026c  speedB_pid                           
00004a45  speed_control                        
000046c1  sprintf                              
20200320  start_time                           
20200324  target_angle                         
20200337  task_num                             
20200328  tick_ms                              
20200294  tracing_pid                          
2020032c  tracing_val                          
2020042c  uart_rx_buffer                       
20200338  uart_rx_index                        
20200330  uart_rx_ticks                        
00003d79  uart_task                            
00005625  wcslen                               
202004f0  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  SYSCFG_DL_GPIO_init                  
0000139d  __aeabi_dsub                         
0000139d  __subdf3                             
000013a7  __adddf3                             
000013a7  __aeabi_dadd                         
0000152f  __aeabi_idiv0                        
00001531  No_MCU_Ganv_Sensor_Init              
000017f5  Tracing_Value_Get                    
0000192d  PID_speed_realize                    
00001a55  GROUP1_IRQHandler                    
00001c99  OLED_ShowChar                        
00001db1  OLED_Init                            
00001ec1  Oled_Task                            
00001fd1  __aeabi_ddiv                         
00001fd1  __divdf3                             
000020dd  DL_Timer_initFourCCPWMMode           
000021e1  OLED_ShowNum                         
000022d7  __aeabi_ldiv0                        
000022d9  detect_trace_state_change            
000023c5  DL_Timer_initTimerMode               
000024ad  __aeabi_dmul                         
000024ad  __muldf3                             
00002591  DL_SYSCTL_configSYSPLL               
0000266d  ldexp                                
0000266d  ldexpl                               
0000266d  scalbn                               
0000266d  scalbnl                              
00002745  __aeabi_fsub                         
00002745  __subsf3                             
0000274f  __addsf3                             
0000274f  __aeabi_fadd                         
0000281d  Get_Analog_value                     
000028ed  motor_direction                      
000029b1  SYSCFG_DL_initPower                  
00002a61  pwm_set                              
00002b0d  normalizeAnalogValues                
00002bb7  _nop                                 
00002bb9  Key_Proc                             
00002c61  __udivmoddi4                         
00002d05  gray_task                            
00002da5  OLED_WR_Byte                         
00002e3d  SYSCFG_DL_PWM_MOTOR_init             
00002ec9  __aeabi_fmul                         
00002ec9  __mulsf3                             
00002f55  PID_init                             
000030e5  __aeabi_fdiv                         
000030e5  __divsf3                             
00003169  Servo_SetAngle                       
000031e5  __TI_decompress_lzss                 
00003261  Key_Read                             
000032d9  WIT_Get_Relative_Yaw                 
00003351  Encoder_Get                          
000033c5  Scheduler_Run                        
00003439  __gedf2                              
00003439  __gtdf2                              
000034ad  ADC0_IRQHandler                      
000034ad  ADC1_IRQHandler                      
000034ad  AES_IRQHandler                       
000034ad  CANFD0_IRQHandler                    
000034ad  DAC0_IRQHandler                      
000034ad  DMA_IRQHandler                       
000034ad  Default_Handler                      
000034ad  GROUP0_IRQHandler                    
000034ad  HardFault_Handler                    
000034ad  I2C0_IRQHandler                      
000034ad  I2C1_IRQHandler                      
000034ad  NMI_Handler                          
000034ad  PendSV_Handler                       
000034ad  RTC_IRQHandler                       
000034ad  SPI0_IRQHandler                      
000034ad  SPI1_IRQHandler                      
000034ad  SVC_Handler                          
000034ad  TIMA0_IRQHandler                     
000034ad  TIMA1_IRQHandler                     
000034ad  TIMG12_IRQHandler                    
000034ad  TIMG6_IRQHandler                     
000034ad  TIMG7_IRQHandler                     
000034ad  TIMG8_IRQHandler                     
000034ad  UART1_IRQHandler                     
000034ad  UART3_IRQHandler                     
000034b1  __aeabi_d2f                          
000034b1  __truncdfsf2                         
00003525  No_MCU_Ganv_Sensor_Init_Frist        
00003597  OLED_ShowString                      
00003607  convertAnalogToDigital               
00003673  OLED_Clear                           
000036dd  SYSCFG_DL_PWM_SERVO_init             
00003745  SYSCFG_DL_UART_WIT_init              
000037ad  __cmpdf2                             
000037ad  __eqdf2                              
000037ad  __ledf2                              
000037ad  __ltdf2                              
000037ad  __nedf2                              
0000387d  oled_i2c_sda_unlock                  
000038e1  __aeabi_dcmpeq                       
000038f5  __aeabi_dcmplt                       
00003909  __aeabi_dcmple                       
0000391d  __aeabi_dcmpge                       
00003931  __aeabi_dcmpgt                       
00003945  __aeabi_fcmpeq                       
00003959  __aeabi_fcmplt                       
0000396d  __aeabi_fcmple                       
00003981  __aeabi_fcmpge                       
00003995  __aeabi_fcmpgt                       
000039a7  DL_I2C_fillControllerTXFIFO          
00003a05  frexp                                
00003a05  frexpl                               
00003abd  printf                               
00003b19  SYSCFG_DL_I2C_OLED_init              
00003b71  __TI_ltoa                            
00003c21  main                                 
00003c79  __aeabi_idiv                         
00003c79  __aeabi_idivmod                      
00003cd1  DL_UART_drainRXFIFO                  
00003d25  SYSCFG_DL_UART_0_init                
00003d79  uart_task                            
00003ec1  fputs                                
00003f11  DL_DMA_initChannel                   
00003fa9  SYSCFG_DL_ADC1_init                  
00004041  __aeabi_d2iz                         
00004041  __fixdfsi                            
0000408b  adc_getValue                         
000040d5  DL_UART_init                         
0000411d  gray_init                            
000041ad  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000041f1  SYSCFG_DL_init                       
00004235  UART0_IRQHandler                     
00004279  No_Mcu_Ganv_Sensor_Task_Without_tick 
000042bd  DL_ADC12_setClockConfig              
000042fd  __aeabi_uidiv                        
000042fd  __aeabi_uidivmod                     
0000433d  __aeabi_f2d                          
0000433d  __extendsfdf2                        
0000437d  atoi                                 
00004471  Get_Anolog_Value                     
000044ad  OLED_Set_Pos                         
000044e9  SYSCFG_DL_SYSCTL_init                
00004525  __aeabi_i2f                          
00004525  __floatsisf                          
00004561  __gesf2                              
00004561  __gtsf2                              
0000459d  __TI_auto_init_nobinit_nopinit       
000045d9  __cmpsf2                             
000045d9  __eqsf2                              
000045d9  __lesf2                              
000045d9  __ltsf2                              
000045d9  __nesf2                              
00004615  __muldsi3                            
0000464f  Get_Normalize_For_User               
00004689  __aeabi_f2iz                         
00004689  __fixsfsi                            
000046c1  sprintf                              
000046f9  Beep_Time_Control                    
00004795  SYSCFG_DL_FOR_CONTROL_init           
00004859  mspm0_delay_ms                       
00004889  oled_pow                             
000049c1  __aeabi_i2d                          
000049c1  __floatsidf                          
000049ed  fputc                                
00004a19  mspm0_get_clock_ms                   
00004a45  speed_control                        
00004b61  _c_int00_noargs                      
00004bfb  DL_I2C_setClockConfig                
00004c69  TIMG0_IRQHandler                     
00004c8d  __aeabi_ui2d                         
00004c8d  __floatunsidf                        
00004cb1  __aeabi_lmul                         
00004cb1  __muldi3                             
00004cd5  memccpy                              
00004d39  pid_set_speed_target                 
00004d95  __aeabi_llsl                         
00004d95  __ashldi3                            
00004f21  DL_Timer_setCaptCompUpdateMethod     
00004f3d  DL_Timer_setClockConfig              
0000522d  DL_Timer_setCaptureCompareOutCtl     
000052ed  SYSCFG_DL_DMA_WIT_init               
00005305  SysTick_Init                         
000053e5  Encoder_Init                         
000053fb  Timer_Init                           
00005411  __TI_zero_init_nomemset              
00005519  Servo_init                           
0000552d  State_Machine_init                   
00005541  __aeabi_uldivmod                     
0000558d  DL_UART_setClockConfig               
0000559f  TI_memcpy_small                      
000055b1  __TI_decompress_none                 
000055e5  DL_Timer_setCaptureCompareValue      
000055f5  SysTick_Handler                      
00005605  delay_ms                             
00005615  servo_tracing                        
00005625  wcslen                               
00005635  Get_Digtal_For_User                  
00005645  __aeabi_memset                       
00005645  __aeabi_memset4                      
00005645  __aeabi_memset8                      
00005661  TI_memset_small                      
00005671  Scheduler_Init                       
0000567d  Tracing_Control                      
00005689  __aeabi_memclr                       
00005689  __aeabi_memclr4                      
00005689  __aeabi_memclr8                      
00005695  DL_Common_delayCycles                
000056c3  Servo_SetCenter                      
000056d7  SYSCFG_DL_DMA_init                   
000056e1  __aeabi_errno_addr                   
000056e9  __aeabi_memcpy                       
000056e9  __aeabi_memcpy4                      
000056e9  __aeabi_memcpy8                      
00005701  Reset_Handler                        
00005715  _system_pre_init                     
00005718  C$$EXIT                              
00005719  abort                                
00005720  asc2_1608                            
00005d10  asc2_0806                            
00005f40  __aeabi_ctype_table_                 
00005f40  __aeabi_ctype_table_C                
00006180  __TI_Handler_Table_Base              
0000618c  __TI_Handler_Table_Limit             
00006194  __TI_CINIT_Base                      
000061a4  __TI_CINIT_Limit                     
000061a4  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  OLED_String                          
202002f1  Digtal                               
202002f2  Key_Down                             
202002f3  Key_Old                              
202002f4  State_Machine                        
20200308  Normal                               
20200318  grayscale_data                       
20200320  start_time                           
20200324  target_angle                         
20200328  tick_ms                              
2020032c  tracing_val                          
20200330  uart_rx_ticks                        
20200334  Key_Up                               
20200335  Key_Val                              
20200336  grayscale_count                      
20200337  task_num                             
20200338  uart_rx_index                        
2020033c  _ftable                              
2020042c  uart_rx_buffer                       
202004d0  Anolog                               
202004e0  black                                
202004f0  white                                
20200500  __aeabi_errno                        
20200504  _lock                                
20200508  _unlock                              
2020050c  bee_time                             
20200514  encoder_A_count                      
20200516  encoder_B_count                      
20200518  encoder_count                        
2020051f  encoder_count_flag                   
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[287 symbols]
