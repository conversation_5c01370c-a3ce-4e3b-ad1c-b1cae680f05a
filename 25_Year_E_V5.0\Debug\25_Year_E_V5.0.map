******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Aug  2 04:05:56 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004da9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000063a8  00019c58  R  X
  SRAM                  20200000   00008000  00000725  000078db  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000063a8   000063a8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005840   00005840    r-x .text
  00005900    00005900    00000a00   00000a00    r-- .rodata
  00006300    00006300    000000a8   000000a8    r-- .cinit
20200000    20200000    00000528   00000000    rw-
  20200000    20200000    00000321   00000000    rw- .bss
  20200324    20200324    00000204   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005840     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    00000200     app_question_task.o (.text.Question_Task_1)
                  0000121c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000013f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000159c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000172e    00000002     app_question_task.o (.text.Question_Task_2)
                  00001730    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000018b8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000019f4    00000130     pid.o (.text.Tracing_Value_Get)
                  00001b24    00000128     pid.o (.text.PID_speed_realize)
                  00001c4c    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001d70    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001e90    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001fa8    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  000020b8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000021c4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000022c8    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  000023be    00000002     app_question_task.o (.text.Question_Task_3)
                  000023c0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000024a8    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  00002590    000000e8     app_tracing_check.o (.text.detect_trace_state_change)
                  00002678    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000275c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002838    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002910    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000029e8    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00002ab8    000000c4     key.o (.text.Key_Proc)
                  00002b7c    000000c4     motor.o (.text.motor_direction)
                  00002c40    000000b8     interrupt.o (.text.TIMG0_IRQHandler)
                  00002cf8    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002da8    000000ac     motor.o (.text.pwm_set)
                  00002e54    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002efe    00000002     app_question_task.o (.text.Question_Task_4)
                  00002f00    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002fa2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002fa4    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00003044    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  000030dc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00003168    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000031f4    00000088     pid.o (.text.PID_init)
                  0000327c    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003300    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003384    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003406    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003408    0000007c     servo.o (.text.Servo_SetAngle)
                  00003484    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003500    00000078     key.o (.text.Key_Read)
                  00003578    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  000035f0    00000074     encoder.o (.text.Encoder_Get)
                  00003664    00000074     Scheduler.o (.text.Scheduler_Run)
                  000036d8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000374c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003750    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000037c4    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003836    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  000038a2    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000390c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00003974    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000039dc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003a44    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003aaa    00000002            : _lock.c.obj (.text._nop)
                  00003aac    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00003b10    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003b72    00000002     --HOLE-- [fill = 0]
                  00003b74    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003bd6    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003c34    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003c90    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003cec    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003d48    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003da0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003df8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003e50    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003ea6    00000002     --HOLE-- [fill = 0]
                  00003ea8    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003efc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003f50    00000054     usart_app.o (.text.uart_task)
                  00003fa4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003ff6    00000002     --HOLE-- [fill = 0]
                  00003ff8    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00004048    00000050     clock.o (.text.SysTick_Config)
                  00004098    00000050     usart_app.o (.text.fputs)
                  000040e8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004134    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004180    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000041cc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004216    00000002     --HOLE-- [fill = 0]
                  00004218    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004262    0000004a     adc_app.o (.text.adc_getValue)
                  000042ac    0000004a     main.o (.text.main)
                  000042f6    00000002     --HOLE-- [fill = 0]
                  000042f8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004340    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004388    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  000043d0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004414    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004458    00000044     usart_app.o (.text.UART0_IRQHandler)
                  0000449c    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000044de    00000002     --HOLE-- [fill = 0]
                  000044e0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004520    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004560    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000045a0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000045e0    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000461c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004658    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004694    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000046d0    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  0000470c    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004748    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004784    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000047c0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000047fc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004836    00000002     --HOLE-- [fill = 0]
                  00004838    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004872    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000048aa    00000002     --HOLE-- [fill = 0]
                  000048ac    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000048e4    00000034     beep.o (.text.Beep_Time_Control)
                  00004918    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000494c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004980    00000034     app_question_task.o (.text.Q1_Tracing_Control_With_Speed)
                  000049b4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000049e8    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00004a18    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00004a48    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004a78    00000030     clock.o (.text.mspm0_delay_ms)
                  00004aa8    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00004ad8    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004b04    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  00004b30    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00004b5c    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004b88    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004bb4    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004be0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004c0c    0000002c     usart_app.o (.text.fputc)
                  00004c38    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004c64    0000002c     app_tracing_control.o (.text.speed_control)
                  00004c90    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004cb8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004ce0    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004d08    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004d30    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004d58    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004d80    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004da8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004dd0    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004df6    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004e1c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004e42    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004e68    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004e8c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004eb0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004ed4    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004ef8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004f1a    00000002     --HOLE-- [fill = 0]
                  00004f1c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004f3c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004f5c    00000020     pid.o (.text.pid_set_speed_target)
                  00004f7c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004f9a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004fb8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004fd6    00000002     --HOLE-- [fill = 0]
                  00004fd8    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004ff4    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00005010    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  0000502c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005048    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00005064    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005080    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000509c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000050b8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000050d4    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  000050f0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000510c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005128    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005144    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005160    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000517c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005198    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000051b0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000051c8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000051e0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  000051f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005210    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005228    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005240    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00005258    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005270    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005288    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  000052a0    00000018     beep.o (.text.DL_GPIO_setPins)
                  000052b8    00000018     motor.o (.text.DL_GPIO_setPins)
                  000052d0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  000052e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005300    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005318    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005330    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005348    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005360    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005378    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005390    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000053a8    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  000053c0    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  000053d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000053f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005408    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005420    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005438    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005450    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005468    00000018     servo.o (.text.DL_Timer_startCounter)
                  00005480    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005498    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000054b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000054c8    00000018     usart_app.o (.text.DL_UART_isBusy)
                  000054e0    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000054f8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005510    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005528    00000018     clock.o (.text.SysTick_Init)
                  00005540    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00005556    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  0000556c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005582    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00005598    00000016     key.o (.text.DL_GPIO_readPins)
                  000055ae    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  000055c4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000055da    00000016     usart_app.o (.text.DL_UART_transmitData)
                  000055f0    00000016     encoder.o (.text.Encoder_Init)
                  00005606    00000016     interrupt.o (.text.Timer_Init)
                  0000561c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005632    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00005646    00000014     beep.o (.text.DL_GPIO_clearPins)
                  0000565a    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000566e    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00005682    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005696    00000002     --HOLE-- [fill = 0]
                  00005698    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  000056ac    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000056c0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000056d4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000056e8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000056fc    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005710    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005724    00000014     servo.o (.text.Servo_init)
                  00005738    00000014     app_question_task.o (.text.State_Machine_init)
                  0000574c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005760    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005774    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00005786    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00005798    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000057aa    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000057bc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000057ce    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000057de    00000002     --HOLE-- [fill = 0]
                  000057e0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000057f0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005800    00000010     interrupt.o (.text.SysTick_Handler)
                  00005810    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005820    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005830    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  0000583e    00000002     --HOLE-- [fill = 0]
                  00005840    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000584e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000585c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000586a    00000002     --HOLE-- [fill = 0]
                  0000586c    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00005878    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005884    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000588e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005898    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000058a8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000058b2    0000000a     servo.o (.text.Servo_SetCenter)
                  000058bc    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000058c4    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000058cc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000058d4    00000008     libc.a : printf.c.obj (.text._outc)
                  000058dc    00000008            : printf.c.obj (.text._outs)
                  000058e4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000058e8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000058f8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000058fc    00000004            : exit.c.obj (.text:abort)

.cinit     0    00006300    000000a8     
                  00006300    00000081     (.cinit..data.load) [load image, compression = lzss]
                  00006381    00000003     --HOLE-- [fill = 0]
                  00006384    0000000c     (__TI_handler_table)
                  00006390    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006398    00000010     (__TI_cinit_table)

.rodata    0    00005900    00000a00     
                  00005900    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005ef0    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006118    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006120    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006221    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006224    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000624c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006264    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006278    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006289    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  0000629a    00000010     encoder.o (.rodata.encoder_table)
                  000062aa    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  000062b9    0000000c     interrupt.o (.rodata.str1.146019215406595515531)
                  000062c5    00000001     --HOLE-- [fill = 0]
                  000062c6    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000062d0    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  000062da    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000062dc    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000062e4    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  000062ec    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  000062ef    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  000062f2    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000062f4    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  000062f6    0000000a     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000321     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:tracing_val)
                  20200318    00000004     (.common:uart_rx_ticks)
                  2020031c    00000001     (.common:Key_Up)
                  2020031d    00000001     (.common:Key_Val)
                  2020031e    00000001     (.common:grayscale_count)
                  2020031f    00000001     (.common:task_num)
                  20200320    00000001     (.common:uart_rx_index)

.data      0    20200324    00000204     UNINITIALIZED
                  20200324    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200414    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200494    00000024     Scheduler.o (.data.scheduler_task)
                  202004b8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004c8    00000010     Ganv_Grayscale.o (.data.black)
                  202004d8    00000010     Ganv_Grayscale.o (.data.white)
                  202004e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004ec    00000004            : _lock.c.obj (.data._lock)
                  202004f0    00000004            : _lock.c.obj (.data._unlock)
                  202004f4    00000004     beep.o (.data.bee_time)
                  202004f8    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004fc    00000004     app_question_task.o (.data.q1_current_base_speed)
                  20200500    00000004     app_question_task.o (.data.q1_min_speed)
                  20200504    00000004     app_question_task.o (.data.q1_slowdown_start_time)
                  20200508    00000004     app_question_task.o (.data.q1_smooth_start_time)
                  2020050c    00000004     app_question_task.o (.data.q1_target_speed)
                  20200510    00000004     app_question_task.o (.data.q1_turn_speed)
                  20200514    00000002     encoder.o (.data.encoder_A_count)
                  20200516    00000002     encoder.o (.data.encoder_B_count)
                  20200518    00000002     encoder.o (.data.encoder_count)
                  2020051a    00000002     app_question_task.o (.data.q1_slowdown_duration)
                  2020051c    00000002     app_question_task.o (.data.q1_smooth_duration)
                  2020051e    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  2020051f    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200520    00000001     app_question_task.o (.data.circle_num)
                  20200521    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200522    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200523    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200524    00000001     encoder.o (.data.encoder_count_flag)
                  20200525    00000001     app_question_task.o (.data.q1_first_flag)
                  20200526    00000001     app_question_task.o (.data.q1_slowdown_active)
                  20200527    00000001     app_question_task.o (.data.turn_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_question_task.o            590     0         52     
       app_tracing_check.o            232     0         7      
       app_tracing_control.o          44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         866     0         59     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1448    12        0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1776    12        8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2194    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2194    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          768     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         768     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5458    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2918    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       165       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22564   2906      1829   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006398 records: 2, size/record: 8, table size: 16
	.data: load addr=00006300, load size=00000081 bytes, run addr=20200324, run size=00000204 bytes, compression=lzss
	.bss: load addr=00006390, load size=00000008 bytes, run addr=20200000, run size=00000321 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006384 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000159d     00005898     00005896   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004da9     000058e8     000058e4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000374d  ADC0_IRQHandler                      
0000374d  ADC1_IRQHandler                      
0000374d  AES_IRQHandler                       
202004b8  Anolog                               
000048e5  Beep_Time_Control                    
000058fc  C$$EXIT                              
0000374d  CANFD0_IRQHandler                    
0000374d  DAC0_IRQHandler                      
000044e1  DL_ADC12_setClockConfig              
00005885  DL_Common_delayCycles                
000040e9  DL_DMA_initChannel                   
00003bd7  DL_I2C_fillControllerTXFIFO          
00004e43  DL_I2C_setClockConfig                
0000275d  DL_SYSCTL_configSYSPLL               
000043d1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000021c5  DL_Timer_initFourCCPWMMode           
000023c1  DL_Timer_initTimerMode               
00005145  DL_Timer_setCaptCompUpdateMethod     
00005451  DL_Timer_setCaptureCompareOutCtl     
000057f1  DL_Timer_setCaptureCompareValue      
00005161  DL_Timer_setClockConfig              
00003ea9  DL_UART_drainRXFIFO                  
000042f9  DL_UART_init                         
00005799  DL_UART_setClockConfig               
0000374d  DMA_IRQHandler                       
0000374d  Default_Handler                      
202002dd  Digtal                               
000035f1  Encoder_Get                          
000055f1  Encoder_Init                         
0000374d  GROUP0_IRQHandler                    
00001c4d  GROUP1_IRQHandler                    
000029e9  Get_Analog_value                     
00004695  Get_Anolog_Value                     
00005831  Get_Digtal_For_User                  
00004873  Get_Normalize_For_User               
0000374d  HardFault_Handler                    
0000374d  I2C0_IRQHandler                      
0000374d  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
00002ab9  Key_Proc                             
00003501  Key_Read                             
2020031c  Key_Up                               
2020031d  Key_Val                              
0000374d  NMI_Handler                          
00001731  No_MCU_Ganv_Sensor_Init              
000037c5  No_MCU_Ganv_Sensor_Init_Frist        
0000449d  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
000038a3  OLED_Clear                           
00001fa9  OLED_Init                            
000046d1  OLED_Set_Pos                         
00001e91  OLED_ShowChar                        
000022c9  OLED_ShowNum                         
00003045  OLED_WR_Byte                         
000024a9  Oled_Task                            
000031f5  PID_init                             
00001b25  PID_speed_realize                    
0000374d  PendSV_Handler                       
00004981  Q1_Tracing_Control_With_Speed        
0000101d  Question_Task_1                      
0000172f  Question_Task_2                      
000023bf  Question_Task_3                      
00002eff  Question_Task_4                      
0000374d  RTC_IRQHandler                       
000058e5  Reset_Handler                        
0000374d  SPI0_IRQHandler                      
0000374d  SPI1_IRQHandler                      
0000374d  SVC_Handler                          
00004181  SYSCFG_DL_ADC1_init                  
00005511  SYSCFG_DL_DMA_WIT_init               
000058bd  SYSCFG_DL_DMA_init                   
000049b5  SYSCFG_DL_FOR_CONTROL_init           
000013f9  SYSCFG_DL_GPIO_init                  
00003d49  SYSCFG_DL_I2C_OLED_init              
000030dd  SYSCFG_DL_PWM_MOTOR_init             
0000390d  SYSCFG_DL_PWM_SERVO_init             
0000470d  SYSCFG_DL_SYSCTL_init                
00003efd  SYSCFG_DL_UART_0_init                
00003975  SYSCFG_DL_UART_WIT_init              
00004415  SYSCFG_DL_init                       
00002cf9  SYSCFG_DL_initPower                  
0000586d  Scheduler_Init                       
00003665  Scheduler_Run                        
00003409  Servo_SetAngle                       
000058b3  Servo_SetCenter                      
00005725  Servo_init                           
202002e0  State_Machine                        
00005739  State_Machine_init                   
00005801  SysTick_Handler                      
00005529  SysTick_Init                         
0000374d  TIMA0_IRQHandler                     
0000374d  TIMA1_IRQHandler                     
00002c41  TIMG0_IRQHandler                     
0000374d  TIMG12_IRQHandler                    
0000374d  TIMG6_IRQHandler                     
0000374d  TIMG7_IRQHandler                     
0000374d  TIMG8_IRQHandler                     
000057ab  TI_memcpy_small                      
0000585d  TI_memset_small                      
00005607  Timer_Init                           
000019f5  Tracing_Value_Get                    
00004459  UART0_IRQHandler                     
0000374d  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
0000374d  UART3_IRQHandler                     
00003579  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006398  __TI_CINIT_Base                      
000063a8  __TI_CINIT_Limit                     
000063a8  __TI_CINIT_Warm                      
00006384  __TI_Handler_Table_Base              
00006390  __TI_Handler_Table_Limit             
000047c1  __TI_auto_init_nobinit_nopinit       
00003485  __TI_decompress_lzss                 
000057bd  __TI_decompress_none                 
00003da1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000561d  __TI_zero_init_nomemset              
000015a7  __adddf3                             
0000291b  __addsf3                             
00006120  __aeabi_ctype_table_                 
00006120  __aeabi_ctype_table_C                
00003751  __aeabi_d2f                          
00004219  __aeabi_d2iz                         
000015a7  __aeabi_dadd                         
00003b11  __aeabi_dcmpeq                       
00003b4d  __aeabi_dcmpge                       
00003b61  __aeabi_dcmpgt                       
00003b39  __aeabi_dcmple                       
00003b25  __aeabi_dcmplt                       
000020b9  __aeabi_ddiv                         
00002679  __aeabi_dmul                         
0000159d  __aeabi_dsub                         
202004e8  __aeabi_errno                        
000058c5  __aeabi_errno_addr                   
00004561  __aeabi_f2d                          
000048ad  __aeabi_f2iz                         
0000291b  __aeabi_fadd                         
00003b75  __aeabi_fcmpeq                       
00003bb1  __aeabi_fcmpge                       
00003bc5  __aeabi_fcmpgt                       
00003b9d  __aeabi_fcmple                       
00003b89  __aeabi_fcmplt                       
00003385  __aeabi_fdiv                         
00003169  __aeabi_fmul                         
00002911  __aeabi_fsub                         
00004be1  __aeabi_i2d                          
00004749  __aeabi_i2f                          
00003e51  __aeabi_idiv                         
00002fa3  __aeabi_idiv0                        
00003e51  __aeabi_idivmod                      
00003407  __aeabi_ldiv0                        
00004fb9  __aeabi_llsl                         
00004ed5  __aeabi_lmul                         
00005879  __aeabi_memclr                       
00005879  __aeabi_memclr4                      
00005879  __aeabi_memclr8                      
000058cd  __aeabi_memcpy                       
000058cd  __aeabi_memcpy4                      
000058cd  __aeabi_memcpy8                      
00005841  __aeabi_memset                       
00005841  __aeabi_memset4                      
00005841  __aeabi_memset8                      
00004eb1  __aeabi_ui2d                         
00004d81  __aeabi_ui2f                         
00004521  __aeabi_uidiv                        
00004521  __aeabi_uidivmod                     
0000574d  __aeabi_uldivmod                     
00004fb9  __ashldi3                            
ffffffff  __binit__                            
000039dd  __cmpdf2                             
000047fd  __cmpsf2                             
000020b9  __divdf3                             
00003385  __divsf3                             
000039dd  __eqdf2                              
000047fd  __eqsf2                              
00004561  __extendsfdf2                        
00004219  __fixdfsi                            
000048ad  __fixsfsi                            
00004be1  __floatsidf                          
00004749  __floatsisf                          
00004eb1  __floatunsidf                        
00004d81  __floatunsisf                        
000036d9  __gedf2                              
00004785  __gesf2                              
000036d9  __gtdf2                              
00004785  __gtsf2                              
000039dd  __ledf2                              
000047fd  __lesf2                              
000039dd  __ltdf2                              
000047fd  __ltsf2                              
UNDEFED   __mpu_init                           
00002679  __muldf3                             
00004ed5  __muldi3                             
00004839  __muldsi3                            
00003169  __mulsf3                             
000039dd  __nedf2                              
000047fd  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000159d  __subdf3                             
00002911  __subsf3                             
00003751  __truncdfsf2                         
00002f01  __udivmoddi4                         
00004da9  _c_int00_noargs                      
20200324  _ftable                              
202004ec  _lock                                
00003aab  _nop                                 
UNDEFED   _system_post_cinit                   
000058f9  _system_pre_init                     
202004f0  _unlock                              
000058fd  abort                                
00004263  adc_getValue                         
2020021c  angle_pid                            
00005ef0  asc2_0806                            
00005900  asc2_1608                            
000045a1  atoi                                 
202004f4  bee_time                             
ffffffff  binit                                
202004c8  black                                
20200520  circle_num                           
00003837  convertAnalogToDigital               
00005811  delay_ms                             
00002591  detect_trace_state_change            
20200514  encoder_A_count                      
20200516  encoder_B_count                      
20200518  encoder_count                        
20200524  encoder_count_flag                   
00004c0d  fputc                                
00004099  fputs                                
00003c35  frexp                                
00003c35  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
00004341  gray_init                            
00002fa5  gray_task                            
2020031e  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
00002839  ldexp                                
00002839  ldexpl                               
000042ad  main                                 
00004ef9  memccpy                              
00002b7d  motor_direction                      
00004a79  mspm0_delay_ms                       
00004c39  mspm0_get_clock_ms                   
00002e55  normalizeAnalogValues                
00003aad  oled_i2c_sda_unlock                  
00004aa9  oled_pow                             
00004f5d  pid_set_speed_target                 
00003ced  printf                               
00002da9  pwm_set                              
202004fc  q1_current_base_speed                
20200525  q1_first_flag                        
20200500  q1_min_speed                         
20200526  q1_slowdown_active                   
2020051a  q1_slowdown_duration                 
20200504  q1_slowdown_start_time               
2020051c  q1_smooth_duration                   
20200508  q1_smooth_start_time                 
2020050c  q1_target_speed                      
20200510  q1_turn_speed                        
00002839  scalbn                               
00002839  scalbnl                              
20200000  sensor                               
20200244  speedA_pid                           
2020026c  speedB_pid                           
00004c65  speed_control                        
2020030c  start_time                           
2020031f  task_num                             
20200310  tick_ms                              
20200294  tracing_pid                          
20200314  tracing_val                          
20200527  turn_num                             
20200414  uart_rx_buffer                       
20200320  uart_rx_index                        
20200318  uart_rx_ticks                        
00003f51  uart_task                            
00005821  wcslen                               
202004d8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
0000101d  Question_Task_1                      
000013f9  SYSCFG_DL_GPIO_init                  
0000159d  __aeabi_dsub                         
0000159d  __subdf3                             
000015a7  __adddf3                             
000015a7  __aeabi_dadd                         
0000172f  Question_Task_2                      
00001731  No_MCU_Ganv_Sensor_Init              
000019f5  Tracing_Value_Get                    
00001b25  PID_speed_realize                    
00001c4d  GROUP1_IRQHandler                    
00001e91  OLED_ShowChar                        
00001fa9  OLED_Init                            
000020b9  __aeabi_ddiv                         
000020b9  __divdf3                             
000021c5  DL_Timer_initFourCCPWMMode           
000022c9  OLED_ShowNum                         
000023bf  Question_Task_3                      
000023c1  DL_Timer_initTimerMode               
000024a9  Oled_Task                            
00002591  detect_trace_state_change            
00002679  __aeabi_dmul                         
00002679  __muldf3                             
0000275d  DL_SYSCTL_configSYSPLL               
00002839  ldexp                                
00002839  ldexpl                               
00002839  scalbn                               
00002839  scalbnl                              
00002911  __aeabi_fsub                         
00002911  __subsf3                             
0000291b  __addsf3                             
0000291b  __aeabi_fadd                         
000029e9  Get_Analog_value                     
00002ab9  Key_Proc                             
00002b7d  motor_direction                      
00002c41  TIMG0_IRQHandler                     
00002cf9  SYSCFG_DL_initPower                  
00002da9  pwm_set                              
00002e55  normalizeAnalogValues                
00002eff  Question_Task_4                      
00002f01  __udivmoddi4                         
00002fa3  __aeabi_idiv0                        
00002fa5  gray_task                            
00003045  OLED_WR_Byte                         
000030dd  SYSCFG_DL_PWM_MOTOR_init             
00003169  __aeabi_fmul                         
00003169  __mulsf3                             
000031f5  PID_init                             
00003385  __aeabi_fdiv                         
00003385  __divsf3                             
00003407  __aeabi_ldiv0                        
00003409  Servo_SetAngle                       
00003485  __TI_decompress_lzss                 
00003501  Key_Read                             
00003579  WIT_Get_Relative_Yaw                 
000035f1  Encoder_Get                          
00003665  Scheduler_Run                        
000036d9  __gedf2                              
000036d9  __gtdf2                              
0000374d  ADC0_IRQHandler                      
0000374d  ADC1_IRQHandler                      
0000374d  AES_IRQHandler                       
0000374d  CANFD0_IRQHandler                    
0000374d  DAC0_IRQHandler                      
0000374d  DMA_IRQHandler                       
0000374d  Default_Handler                      
0000374d  GROUP0_IRQHandler                    
0000374d  HardFault_Handler                    
0000374d  I2C0_IRQHandler                      
0000374d  I2C1_IRQHandler                      
0000374d  NMI_Handler                          
0000374d  PendSV_Handler                       
0000374d  RTC_IRQHandler                       
0000374d  SPI0_IRQHandler                      
0000374d  SPI1_IRQHandler                      
0000374d  SVC_Handler                          
0000374d  TIMA0_IRQHandler                     
0000374d  TIMA1_IRQHandler                     
0000374d  TIMG12_IRQHandler                    
0000374d  TIMG6_IRQHandler                     
0000374d  TIMG7_IRQHandler                     
0000374d  TIMG8_IRQHandler                     
0000374d  UART1_IRQHandler                     
0000374d  UART3_IRQHandler                     
00003751  __aeabi_d2f                          
00003751  __truncdfsf2                         
000037c5  No_MCU_Ganv_Sensor_Init_Frist        
00003837  convertAnalogToDigital               
000038a3  OLED_Clear                           
0000390d  SYSCFG_DL_PWM_SERVO_init             
00003975  SYSCFG_DL_UART_WIT_init              
000039dd  __cmpdf2                             
000039dd  __eqdf2                              
000039dd  __ledf2                              
000039dd  __ltdf2                              
000039dd  __nedf2                              
00003aab  _nop                                 
00003aad  oled_i2c_sda_unlock                  
00003b11  __aeabi_dcmpeq                       
00003b25  __aeabi_dcmplt                       
00003b39  __aeabi_dcmple                       
00003b4d  __aeabi_dcmpge                       
00003b61  __aeabi_dcmpgt                       
00003b75  __aeabi_fcmpeq                       
00003b89  __aeabi_fcmplt                       
00003b9d  __aeabi_fcmple                       
00003bb1  __aeabi_fcmpge                       
00003bc5  __aeabi_fcmpgt                       
00003bd7  DL_I2C_fillControllerTXFIFO          
00003c35  frexp                                
00003c35  frexpl                               
00003ced  printf                               
00003d49  SYSCFG_DL_I2C_OLED_init              
00003da1  __TI_ltoa                            
00003e51  __aeabi_idiv                         
00003e51  __aeabi_idivmod                      
00003ea9  DL_UART_drainRXFIFO                  
00003efd  SYSCFG_DL_UART_0_init                
00003f51  uart_task                            
00004099  fputs                                
000040e9  DL_DMA_initChannel                   
00004181  SYSCFG_DL_ADC1_init                  
00004219  __aeabi_d2iz                         
00004219  __fixdfsi                            
00004263  adc_getValue                         
000042ad  main                                 
000042f9  DL_UART_init                         
00004341  gray_init                            
000043d1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004415  SYSCFG_DL_init                       
00004459  UART0_IRQHandler                     
0000449d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000044e1  DL_ADC12_setClockConfig              
00004521  __aeabi_uidiv                        
00004521  __aeabi_uidivmod                     
00004561  __aeabi_f2d                          
00004561  __extendsfdf2                        
000045a1  atoi                                 
00004695  Get_Anolog_Value                     
000046d1  OLED_Set_Pos                         
0000470d  SYSCFG_DL_SYSCTL_init                
00004749  __aeabi_i2f                          
00004749  __floatsisf                          
00004785  __gesf2                              
00004785  __gtsf2                              
000047c1  __TI_auto_init_nobinit_nopinit       
000047fd  __cmpsf2                             
000047fd  __eqsf2                              
000047fd  __lesf2                              
000047fd  __ltsf2                              
000047fd  __nesf2                              
00004839  __muldsi3                            
00004873  Get_Normalize_For_User               
000048ad  __aeabi_f2iz                         
000048ad  __fixsfsi                            
000048e5  Beep_Time_Control                    
00004981  Q1_Tracing_Control_With_Speed        
000049b5  SYSCFG_DL_FOR_CONTROL_init           
00004a79  mspm0_delay_ms                       
00004aa9  oled_pow                             
00004be1  __aeabi_i2d                          
00004be1  __floatsidf                          
00004c0d  fputc                                
00004c39  mspm0_get_clock_ms                   
00004c65  speed_control                        
00004d81  __aeabi_ui2f                         
00004d81  __floatunsisf                        
00004da9  _c_int00_noargs                      
00004e43  DL_I2C_setClockConfig                
00004eb1  __aeabi_ui2d                         
00004eb1  __floatunsidf                        
00004ed5  __aeabi_lmul                         
00004ed5  __muldi3                             
00004ef9  memccpy                              
00004f5d  pid_set_speed_target                 
00004fb9  __aeabi_llsl                         
00004fb9  __ashldi3                            
00005145  DL_Timer_setCaptCompUpdateMethod     
00005161  DL_Timer_setClockConfig              
00005451  DL_Timer_setCaptureCompareOutCtl     
00005511  SYSCFG_DL_DMA_WIT_init               
00005529  SysTick_Init                         
000055f1  Encoder_Init                         
00005607  Timer_Init                           
0000561d  __TI_zero_init_nomemset              
00005725  Servo_init                           
00005739  State_Machine_init                   
0000574d  __aeabi_uldivmod                     
00005799  DL_UART_setClockConfig               
000057ab  TI_memcpy_small                      
000057bd  __TI_decompress_none                 
000057f1  DL_Timer_setCaptureCompareValue      
00005801  SysTick_Handler                      
00005811  delay_ms                             
00005821  wcslen                               
00005831  Get_Digtal_For_User                  
00005841  __aeabi_memset                       
00005841  __aeabi_memset4                      
00005841  __aeabi_memset8                      
0000585d  TI_memset_small                      
0000586d  Scheduler_Init                       
00005879  __aeabi_memclr                       
00005879  __aeabi_memclr4                      
00005879  __aeabi_memclr8                      
00005885  DL_Common_delayCycles                
000058b3  Servo_SetCenter                      
000058bd  SYSCFG_DL_DMA_init                   
000058c5  __aeabi_errno_addr                   
000058cd  __aeabi_memcpy                       
000058cd  __aeabi_memcpy4                      
000058cd  __aeabi_memcpy8                      
000058e5  Reset_Handler                        
000058f9  _system_pre_init                     
000058fc  C$$EXIT                              
000058fd  abort                                
00005900  asc2_1608                            
00005ef0  asc2_0806                            
00006120  __aeabi_ctype_table_                 
00006120  __aeabi_ctype_table_C                
00006384  __TI_Handler_Table_Base              
00006390  __TI_Handler_Table_Limit             
00006398  __TI_CINIT_Base                      
000063a8  __TI_CINIT_Limit                     
000063a8  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  tracing_val                          
20200318  uart_rx_ticks                        
2020031c  Key_Up                               
2020031d  Key_Val                              
2020031e  grayscale_count                      
2020031f  task_num                             
20200320  uart_rx_index                        
20200324  _ftable                              
20200414  uart_rx_buffer                       
202004b8  Anolog                               
202004c8  black                                
202004d8  white                                
202004e8  __aeabi_errno                        
202004ec  _lock                                
202004f0  _unlock                              
202004f4  bee_time                             
202004fc  q1_current_base_speed                
20200500  q1_min_speed                         
20200504  q1_slowdown_start_time               
20200508  q1_smooth_start_time                 
2020050c  q1_target_speed                      
20200510  q1_turn_speed                        
20200514  encoder_A_count                      
20200516  encoder_B_count                      
20200518  encoder_count                        
2020051a  q1_slowdown_duration                 
2020051c  q1_smooth_duration                   
20200520  circle_num                           
20200524  encoder_count_flag                   
20200525  q1_first_flag                        
20200526  q1_slowdown_active                   
20200527  turn_num                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[300 symbols]
