******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 12:22:45 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004c7d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000062a8  00019d58  R  X
  SRAM                  20200000   00008000  00000722  000078de  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000062a8   000062a8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005760   00005760    r-x .text
  00005820    00005820    000009f0   000009f0    r-- .rodata
  00006210    00006210    00000098   00000098    r-- .cinit
20200000    20200000    00000525   00000000    rw-
  20200000    20200000    0000033d   00000000    rw- .bss
  20200340    20200340    000001e5   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005760     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000152e    00000002     app_question_task.o (.text.Question_Task_2)
                  00001530    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000016b8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000017f4    00000138     pid.o (.text.Tracing_Value_Get)
                  0000192c    00000128     pid.o (.text.PID_speed_realize)
                  00001a54    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001b78    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001c98    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001db0    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00001ec0    00000110     oled_hardware_i2c.o (.text.Oled_Task)
                  00001fd0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000020dc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000021e0    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  000022d6    00000002     app_question_task.o (.text.Question_Task_3)
                  000022d8    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  000023c4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000024ac    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002590    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000266c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002744    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000281c    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  000028ec    000000c4     motor.o (.text.motor_direction)
                  000029b0    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002a60    000000ac     motor.o (.text.pwm_set)
                  00002b0c    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002bb6    00000002     app_question_task.o (.text.Question_Task_4)
                  00002bb8    000000a8     key.o (.text.Key_Proc)
                  00002c60    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002d02    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002d04    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002da4    0000009c     app_question_task.o (.text.Question_Task_1)
                  00002e40    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00002ed8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00002f64    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002ff0    00000088     pid.o (.text.PID_init)
                  00003078    00000088     interrupt.o (.text.TIMG0_IRQHandler)
                  00003100    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003184    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003208    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000328a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000328c    0000007c     servo.o (.text.Servo_SetAngle)
                  00003308    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003384    00000078     key.o (.text.Key_Read)
                  000033fc    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  00003474    00000074     encoder.o (.text.Encoder_Get)
                  000034e8    00000074     Scheduler.o (.text.Scheduler_Run)
                  0000355c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000035d0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003644    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000036b6    00000070     oled_hardware_i2c.o (.text.OLED_ShowString)
                  00003726    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003792    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  000037fc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00003864    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000038cc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003934    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000399a    00000002            : _lock.c.obj (.text._nop)
                  0000399c    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00003a00    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003a62    00000002     --HOLE-- [fill = 0]
                  00003a64    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003ac6    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b24    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003b80    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003bdc    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003c38    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003c90    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003ce8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003d40    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003d96    00000002     --HOLE-- [fill = 0]
                  00003d98    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003dec    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003e40    00000054     main.o (.text.main)
                  00003e94    00000054     usart_app.o (.text.uart_task)
                  00003ee8    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003f3a    00000002     --HOLE-- [fill = 0]
                  00003f3c    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003f8c    00000050     clock.o (.text.SysTick_Config)
                  00003fdc    00000050     usart_app.o (.text.fputs)
                  0000402c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004078    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000040c4    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004110    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000415a    00000002     --HOLE-- [fill = 0]
                  0000415c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000041a6    0000004a     adc_app.o (.text.adc_getValue)
                  000041f0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004238    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004280    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  000042c8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000430c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004350    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00004394    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000043d6    00000002     --HOLE-- [fill = 0]
                  000043d8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004418    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004458    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004498    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000044d8    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004514    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004550    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000458c    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  000045c8    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  00004604    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004640    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000467c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000046b8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000046f4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000472e    00000002     --HOLE-- [fill = 0]
                  00004730    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000476a    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  000047a2    00000002     --HOLE-- [fill = 0]
                  000047a4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000047dc    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004814    00000034     beep.o (.text.Beep_Time_Control)
                  00004848    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000487c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000048b0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  000048e4    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00004914    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  00004944    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004974    00000030     clock.o (.text.mspm0_delay_ms)
                  000049a4    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  000049d4    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004a00    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  00004a2c    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00004a58    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004a84    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004ab0    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004adc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004b08    0000002c     usart_app.o (.text.fputc)
                  00004b34    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004b60    0000002c     app_tracing_control.o (.text.speed_control)
                  00004b8c    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004bb4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004bdc    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004c04    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004c2c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004c54    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004c7c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004ca4    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004cca    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004cf0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004d16    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004d3c    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004d60    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004d84    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004da8    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004dcc    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004dee    00000002     --HOLE-- [fill = 0]
                  00004df0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004e10    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004e30    00000020     pid.o (.text.pid_set_speed_target)
                  00004e50    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004e6e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004e8c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004eaa    00000002     --HOLE-- [fill = 0]
                  00004eac    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004ec8    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004ee4    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004f00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004f1c    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004f38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004f54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004f70    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004f8c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004fa8    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00004fc4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004fe0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004ffc    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005018    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005034    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005050    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000506c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005084    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000509c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000050b4    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  000050cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000050e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000050fc    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005114    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  0000512c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005144    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000515c    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005174    00000018     beep.o (.text.DL_GPIO_setPins)
                  0000518c    00000018     motor.o (.text.DL_GPIO_setPins)
                  000051a4    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  000051bc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000051d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000051ec    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005204    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000521c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005234    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000524c    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005264    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000527c    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005294    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  000052ac    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000052c4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000052dc    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000052f4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000530c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005324    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000533c    00000018     servo.o (.text.DL_Timer_startCounter)
                  00005354    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000536c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005384    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000539c    00000018     usart_app.o (.text.DL_UART_isBusy)
                  000053b4    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000053cc    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000053e4    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  000053fc    00000018     clock.o (.text.SysTick_Init)
                  00005414    00000018     libc.a : sprintf.c.obj (.text._outs)
                  0000542c    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00005442    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  00005458    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000546e    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00005484    00000016     key.o (.text.DL_GPIO_readPins)
                  0000549a    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  000054b0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000054c6    00000016     usart_app.o (.text.DL_UART_transmitData)
                  000054dc    00000016     encoder.o (.text.Encoder_Init)
                  000054f2    00000016     interrupt.o (.text.Timer_Init)
                  00005508    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000551e    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00005532    00000014     beep.o (.text.DL_GPIO_clearPins)
                  00005546    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000555a    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  0000556e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005582    00000002     --HOLE-- [fill = 0]
                  00005584    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00005598    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000055ac    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000055c0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000055d4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000055e8    00000014     interrupt.o (.text.DL_UART_receiveData)
                  000055fc    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005610    00000014     servo.o (.text.Servo_init)
                  00005624    00000014     app_question_task.o (.text.State_Machine_init)
                  00005638    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000564c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005660    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  00005672    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00005684    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005696    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000056a8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000056ba    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  000056ca    00000002     --HOLE-- [fill = 0]
                  000056cc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000056dc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000056ec    00000010     interrupt.o (.text.SysTick_Handler)
                  000056fc    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  0000570c    00000010     app_tracing_control.o (.text.servo_tracing)
                  0000571c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000572c    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  0000573a    00000002     --HOLE-- [fill = 0]
                  0000573c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000574a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005758    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005766    00000002     --HOLE-- [fill = 0]
                  00005768    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00005774    0000000c     app_tracing_control.o (.text.Tracing_Control)
                  00005780    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000578c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005796    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000057a0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000057b0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000057ba    0000000a     servo.o (.text.Servo_SetCenter)
                  000057c4    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  000057ce    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000057d6    00000002     --HOLE-- [fill = 0]
                  000057d8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000057e0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000057e8    00000008     libc.a : printf.c.obj (.text._outc)
                  000057f0    00000008            : printf.c.obj (.text._outs)
                  000057f8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000057fc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005800    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005810    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005814    00000004            : exit.c.obj (.text:abort)
                  00005818    00000008     --HOLE-- [fill = 0]

.cinit     0    00006210    00000098     
                  00006210    0000006f     (.cinit..data.load) [load image, compression = lzss]
                  0000627f    00000001     --HOLE-- [fill = 0]
                  00006280    0000000c     (__TI_handler_table)
                  0000628c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006294    00000010     (__TI_cinit_table)
                  000062a4    00000004     --HOLE-- [fill = 0]

.rodata    0    00005820    000009f0     
                  00005820    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005e10    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006038    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006040    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006141    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006144    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000616c    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00006184    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00006198    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000061a9    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000061ba    00000010     encoder.o (.rodata.encoder_table)
                  000061ca    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  000061d9    00000001     --HOLE-- [fill = 0]
                  000061da    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000061e4    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  000061ee    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000061f0    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000061f8    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  00006200    00000005     oled_hardware_i2c.o (.rodata.str1.113193128738702790041)
                  00006205    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00006208    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  0000620b    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000620d    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  0000620f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000033d     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000014     (.common:OLED_String)
                  202002f1    00000001     (.common:Digtal)
                  202002f2    00000001     (.common:Key_Down)
                  202002f3    00000001     (.common:Key_Old)
                  202002f4    00000014     (.common:State_Machine)
                  20200308    00000010     (.common:Normal)
                  20200318    00000008     (.common:grayscale_data)
                  20200320    00000004     (.common:start_time)
                  20200324    00000004     (.common:target_angle)
                  20200328    00000004     (.common:tick_ms)
                  2020032c    00000004     (.common:time_last)
                  20200330    00000004     (.common:tracing_val)
                  20200334    00000004     (.common:uart_rx_ticks)
                  20200338    00000001     (.common:Key_Up)
                  20200339    00000001     (.common:Key_Val)
                  2020033a    00000001     (.common:grayscale_count)
                  2020033b    00000001     (.common:task_num)
                  2020033c    00000001     (.common:uart_rx_index)

.data      0    20200340    000001e5     UNINITIALIZED
                  20200340    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200430    00000080     usart_app.o (.data.uart_rx_buffer)
                  202004b0    00000024     Scheduler.o (.data.scheduler_task)
                  202004d4    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004e4    00000010     Ganv_Grayscale.o (.data.black)
                  202004f4    00000010     Ganv_Grayscale.o (.data.white)
                  20200504    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200508    00000004            : _lock.c.obj (.data._lock)
                  2020050c    00000004            : _lock.c.obj (.data._unlock)
                  20200510    00000004     beep.o (.data.bee_time)
                  20200514    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  20200518    00000002     encoder.o (.data.encoder_A_count)
                  2020051a    00000002     encoder.o (.data.encoder_B_count)
                  2020051c    00000002     encoder.o (.data.encoder_count)
                  2020051e    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  2020051f    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200520    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200521    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200522    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200523    00000001     encoder.o (.data.encoder_count_flag)
                  20200524    00000001     app_question_task.o (.data.q1_first_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         172     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3336    335       320    
                                                               
    .\APP\
       app_tracing_check.o            236     0         7      
       app_question_task.o            182     0         29     
       app_tracing_control.o          72      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         490     0         36     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          310     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         310     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1400    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1728    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2346    2077      20     
    +--+------------------------------+-------+---------+---------+
       Total:                         2346    2077      20     
                                                               
    .\Hardware\PID\
       pid.o                          776     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         776     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5548    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2878    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       147       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22332   2881      1826   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006294 records: 2, size/record: 8, table size: 16
	.data: load addr=00006210, load size=0000006f bytes, run addr=20200340, run size=000001e5 bytes, compression=lzss
	.bss: load addr=0000628c, load size=00000008 bytes, run addr=20200000, run size=0000033d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006280 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000139d     000057a0     0000579e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004c7d     00005800     000057fc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000057f9  ADC0_IRQHandler                      
000057f9  ADC1_IRQHandler                      
000057f9  AES_IRQHandler                       
202004d4  Anolog                               
00004815  Beep_Time_Control                    
00005814  C$$EXIT                              
000057f9  CANFD0_IRQHandler                    
000057f9  DAC0_IRQHandler                      
000043d9  DL_ADC12_setClockConfig              
0000578d  DL_Common_delayCycles                
0000402d  DL_DMA_initChannel                   
00003ac7  DL_I2C_fillControllerTXFIFO          
00004d17  DL_I2C_setClockConfig                
00002591  DL_SYSCTL_configSYSPLL               
000042c9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000020dd  DL_Timer_initFourCCPWMMode           
000023c5  DL_Timer_initTimerMode               
00005019  DL_Timer_setCaptCompUpdateMethod     
00005325  DL_Timer_setCaptureCompareOutCtl     
000056dd  DL_Timer_setCaptureCompareValue      
00005035  DL_Timer_setClockConfig              
00003d99  DL_UART_drainRXFIFO                  
000041f1  DL_UART_init                         
00005685  DL_UART_setClockConfig               
000057f9  DMA_IRQHandler                       
000057f9  Default_Handler                      
202002f1  Digtal                               
00003475  Encoder_Get                          
000054dd  Encoder_Init                         
000057f9  GROUP0_IRQHandler                    
00001a55  GROUP1_IRQHandler                    
0000281d  Get_Analog_value                     
0000458d  Get_Anolog_Value                     
0000572d  Get_Digtal_For_User                  
0000476b  Get_Normalize_For_User               
000057f9  HardFault_Handler                    
000057f9  I2C0_IRQHandler                      
000057f9  I2C1_IRQHandler                      
202002f2  Key_Down                             
202002f3  Key_Old                              
00002bb9  Key_Proc                             
00003385  Key_Read                             
20200338  Key_Up                               
20200339  Key_Val                              
000057f9  NMI_Handler                          
00001531  No_MCU_Ganv_Sensor_Init              
00003645  No_MCU_Ganv_Sensor_Init_Frist        
00004395  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200308  Normal                               
00003793  OLED_Clear                           
00001db1  OLED_Init                            
000045c9  OLED_Set_Pos                         
00001c99  OLED_ShowChar                        
000021e1  OLED_ShowNum                         
000036b7  OLED_ShowString                      
202002dd  OLED_String                          
00002e41  OLED_WR_Byte                         
00001ec1  Oled_Task                            
00002ff1  PID_init                             
0000192d  PID_speed_realize                    
000057f9  PendSV_Handler                       
00002da5  Question_Task_1                      
0000152f  Question_Task_2                      
000022d7  Question_Task_3                      
00002bb7  Question_Task_4                      
000057f9  RTC_IRQHandler                       
000057fd  Reset_Handler                        
000057f9  SPI0_IRQHandler                      
000057f9  SPI1_IRQHandler                      
000057f9  SVC_Handler                          
000040c5  SYSCFG_DL_ADC1_init                  
000053e5  SYSCFG_DL_DMA_WIT_init               
000057cf  SYSCFG_DL_DMA_init                   
000048b1  SYSCFG_DL_FOR_CONTROL_init           
000011f9  SYSCFG_DL_GPIO_init                  
00003c39  SYSCFG_DL_I2C_OLED_init              
00002ed9  SYSCFG_DL_PWM_MOTOR_init             
000037fd  SYSCFG_DL_PWM_SERVO_init             
00004605  SYSCFG_DL_SYSCTL_init                
00003ded  SYSCFG_DL_UART_0_init                
00003865  SYSCFG_DL_UART_WIT_init              
0000430d  SYSCFG_DL_init                       
000029b1  SYSCFG_DL_initPower                  
00005769  Scheduler_Init                       
000034e9  Scheduler_Run                        
0000328d  Servo_SetAngle                       
000057bb  Servo_SetCenter                      
00005611  Servo_init                           
202002f4  State_Machine                        
00005625  State_Machine_init                   
000056ed  SysTick_Handler                      
000053fd  SysTick_Init                         
000057f9  TIMA0_IRQHandler                     
000057f9  TIMA1_IRQHandler                     
00003079  TIMG0_IRQHandler                     
000057f9  TIMG12_IRQHandler                    
000057f9  TIMG6_IRQHandler                     
000057f9  TIMG7_IRQHandler                     
000057f9  TIMG8_IRQHandler                     
00005697  TI_memcpy_small                      
00005759  TI_memset_small                      
000054f3  Timer_Init                           
00005775  Tracing_Control                      
000017f5  Tracing_Value_Get                    
00004351  UART0_IRQHandler                     
000057f9  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
000057f9  UART3_IRQHandler                     
000033fd  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006294  __TI_CINIT_Base                      
000062a4  __TI_CINIT_Limit                     
000062a4  __TI_CINIT_Warm                      
00006280  __TI_Handler_Table_Base              
0000628c  __TI_Handler_Table_Limit             
000046b9  __TI_auto_init_nobinit_nopinit       
00003309  __TI_decompress_lzss                 
000056a9  __TI_decompress_none                 
00003c91  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005509  __TI_zero_init_nomemset              
000013a7  __adddf3                             
0000274f  __addsf3                             
00006040  __aeabi_ctype_table_                 
00006040  __aeabi_ctype_table_C                
000035d1  __aeabi_d2f                          
0000415d  __aeabi_d2iz                         
000013a7  __aeabi_dadd                         
00003a01  __aeabi_dcmpeq                       
00003a3d  __aeabi_dcmpge                       
00003a51  __aeabi_dcmpgt                       
00003a29  __aeabi_dcmple                       
00003a15  __aeabi_dcmplt                       
00001fd1  __aeabi_ddiv                         
000024ad  __aeabi_dmul                         
0000139d  __aeabi_dsub                         
20200504  __aeabi_errno                        
000057d9  __aeabi_errno_addr                   
00004459  __aeabi_f2d                          
000047a5  __aeabi_f2iz                         
0000274f  __aeabi_fadd                         
00003a65  __aeabi_fcmpeq                       
00003aa1  __aeabi_fcmpge                       
00003ab5  __aeabi_fcmpgt                       
00003a8d  __aeabi_fcmple                       
00003a79  __aeabi_fcmplt                       
00003209  __aeabi_fdiv                         
00002f65  __aeabi_fmul                         
00002745  __aeabi_fsub                         
00004add  __aeabi_i2d                          
00004641  __aeabi_i2f                          
00003d41  __aeabi_idiv                         
00002d03  __aeabi_idiv0                        
00003d41  __aeabi_idivmod                      
0000328b  __aeabi_ldiv0                        
00004e8d  __aeabi_llsl                         
00004da9  __aeabi_lmul                         
00005781  __aeabi_memclr                       
00005781  __aeabi_memclr4                      
00005781  __aeabi_memclr8                      
000057e1  __aeabi_memcpy                       
000057e1  __aeabi_memcpy4                      
000057e1  __aeabi_memcpy8                      
0000573d  __aeabi_memset                       
0000573d  __aeabi_memset4                      
0000573d  __aeabi_memset8                      
00004d85  __aeabi_ui2d                         
00004419  __aeabi_uidiv                        
00004419  __aeabi_uidivmod                     
00005639  __aeabi_uldivmod                     
00004e8d  __ashldi3                            
ffffffff  __binit__                            
000038cd  __cmpdf2                             
000046f5  __cmpsf2                             
00001fd1  __divdf3                             
00003209  __divsf3                             
000038cd  __eqdf2                              
000046f5  __eqsf2                              
00004459  __extendsfdf2                        
0000415d  __fixdfsi                            
000047a5  __fixsfsi                            
00004add  __floatsidf                          
00004641  __floatsisf                          
00004d85  __floatunsidf                        
0000355d  __gedf2                              
0000467d  __gesf2                              
0000355d  __gtdf2                              
0000467d  __gtsf2                              
000038cd  __ledf2                              
000046f5  __lesf2                              
000038cd  __ltdf2                              
000046f5  __ltsf2                              
UNDEFED   __mpu_init                           
000024ad  __muldf3                             
00004da9  __muldi3                             
00004731  __muldsi3                            
00002f65  __mulsf3                             
000038cd  __nedf2                              
000046f5  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000139d  __subdf3                             
00002745  __subsf3                             
000035d1  __truncdfsf2                         
00002c61  __udivmoddi4                         
00004c7d  _c_int00_noargs                      
20200340  _ftable                              
20200508  _lock                                
0000399b  _nop                                 
UNDEFED   _system_post_cinit                   
00005811  _system_pre_init                     
2020050c  _unlock                              
00005815  abort                                
000041a7  adc_getValue                         
2020021c  angle_pid                            
00005e10  asc2_0806                            
00005820  asc2_1608                            
00004499  atoi                                 
20200510  bee_time                             
ffffffff  binit                                
202004e4  black                                
00003727  convertAnalogToDigital               
000056fd  delay_ms                             
000022d9  detect_trace_state_change            
20200518  encoder_A_count                      
2020051a  encoder_B_count                      
2020051c  encoder_count                        
20200523  encoder_count_flag                   
00004b09  fputc                                
00003fdd  fputs                                
00003b25  frexp                                
00003b25  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
00004239  gray_init                            
00002d05  gray_task                            
2020033a  grayscale_count                      
20200318  grayscale_data                       
00000000  interruptVectors                     
0000266d  ldexp                                
0000266d  ldexpl                               
00003e41  main                                 
00004dcd  memccpy                              
000028ed  motor_direction                      
00004975  mspm0_delay_ms                       
00004b35  mspm0_get_clock_ms                   
00002b0d  normalizeAnalogValues                
0000399d  oled_i2c_sda_unlock                  
000049a5  oled_pow                             
00004e31  pid_set_speed_target                 
00003bdd  printf                               
00002a61  pwm_set                              
20200524  q1_first_flag                        
0000266d  scalbn                               
0000266d  scalbnl                              
20200000  sensor                               
0000570d  servo_tracing                        
20200244  speedA_pid                           
2020026c  speedB_pid                           
00004b61  speed_control                        
000047dd  sprintf                              
20200320  start_time                           
20200324  target_angle                         
2020033b  task_num                             
20200328  tick_ms                              
2020032c  time_last                            
20200294  tracing_pid                          
20200330  tracing_val                          
20200430  uart_rx_buffer                       
2020033c  uart_rx_index                        
20200334  uart_rx_ticks                        
00003e95  uart_task                            
0000571d  wcslen                               
202004f4  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  SYSCFG_DL_GPIO_init                  
0000139d  __aeabi_dsub                         
0000139d  __subdf3                             
000013a7  __adddf3                             
000013a7  __aeabi_dadd                         
0000152f  Question_Task_2                      
00001531  No_MCU_Ganv_Sensor_Init              
000017f5  Tracing_Value_Get                    
0000192d  PID_speed_realize                    
00001a55  GROUP1_IRQHandler                    
00001c99  OLED_ShowChar                        
00001db1  OLED_Init                            
00001ec1  Oled_Task                            
00001fd1  __aeabi_ddiv                         
00001fd1  __divdf3                             
000020dd  DL_Timer_initFourCCPWMMode           
000021e1  OLED_ShowNum                         
000022d7  Question_Task_3                      
000022d9  detect_trace_state_change            
000023c5  DL_Timer_initTimerMode               
000024ad  __aeabi_dmul                         
000024ad  __muldf3                             
00002591  DL_SYSCTL_configSYSPLL               
0000266d  ldexp                                
0000266d  ldexpl                               
0000266d  scalbn                               
0000266d  scalbnl                              
00002745  __aeabi_fsub                         
00002745  __subsf3                             
0000274f  __addsf3                             
0000274f  __aeabi_fadd                         
0000281d  Get_Analog_value                     
000028ed  motor_direction                      
000029b1  SYSCFG_DL_initPower                  
00002a61  pwm_set                              
00002b0d  normalizeAnalogValues                
00002bb7  Question_Task_4                      
00002bb9  Key_Proc                             
00002c61  __udivmoddi4                         
00002d03  __aeabi_idiv0                        
00002d05  gray_task                            
00002da5  Question_Task_1                      
00002e41  OLED_WR_Byte                         
00002ed9  SYSCFG_DL_PWM_MOTOR_init             
00002f65  __aeabi_fmul                         
00002f65  __mulsf3                             
00002ff1  PID_init                             
00003079  TIMG0_IRQHandler                     
00003209  __aeabi_fdiv                         
00003209  __divsf3                             
0000328b  __aeabi_ldiv0                        
0000328d  Servo_SetAngle                       
00003309  __TI_decompress_lzss                 
00003385  Key_Read                             
000033fd  WIT_Get_Relative_Yaw                 
00003475  Encoder_Get                          
000034e9  Scheduler_Run                        
0000355d  __gedf2                              
0000355d  __gtdf2                              
000035d1  __aeabi_d2f                          
000035d1  __truncdfsf2                         
00003645  No_MCU_Ganv_Sensor_Init_Frist        
000036b7  OLED_ShowString                      
00003727  convertAnalogToDigital               
00003793  OLED_Clear                           
000037fd  SYSCFG_DL_PWM_SERVO_init             
00003865  SYSCFG_DL_UART_WIT_init              
000038cd  __cmpdf2                             
000038cd  __eqdf2                              
000038cd  __ledf2                              
000038cd  __ltdf2                              
000038cd  __nedf2                              
0000399b  _nop                                 
0000399d  oled_i2c_sda_unlock                  
00003a01  __aeabi_dcmpeq                       
00003a15  __aeabi_dcmplt                       
00003a29  __aeabi_dcmple                       
00003a3d  __aeabi_dcmpge                       
00003a51  __aeabi_dcmpgt                       
00003a65  __aeabi_fcmpeq                       
00003a79  __aeabi_fcmplt                       
00003a8d  __aeabi_fcmple                       
00003aa1  __aeabi_fcmpge                       
00003ab5  __aeabi_fcmpgt                       
00003ac7  DL_I2C_fillControllerTXFIFO          
00003b25  frexp                                
00003b25  frexpl                               
00003bdd  printf                               
00003c39  SYSCFG_DL_I2C_OLED_init              
00003c91  __TI_ltoa                            
00003d41  __aeabi_idiv                         
00003d41  __aeabi_idivmod                      
00003d99  DL_UART_drainRXFIFO                  
00003ded  SYSCFG_DL_UART_0_init                
00003e41  main                                 
00003e95  uart_task                            
00003fdd  fputs                                
0000402d  DL_DMA_initChannel                   
000040c5  SYSCFG_DL_ADC1_init                  
0000415d  __aeabi_d2iz                         
0000415d  __fixdfsi                            
000041a7  adc_getValue                         
000041f1  DL_UART_init                         
00004239  gray_init                            
000042c9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000430d  SYSCFG_DL_init                       
00004351  UART0_IRQHandler                     
00004395  No_Mcu_Ganv_Sensor_Task_Without_tick 
000043d9  DL_ADC12_setClockConfig              
00004419  __aeabi_uidiv                        
00004419  __aeabi_uidivmod                     
00004459  __aeabi_f2d                          
00004459  __extendsfdf2                        
00004499  atoi                                 
0000458d  Get_Anolog_Value                     
000045c9  OLED_Set_Pos                         
00004605  SYSCFG_DL_SYSCTL_init                
00004641  __aeabi_i2f                          
00004641  __floatsisf                          
0000467d  __gesf2                              
0000467d  __gtsf2                              
000046b9  __TI_auto_init_nobinit_nopinit       
000046f5  __cmpsf2                             
000046f5  __eqsf2                              
000046f5  __lesf2                              
000046f5  __ltsf2                              
000046f5  __nesf2                              
00004731  __muldsi3                            
0000476b  Get_Normalize_For_User               
000047a5  __aeabi_f2iz                         
000047a5  __fixsfsi                            
000047dd  sprintf                              
00004815  Beep_Time_Control                    
000048b1  SYSCFG_DL_FOR_CONTROL_init           
00004975  mspm0_delay_ms                       
000049a5  oled_pow                             
00004add  __aeabi_i2d                          
00004add  __floatsidf                          
00004b09  fputc                                
00004b35  mspm0_get_clock_ms                   
00004b61  speed_control                        
00004c7d  _c_int00_noargs                      
00004d17  DL_I2C_setClockConfig                
00004d85  __aeabi_ui2d                         
00004d85  __floatunsidf                        
00004da9  __aeabi_lmul                         
00004da9  __muldi3                             
00004dcd  memccpy                              
00004e31  pid_set_speed_target                 
00004e8d  __aeabi_llsl                         
00004e8d  __ashldi3                            
00005019  DL_Timer_setCaptCompUpdateMethod     
00005035  DL_Timer_setClockConfig              
00005325  DL_Timer_setCaptureCompareOutCtl     
000053e5  SYSCFG_DL_DMA_WIT_init               
000053fd  SysTick_Init                         
000054dd  Encoder_Init                         
000054f3  Timer_Init                           
00005509  __TI_zero_init_nomemset              
00005611  Servo_init                           
00005625  State_Machine_init                   
00005639  __aeabi_uldivmod                     
00005685  DL_UART_setClockConfig               
00005697  TI_memcpy_small                      
000056a9  __TI_decompress_none                 
000056dd  DL_Timer_setCaptureCompareValue      
000056ed  SysTick_Handler                      
000056fd  delay_ms                             
0000570d  servo_tracing                        
0000571d  wcslen                               
0000572d  Get_Digtal_For_User                  
0000573d  __aeabi_memset                       
0000573d  __aeabi_memset4                      
0000573d  __aeabi_memset8                      
00005759  TI_memset_small                      
00005769  Scheduler_Init                       
00005775  Tracing_Control                      
00005781  __aeabi_memclr                       
00005781  __aeabi_memclr4                      
00005781  __aeabi_memclr8                      
0000578d  DL_Common_delayCycles                
000057bb  Servo_SetCenter                      
000057cf  SYSCFG_DL_DMA_init                   
000057d9  __aeabi_errno_addr                   
000057e1  __aeabi_memcpy                       
000057e1  __aeabi_memcpy4                      
000057e1  __aeabi_memcpy8                      
000057f9  ADC0_IRQHandler                      
000057f9  ADC1_IRQHandler                      
000057f9  AES_IRQHandler                       
000057f9  CANFD0_IRQHandler                    
000057f9  DAC0_IRQHandler                      
000057f9  DMA_IRQHandler                       
000057f9  Default_Handler                      
000057f9  GROUP0_IRQHandler                    
000057f9  HardFault_Handler                    
000057f9  I2C0_IRQHandler                      
000057f9  I2C1_IRQHandler                      
000057f9  NMI_Handler                          
000057f9  PendSV_Handler                       
000057f9  RTC_IRQHandler                       
000057f9  SPI0_IRQHandler                      
000057f9  SPI1_IRQHandler                      
000057f9  SVC_Handler                          
000057f9  TIMA0_IRQHandler                     
000057f9  TIMA1_IRQHandler                     
000057f9  TIMG12_IRQHandler                    
000057f9  TIMG6_IRQHandler                     
000057f9  TIMG7_IRQHandler                     
000057f9  TIMG8_IRQHandler                     
000057f9  UART1_IRQHandler                     
000057f9  UART3_IRQHandler                     
000057fd  Reset_Handler                        
00005811  _system_pre_init                     
00005814  C$$EXIT                              
00005815  abort                                
00005820  asc2_1608                            
00005e10  asc2_0806                            
00006040  __aeabi_ctype_table_                 
00006040  __aeabi_ctype_table_C                
00006280  __TI_Handler_Table_Base              
0000628c  __TI_Handler_Table_Limit             
00006294  __TI_CINIT_Base                      
000062a4  __TI_CINIT_Limit                     
000062a4  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  OLED_String                          
202002f1  Digtal                               
202002f2  Key_Down                             
202002f3  Key_Old                              
202002f4  State_Machine                        
20200308  Normal                               
20200318  grayscale_data                       
20200320  start_time                           
20200324  target_angle                         
20200328  tick_ms                              
2020032c  time_last                            
20200330  tracing_val                          
20200334  uart_rx_ticks                        
20200338  Key_Up                               
20200339  Key_Val                              
2020033a  grayscale_count                      
2020033b  task_num                             
2020033c  uart_rx_index                        
20200340  _ftable                              
20200430  uart_rx_buffer                       
202004d4  Anolog                               
202004e4  black                                
202004f4  white                                
20200504  __aeabi_errno                        
20200508  _lock                                
2020050c  _unlock                              
20200510  bee_time                             
20200518  encoder_A_count                      
2020051a  encoder_B_count                      
2020051c  encoder_count                        
20200523  encoder_count_flag                   
20200524  q1_first_flag                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[293 symbols]
