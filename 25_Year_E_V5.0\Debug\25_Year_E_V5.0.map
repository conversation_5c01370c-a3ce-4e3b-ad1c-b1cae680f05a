******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Aug  2 02:13:13 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004d21


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006320  00019ce0  R  X
  SRAM                  20200000   00008000  00000716  000078ea  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006320   00006320    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000057c0   000057c0    r-x .text
  00005880    00005880    00000a00   00000a00    r-- .rodata
  00006280    00006280    000000a0   000000a0    r-- .cinit
20200000    20200000    00000519   00000000    rw-
  20200000    20200000    00000321   00000000    rw- .bss
  20200324    20200324    000001f5   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000057c0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000152e    00000002     app_question_task.o (.text.Question_Task_2)
                  00001530    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000016b8    00000160     app_question_task.o (.text.Question_Task_1)
                  00001818    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001954    00000134     pid.o (.text.Tracing_Value_Get)
                  00001a88    00000128     pid.o (.text.PID_speed_realize)
                  00001bb0    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001cd4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001df4    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001f0c    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  0000201c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002128    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000222c    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  00002322    00000002     app_question_task.o (.text.Question_Task_3)
                  00002324    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000240c    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  000024f4    000000e8     app_tracing_check.o (.text.detect_trace_state_change)
                  000025dc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000026c0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000279c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002874    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000294c    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00002a1c    000000c4     key.o (.text.Key_Proc)
                  00002ae0    000000c4     motor.o (.text.motor_direction)
                  00002ba4    000000b8     interrupt.o (.text.TIMG0_IRQHandler)
                  00002c5c    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002d0c    000000ac     motor.o (.text.pwm_set)
                  00002db8    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002e62    00000002     app_question_task.o (.text.Question_Task_4)
                  00002e64    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002f06    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002f08    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002fa8    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00003040    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  000030cc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003158    00000088     pid.o (.text.PID_init)
                  000031e0    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003264    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000032e8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000336a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000336c    0000007c     servo.o (.text.Servo_SetAngle)
                  000033e8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003464    00000078     key.o (.text.Key_Read)
                  000034dc    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  00003554    00000074     encoder.o (.text.Encoder_Get)
                  000035c8    00000074     Scheduler.o (.text.Scheduler_Run)
                  0000363c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000036b0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003724    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003796    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003802    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000386c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  000038d4    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  0000393c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000039a4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a0a    00000002            : _lock.c.obj (.text._nop)
                  00003a0c    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00003a70    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003ad2    00000002     --HOLE-- [fill = 0]
                  00003ad4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003b36    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b94    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003bf0    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003c4c    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003ca8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003d00    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003d58    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003db0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003e06    00000002     --HOLE-- [fill = 0]
                  00003e08    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003e5c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003eb0    00000054     usart_app.o (.text.uart_task)
                  00003f04    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003f56    00000002     --HOLE-- [fill = 0]
                  00003f58    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003fa8    00000050     clock.o (.text.SysTick_Config)
                  00003ff8    00000050     usart_app.o (.text.fputs)
                  00004048    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004094    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000040e0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  0000412c    0000004c     app_tracing_control.o (.text.Tracing_Control)
                  00004178    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000041c2    00000002     --HOLE-- [fill = 0]
                  000041c4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000420e    0000004a     adc_app.o (.text.adc_getValue)
                  00004258    0000004a     main.o (.text.main)
                  000042a2    00000002     --HOLE-- [fill = 0]
                  000042a4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000042ec    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00004334    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  0000437c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000043c0    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004404    00000044     usart_app.o (.text.UART0_IRQHandler)
                  00004448    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000448a    00000002     --HOLE-- [fill = 0]
                  0000448c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000044cc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000450c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000454c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000458c    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000045c8    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004604    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004640    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  0000467c    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000046b8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000046f4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004730    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000476c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000047a8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000047e2    00000002     --HOLE-- [fill = 0]
                  000047e4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000481e    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  00004856    00000002     --HOLE-- [fill = 0]
                  00004858    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004890    00000034     beep.o (.text.Beep_Time_Control)
                  000048c4    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000048f8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000492c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00004960    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00004990    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  000049c0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000049f0    00000030     clock.o (.text.mspm0_delay_ms)
                  00004a20    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00004a50    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004a7c    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  00004aa8    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00004ad4    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004b00    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004b2c    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004b58    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004b84    0000002c     usart_app.o (.text.fputc)
                  00004bb0    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004bdc    0000002c     app_tracing_control.o (.text.speed_control)
                  00004c08    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004c30    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004c58    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004c80    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004ca8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004cd0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004cf8    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004d20    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004d48    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004d6e    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004d94    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004dba    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004de0    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004e04    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004e28    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004e4c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004e70    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004e92    00000002     --HOLE-- [fill = 0]
                  00004e94    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004eb4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004ed4    00000020     pid.o (.text.pid_set_speed_target)
                  00004ef4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004f12    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004f30    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004f4e    00000002     --HOLE-- [fill = 0]
                  00004f50    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004f6c    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004f88    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004fa4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004fc0    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004fdc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004ff8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005014    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005030    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000504c    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005068    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005084    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000050a0    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000050bc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000050d8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000050f4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005110    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005128    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005140    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005158    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005170    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005188    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000051a0    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000051b8    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  000051d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000051e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005200    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005218    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005230    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005248    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005260    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005278    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005290    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  000052a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000052c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000052d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000052f0    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00005308    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005320    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005338    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00005350    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005368    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005380    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005398    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000053b0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000053c8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000053e0    00000018     servo.o (.text.DL_Timer_startCounter)
                  000053f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005410    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005428    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005440    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00005458    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005470    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005488    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  000054a0    00000018     clock.o (.text.SysTick_Init)
                  000054b8    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  000054ce    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  000054e4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000054fa    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00005510    00000016     key.o (.text.DL_GPIO_readPins)
                  00005526    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  0000553c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005552    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00005568    00000016     encoder.o (.text.Encoder_Init)
                  0000557e    00000016     interrupt.o (.text.Timer_Init)
                  00005594    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000055aa    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  000055be    00000014     beep.o (.text.DL_GPIO_clearPins)
                  000055d2    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000055e6    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  000055fa    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000560e    00000002     --HOLE-- [fill = 0]
                  00005610    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  00005624    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005638    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000564c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005660    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005674    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005688    00000014     usart_app.o (.text.DL_UART_receiveData)
                  0000569c    00000014     servo.o (.text.Servo_init)
                  000056b0    00000014     app_question_task.o (.text.State_Machine_init)
                  000056c4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000056d8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000056ec    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  000056fe    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00005710    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005722    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005734    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005746    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  00005756    00000002     --HOLE-- [fill = 0]
                  00005758    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005768    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005778    00000010     interrupt.o (.text.SysTick_Handler)
                  00005788    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005798    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000057a8    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  000057b6    00000002     --HOLE-- [fill = 0]
                  000057b8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000057c6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000057d4    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000057e2    00000002     --HOLE-- [fill = 0]
                  000057e4    0000000c     Scheduler.o (.text.Scheduler_Init)
                  000057f0    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000057fc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005806    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005810    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005820    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000582a    0000000a     servo.o (.text.Servo_SetCenter)
                  00005834    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000583c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005844    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000584c    00000008     libc.a : printf.c.obj (.text._outc)
                  00005854    00000008            : printf.c.obj (.text._outs)
                  0000585c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005860    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005864    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005874    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005878    00000004            : exit.c.obj (.text:abort)
                  0000587c    00000004     --HOLE-- [fill = 0]

.cinit     0    00006280    000000a0     
                  00006280    0000007a     (.cinit..data.load) [load image, compression = lzss]
                  000062fa    00000002     --HOLE-- [fill = 0]
                  000062fc    0000000c     (__TI_handler_table)
                  00006308    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006310    00000010     (__TI_cinit_table)

.rodata    0    00005880    00000a00     
                  00005880    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005e70    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006098    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000060a0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000061a1    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  000061a4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000061cc    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  000061e4    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  000061f8    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006209    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  0000621a    00000010     encoder.o (.rodata.encoder_table)
                  0000622a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006239    0000000c     interrupt.o (.rodata.str1.146019215406595515531)
                  00006245    00000001     --HOLE-- [fill = 0]
                  00006246    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006250    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  0000625a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000625c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006264    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  0000626c    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  0000626f    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00006272    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006274    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006276    0000000a     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000321     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:tracing_val)
                  20200318    00000004     (.common:uart_rx_ticks)
                  2020031c    00000001     (.common:Key_Up)
                  2020031d    00000001     (.common:Key_Val)
                  2020031e    00000001     (.common:grayscale_count)
                  2020031f    00000001     (.common:task_num)
                  20200320    00000001     (.common:uart_rx_index)

.data      0    20200324    000001f5     UNINITIALIZED
                  20200324    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200414    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200494    00000024     Scheduler.o (.data.scheduler_task)
                  202004b8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004c8    00000010     Ganv_Grayscale.o (.data.black)
                  202004d8    00000010     Ganv_Grayscale.o (.data.white)
                  202004e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004ec    00000004            : _lock.c.obj (.data._lock)
                  202004f0    00000004            : _lock.c.obj (.data._unlock)
                  202004f4    00000004     beep.o (.data.bee_time)
                  202004f8    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004fc    00000004     app_question_task.o (.data.q1_smooth_start_time)
                  20200500    00000004     motor.o (.data.speed_basic)
                  20200504    00000002     encoder.o (.data.encoder_A_count)
                  20200506    00000002     encoder.o (.data.encoder_B_count)
                  20200508    00000002     encoder.o (.data.encoder_count)
                  2020050a    00000002     app_question_task.o (.data.q1_min_speed)
                  2020050c    00000002     app_question_task.o (.data.q1_smooth_duration)
                  2020050e    00000002     app_question_task.o (.data.q1_target_speed)
                  20200510    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200511    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200512    00000001     app_question_task.o (.data.circle_num)
                  20200513    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200514    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200515    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200516    00000001     encoder.o (.data.encoder_count_flag)
                  20200517    00000001     app_question_task.o (.data.q1_first_flag)
                  20200518    00000001     app_question_task.o (.data.turn_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_question_task.o            378     0         33     
       app_tracing_check.o            232     0         7      
       app_tracing_control.o          120     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         730     0         40     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1448    12        0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1776    12        8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         4      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2194    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2194    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          772     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         772     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5458    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2918    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       158       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22432   2899      1814   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006310 records: 2, size/record: 8, table size: 16
	.data: load addr=00006280, load size=0000007a bytes, run addr=20200324, run size=000001f5 bytes, compression=lzss
	.bss: load addr=00006308, load size=00000008 bytes, run addr=20200000, run size=00000321 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000062fc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000139d     00005810     0000580e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004d21     00005864     00005860   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000585d  ADC0_IRQHandler                      
0000585d  ADC1_IRQHandler                      
0000585d  AES_IRQHandler                       
202004b8  Anolog                               
00004891  Beep_Time_Control                    
00005878  C$$EXIT                              
0000585d  CANFD0_IRQHandler                    
0000585d  DAC0_IRQHandler                      
0000448d  DL_ADC12_setClockConfig              
000057fd  DL_Common_delayCycles                
00004049  DL_DMA_initChannel                   
00003b37  DL_I2C_fillControllerTXFIFO          
00004dbb  DL_I2C_setClockConfig                
000026c1  DL_SYSCTL_configSYSPLL               
0000437d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002129  DL_Timer_initFourCCPWMMode           
00002325  DL_Timer_initTimerMode               
000050bd  DL_Timer_setCaptCompUpdateMethod     
000053c9  DL_Timer_setCaptureCompareOutCtl     
00005769  DL_Timer_setCaptureCompareValue      
000050d9  DL_Timer_setClockConfig              
00003e09  DL_UART_drainRXFIFO                  
000042a5  DL_UART_init                         
00005711  DL_UART_setClockConfig               
0000585d  DMA_IRQHandler                       
0000585d  Default_Handler                      
202002dd  Digtal                               
00003555  Encoder_Get                          
00005569  Encoder_Init                         
0000585d  GROUP0_IRQHandler                    
00001bb1  GROUP1_IRQHandler                    
0000294d  Get_Analog_value                     
00004641  Get_Anolog_Value                     
000057a9  Get_Digtal_For_User                  
0000481f  Get_Normalize_For_User               
0000585d  HardFault_Handler                    
0000585d  I2C0_IRQHandler                      
0000585d  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
00002a1d  Key_Proc                             
00003465  Key_Read                             
2020031c  Key_Up                               
2020031d  Key_Val                              
0000585d  NMI_Handler                          
00001531  No_MCU_Ganv_Sensor_Init              
00003725  No_MCU_Ganv_Sensor_Init_Frist        
00004449  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
00003803  OLED_Clear                           
00001f0d  OLED_Init                            
0000467d  OLED_Set_Pos                         
00001df5  OLED_ShowChar                        
0000222d  OLED_ShowNum                         
00002fa9  OLED_WR_Byte                         
0000240d  Oled_Task                            
00003159  PID_init                             
00001a89  PID_speed_realize                    
0000585d  PendSV_Handler                       
000016b9  Question_Task_1                      
0000152f  Question_Task_2                      
00002323  Question_Task_3                      
00002e63  Question_Task_4                      
0000585d  RTC_IRQHandler                       
00005861  Reset_Handler                        
0000585d  SPI0_IRQHandler                      
0000585d  SPI1_IRQHandler                      
0000585d  SVC_Handler                          
000040e1  SYSCFG_DL_ADC1_init                  
00005489  SYSCFG_DL_DMA_WIT_init               
00005835  SYSCFG_DL_DMA_init                   
0000492d  SYSCFG_DL_FOR_CONTROL_init           
000011f9  SYSCFG_DL_GPIO_init                  
00003ca9  SYSCFG_DL_I2C_OLED_init              
00003041  SYSCFG_DL_PWM_MOTOR_init             
0000386d  SYSCFG_DL_PWM_SERVO_init             
000046b9  SYSCFG_DL_SYSCTL_init                
00003e5d  SYSCFG_DL_UART_0_init                
000038d5  SYSCFG_DL_UART_WIT_init              
000043c1  SYSCFG_DL_init                       
00002c5d  SYSCFG_DL_initPower                  
000057e5  Scheduler_Init                       
000035c9  Scheduler_Run                        
0000336d  Servo_SetAngle                       
0000582b  Servo_SetCenter                      
0000569d  Servo_init                           
202002e0  State_Machine                        
000056b1  State_Machine_init                   
00005779  SysTick_Handler                      
000054a1  SysTick_Init                         
0000585d  TIMA0_IRQHandler                     
0000585d  TIMA1_IRQHandler                     
00002ba5  TIMG0_IRQHandler                     
0000585d  TIMG12_IRQHandler                    
0000585d  TIMG6_IRQHandler                     
0000585d  TIMG7_IRQHandler                     
0000585d  TIMG8_IRQHandler                     
00005723  TI_memcpy_small                      
000057d5  TI_memset_small                      
0000557f  Timer_Init                           
0000412d  Tracing_Control                      
00001955  Tracing_Value_Get                    
00004405  UART0_IRQHandler                     
0000585d  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
0000585d  UART3_IRQHandler                     
000034dd  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006310  __TI_CINIT_Base                      
00006320  __TI_CINIT_Limit                     
00006320  __TI_CINIT_Warm                      
000062fc  __TI_Handler_Table_Base              
00006308  __TI_Handler_Table_Limit             
0000476d  __TI_auto_init_nobinit_nopinit       
000033e9  __TI_decompress_lzss                 
00005735  __TI_decompress_none                 
00003d01  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005595  __TI_zero_init_nomemset              
000013a7  __adddf3                             
0000287f  __addsf3                             
000060a0  __aeabi_ctype_table_                 
000060a0  __aeabi_ctype_table_C                
000036b1  __aeabi_d2f                          
000041c5  __aeabi_d2iz                         
000013a7  __aeabi_dadd                         
00003a71  __aeabi_dcmpeq                       
00003aad  __aeabi_dcmpge                       
00003ac1  __aeabi_dcmpgt                       
00003a99  __aeabi_dcmple                       
00003a85  __aeabi_dcmplt                       
0000201d  __aeabi_ddiv                         
000025dd  __aeabi_dmul                         
0000139d  __aeabi_dsub                         
202004e8  __aeabi_errno                        
0000583d  __aeabi_errno_addr                   
0000450d  __aeabi_f2d                          
00004859  __aeabi_f2iz                         
0000287f  __aeabi_fadd                         
00003ad5  __aeabi_fcmpeq                       
00003b11  __aeabi_fcmpge                       
00003b25  __aeabi_fcmpgt                       
00003afd  __aeabi_fcmple                       
00003ae9  __aeabi_fcmplt                       
000032e9  __aeabi_fdiv                         
000030cd  __aeabi_fmul                         
00002875  __aeabi_fsub                         
00004b59  __aeabi_i2d                          
000046f5  __aeabi_i2f                          
00003db1  __aeabi_idiv                         
00002f07  __aeabi_idiv0                        
00003db1  __aeabi_idivmod                      
0000336b  __aeabi_ldiv0                        
00004f31  __aeabi_llsl                         
00004e4d  __aeabi_lmul                         
000057f1  __aeabi_memclr                       
000057f1  __aeabi_memclr4                      
000057f1  __aeabi_memclr8                      
00005845  __aeabi_memcpy                       
00005845  __aeabi_memcpy4                      
00005845  __aeabi_memcpy8                      
000057b9  __aeabi_memset                       
000057b9  __aeabi_memset4                      
000057b9  __aeabi_memset8                      
00004e29  __aeabi_ui2d                         
00004cf9  __aeabi_ui2f                         
000044cd  __aeabi_uidiv                        
000044cd  __aeabi_uidivmod                     
000056c5  __aeabi_uldivmod                     
00004f31  __ashldi3                            
ffffffff  __binit__                            
0000393d  __cmpdf2                             
000047a9  __cmpsf2                             
0000201d  __divdf3                             
000032e9  __divsf3                             
0000393d  __eqdf2                              
000047a9  __eqsf2                              
0000450d  __extendsfdf2                        
000041c5  __fixdfsi                            
00004859  __fixsfsi                            
00004b59  __floatsidf                          
000046f5  __floatsisf                          
00004e29  __floatunsidf                        
00004cf9  __floatunsisf                        
0000363d  __gedf2                              
00004731  __gesf2                              
0000363d  __gtdf2                              
00004731  __gtsf2                              
0000393d  __ledf2                              
000047a9  __lesf2                              
0000393d  __ltdf2                              
000047a9  __ltsf2                              
UNDEFED   __mpu_init                           
000025dd  __muldf3                             
00004e4d  __muldi3                             
000047e5  __muldsi3                            
000030cd  __mulsf3                             
0000393d  __nedf2                              
000047a9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000139d  __subdf3                             
00002875  __subsf3                             
000036b1  __truncdfsf2                         
00002e65  __udivmoddi4                         
00004d21  _c_int00_noargs                      
20200324  _ftable                              
202004ec  _lock                                
00003a0b  _nop                                 
UNDEFED   _system_post_cinit                   
00005875  _system_pre_init                     
202004f0  _unlock                              
00005879  abort                                
0000420f  adc_getValue                         
2020021c  angle_pid                            
00005e70  asc2_0806                            
00005880  asc2_1608                            
0000454d  atoi                                 
202004f4  bee_time                             
ffffffff  binit                                
202004c8  black                                
20200512  circle_num                           
00003797  convertAnalogToDigital               
00005789  delay_ms                             
000024f5  detect_trace_state_change            
20200504  encoder_A_count                      
20200506  encoder_B_count                      
20200508  encoder_count                        
20200516  encoder_count_flag                   
00004b85  fputc                                
00003ff9  fputs                                
00003b95  frexp                                
00003b95  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
000042ed  gray_init                            
00002f09  gray_task                            
2020031e  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
0000279d  ldexp                                
0000279d  ldexpl                               
00004259  main                                 
00004e71  memccpy                              
00002ae1  motor_direction                      
000049f1  mspm0_delay_ms                       
00004bb1  mspm0_get_clock_ms                   
00002db9  normalizeAnalogValues                
00003a0d  oled_i2c_sda_unlock                  
00004a21  oled_pow                             
00004ed5  pid_set_speed_target                 
00003c4d  printf                               
00002d0d  pwm_set                              
20200517  q1_first_flag                        
2020050a  q1_min_speed                         
2020050c  q1_smooth_duration                   
202004fc  q1_smooth_start_time                 
2020050e  q1_target_speed                      
0000279d  scalbn                               
0000279d  scalbnl                              
20200000  sensor                               
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200500  speed_basic                          
00004bdd  speed_control                        
2020030c  start_time                           
2020031f  task_num                             
20200310  tick_ms                              
20200294  tracing_pid                          
20200314  tracing_val                          
20200518  turn_num                             
20200414  uart_rx_buffer                       
20200320  uart_rx_index                        
20200318  uart_rx_ticks                        
00003eb1  uart_task                            
00005799  wcslen                               
202004d8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  SYSCFG_DL_GPIO_init                  
0000139d  __aeabi_dsub                         
0000139d  __subdf3                             
000013a7  __adddf3                             
000013a7  __aeabi_dadd                         
0000152f  Question_Task_2                      
00001531  No_MCU_Ganv_Sensor_Init              
000016b9  Question_Task_1                      
00001955  Tracing_Value_Get                    
00001a89  PID_speed_realize                    
00001bb1  GROUP1_IRQHandler                    
00001df5  OLED_ShowChar                        
00001f0d  OLED_Init                            
0000201d  __aeabi_ddiv                         
0000201d  __divdf3                             
00002129  DL_Timer_initFourCCPWMMode           
0000222d  OLED_ShowNum                         
00002323  Question_Task_3                      
00002325  DL_Timer_initTimerMode               
0000240d  Oled_Task                            
000024f5  detect_trace_state_change            
000025dd  __aeabi_dmul                         
000025dd  __muldf3                             
000026c1  DL_SYSCTL_configSYSPLL               
0000279d  ldexp                                
0000279d  ldexpl                               
0000279d  scalbn                               
0000279d  scalbnl                              
00002875  __aeabi_fsub                         
00002875  __subsf3                             
0000287f  __addsf3                             
0000287f  __aeabi_fadd                         
0000294d  Get_Analog_value                     
00002a1d  Key_Proc                             
00002ae1  motor_direction                      
00002ba5  TIMG0_IRQHandler                     
00002c5d  SYSCFG_DL_initPower                  
00002d0d  pwm_set                              
00002db9  normalizeAnalogValues                
00002e63  Question_Task_4                      
00002e65  __udivmoddi4                         
00002f07  __aeabi_idiv0                        
00002f09  gray_task                            
00002fa9  OLED_WR_Byte                         
00003041  SYSCFG_DL_PWM_MOTOR_init             
000030cd  __aeabi_fmul                         
000030cd  __mulsf3                             
00003159  PID_init                             
000032e9  __aeabi_fdiv                         
000032e9  __divsf3                             
0000336b  __aeabi_ldiv0                        
0000336d  Servo_SetAngle                       
000033e9  __TI_decompress_lzss                 
00003465  Key_Read                             
000034dd  WIT_Get_Relative_Yaw                 
00003555  Encoder_Get                          
000035c9  Scheduler_Run                        
0000363d  __gedf2                              
0000363d  __gtdf2                              
000036b1  __aeabi_d2f                          
000036b1  __truncdfsf2                         
00003725  No_MCU_Ganv_Sensor_Init_Frist        
00003797  convertAnalogToDigital               
00003803  OLED_Clear                           
0000386d  SYSCFG_DL_PWM_SERVO_init             
000038d5  SYSCFG_DL_UART_WIT_init              
0000393d  __cmpdf2                             
0000393d  __eqdf2                              
0000393d  __ledf2                              
0000393d  __ltdf2                              
0000393d  __nedf2                              
00003a0b  _nop                                 
00003a0d  oled_i2c_sda_unlock                  
00003a71  __aeabi_dcmpeq                       
00003a85  __aeabi_dcmplt                       
00003a99  __aeabi_dcmple                       
00003aad  __aeabi_dcmpge                       
00003ac1  __aeabi_dcmpgt                       
00003ad5  __aeabi_fcmpeq                       
00003ae9  __aeabi_fcmplt                       
00003afd  __aeabi_fcmple                       
00003b11  __aeabi_fcmpge                       
00003b25  __aeabi_fcmpgt                       
00003b37  DL_I2C_fillControllerTXFIFO          
00003b95  frexp                                
00003b95  frexpl                               
00003c4d  printf                               
00003ca9  SYSCFG_DL_I2C_OLED_init              
00003d01  __TI_ltoa                            
00003db1  __aeabi_idiv                         
00003db1  __aeabi_idivmod                      
00003e09  DL_UART_drainRXFIFO                  
00003e5d  SYSCFG_DL_UART_0_init                
00003eb1  uart_task                            
00003ff9  fputs                                
00004049  DL_DMA_initChannel                   
000040e1  SYSCFG_DL_ADC1_init                  
0000412d  Tracing_Control                      
000041c5  __aeabi_d2iz                         
000041c5  __fixdfsi                            
0000420f  adc_getValue                         
00004259  main                                 
000042a5  DL_UART_init                         
000042ed  gray_init                            
0000437d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000043c1  SYSCFG_DL_init                       
00004405  UART0_IRQHandler                     
00004449  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000448d  DL_ADC12_setClockConfig              
000044cd  __aeabi_uidiv                        
000044cd  __aeabi_uidivmod                     
0000450d  __aeabi_f2d                          
0000450d  __extendsfdf2                        
0000454d  atoi                                 
00004641  Get_Anolog_Value                     
0000467d  OLED_Set_Pos                         
000046b9  SYSCFG_DL_SYSCTL_init                
000046f5  __aeabi_i2f                          
000046f5  __floatsisf                          
00004731  __gesf2                              
00004731  __gtsf2                              
0000476d  __TI_auto_init_nobinit_nopinit       
000047a9  __cmpsf2                             
000047a9  __eqsf2                              
000047a9  __lesf2                              
000047a9  __ltsf2                              
000047a9  __nesf2                              
000047e5  __muldsi3                            
0000481f  Get_Normalize_For_User               
00004859  __aeabi_f2iz                         
00004859  __fixsfsi                            
00004891  Beep_Time_Control                    
0000492d  SYSCFG_DL_FOR_CONTROL_init           
000049f1  mspm0_delay_ms                       
00004a21  oled_pow                             
00004b59  __aeabi_i2d                          
00004b59  __floatsidf                          
00004b85  fputc                                
00004bb1  mspm0_get_clock_ms                   
00004bdd  speed_control                        
00004cf9  __aeabi_ui2f                         
00004cf9  __floatunsisf                        
00004d21  _c_int00_noargs                      
00004dbb  DL_I2C_setClockConfig                
00004e29  __aeabi_ui2d                         
00004e29  __floatunsidf                        
00004e4d  __aeabi_lmul                         
00004e4d  __muldi3                             
00004e71  memccpy                              
00004ed5  pid_set_speed_target                 
00004f31  __aeabi_llsl                         
00004f31  __ashldi3                            
000050bd  DL_Timer_setCaptCompUpdateMethod     
000050d9  DL_Timer_setClockConfig              
000053c9  DL_Timer_setCaptureCompareOutCtl     
00005489  SYSCFG_DL_DMA_WIT_init               
000054a1  SysTick_Init                         
00005569  Encoder_Init                         
0000557f  Timer_Init                           
00005595  __TI_zero_init_nomemset              
0000569d  Servo_init                           
000056b1  State_Machine_init                   
000056c5  __aeabi_uldivmod                     
00005711  DL_UART_setClockConfig               
00005723  TI_memcpy_small                      
00005735  __TI_decompress_none                 
00005769  DL_Timer_setCaptureCompareValue      
00005779  SysTick_Handler                      
00005789  delay_ms                             
00005799  wcslen                               
000057a9  Get_Digtal_For_User                  
000057b9  __aeabi_memset                       
000057b9  __aeabi_memset4                      
000057b9  __aeabi_memset8                      
000057d5  TI_memset_small                      
000057e5  Scheduler_Init                       
000057f1  __aeabi_memclr                       
000057f1  __aeabi_memclr4                      
000057f1  __aeabi_memclr8                      
000057fd  DL_Common_delayCycles                
0000582b  Servo_SetCenter                      
00005835  SYSCFG_DL_DMA_init                   
0000583d  __aeabi_errno_addr                   
00005845  __aeabi_memcpy                       
00005845  __aeabi_memcpy4                      
00005845  __aeabi_memcpy8                      
0000585d  ADC0_IRQHandler                      
0000585d  ADC1_IRQHandler                      
0000585d  AES_IRQHandler                       
0000585d  CANFD0_IRQHandler                    
0000585d  DAC0_IRQHandler                      
0000585d  DMA_IRQHandler                       
0000585d  Default_Handler                      
0000585d  GROUP0_IRQHandler                    
0000585d  HardFault_Handler                    
0000585d  I2C0_IRQHandler                      
0000585d  I2C1_IRQHandler                      
0000585d  NMI_Handler                          
0000585d  PendSV_Handler                       
0000585d  RTC_IRQHandler                       
0000585d  SPI0_IRQHandler                      
0000585d  SPI1_IRQHandler                      
0000585d  SVC_Handler                          
0000585d  TIMA0_IRQHandler                     
0000585d  TIMA1_IRQHandler                     
0000585d  TIMG12_IRQHandler                    
0000585d  TIMG6_IRQHandler                     
0000585d  TIMG7_IRQHandler                     
0000585d  TIMG8_IRQHandler                     
0000585d  UART1_IRQHandler                     
0000585d  UART3_IRQHandler                     
00005861  Reset_Handler                        
00005875  _system_pre_init                     
00005878  C$$EXIT                              
00005879  abort                                
00005880  asc2_1608                            
00005e70  asc2_0806                            
000060a0  __aeabi_ctype_table_                 
000060a0  __aeabi_ctype_table_C                
000062fc  __TI_Handler_Table_Base              
00006308  __TI_Handler_Table_Limit             
00006310  __TI_CINIT_Base                      
00006320  __TI_CINIT_Limit                     
00006320  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  tracing_val                          
20200318  uart_rx_ticks                        
2020031c  Key_Up                               
2020031d  Key_Val                              
2020031e  grayscale_count                      
2020031f  task_num                             
20200320  uart_rx_index                        
20200324  _ftable                              
20200414  uart_rx_buffer                       
202004b8  Anolog                               
202004c8  black                                
202004d8  white                                
202004e8  __aeabi_errno                        
202004ec  _lock                                
202004f0  _unlock                              
202004f4  bee_time                             
202004fc  q1_smooth_start_time                 
20200500  speed_basic                          
20200504  encoder_A_count                      
20200506  encoder_B_count                      
20200508  encoder_count                        
2020050a  q1_min_speed                         
2020050c  q1_smooth_duration                   
2020050e  q1_target_speed                      
20200512  circle_num                           
20200516  encoder_count_flag                   
20200517  q1_first_flag                        
20200518  turn_num                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[296 symbols]
