******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 30 18:45:45 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003acd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004f68  0001b098  R  X
  SRAM                  20200000   00008000  00000704  000078fc  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004f68   00004f68    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000044d0   000044d0    r-x .text
  00004590    00004590    00000940   00000940    r-- .rodata
  00004ed0    00004ed0    00000098   00000098    r-- .cinit
20200000    20200000    00000507   00000000    rw-
  20200000    20200000    00000321   00000000    rw- .bss
  20200324    20200324    000001e3   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000044d0     
                  000000c0    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  0000042c    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  000006b0    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000854    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000009e6    00000002     app_question_task.o (.text.Question_Task_2)
                  000009e8    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000b70    00000138     pid.o (.text.Tracing_Value_Get)
                  00000ca8    00000128     pid.o (.text.PID_speed_realize)
                  00000dd0    00000128     Ganv_Grayscale.o (.text.gray_task)
                  00000ef8    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  0000101c    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001134    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00001244    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001350    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001454    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  0000154a    00000002     app_question_task.o (.text.Question_Task_3)
                  0000154c    000000ec     app_tracing_check.o (.text.detect_trace_state_change)
                  00001638    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001720    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  00001808    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000018ec    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000019c8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001aa0    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00001b70    000000c4     key.o (.text.Key_Proc)
                  00001c34    000000c4     app_question_task.o (.text.Question_Task_1)
                  00001cf8    000000c4     motor.o (.text.motor_direction)
                  00001dbc    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001e6c    000000ac     motor.o (.text.pwm_set)
                  00001f18    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00001fc2    00000002     app_question_task.o (.text.Question_Task_4)
                  00001fc4    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  0000205c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  000020e8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002174    00000088     pid.o (.text.PID_init)
                  000021fc    00000088     interrupt.o (.text.TIMG0_IRQHandler)
                  00002284    00000084     clock.o (.text.__NVIC_SetPriority)
                  00002308    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000238c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000240e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002410    0000007c     servo.o (.text.Servo_SetAngle)
                  0000248c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002508    00000078     key.o (.text.Key_Read)
                  00002580    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  000025f8    00000074     encoder.o (.text.Encoder_Get)
                  0000266c    00000074     Scheduler.o (.text.Scheduler_Run)
                  000026e0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002754    0000000c     Scheduler.o (.text.Scheduler_Init)
                  00002760    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000027d4    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002846    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  000028b2    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000291c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  00002984    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  000029ec    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002a54    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00002ab8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002b1a    00000002     libc.a : _lock.c.obj (.text._nop)
                  00002b1c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002b7e    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002bdc    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00002c38    0000005c     libc.a : printf.c.obj (.text.printf)
                  00002c94    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002cec    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00002d40    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002d94    00000054     usart_app.o (.text.uart_task)
                  00002de8    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00002e38    00000050     clock.o (.text.SysTick_Config)
                  00002e88    00000050     usart_app.o (.text.fputs)
                  00002ed8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00002f24    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002f70    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00002fbc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003006    00000002     --HOLE-- [fill = 0]
                  00003008    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003052    0000004a     adc_app.o (.text.adc_getValue)
                  0000309c    0000004a     main.o (.text.main)
                  000030e6    00000002     --HOLE-- [fill = 0]
                  000030e8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003130    00000048     Ganv_Grayscale.o (.text.gray_init)
                  00003178    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  000031c0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003204    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003248    00000044     usart_app.o (.text.UART0_IRQHandler)
                  0000328c    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000032ce    00000002     --HOLE-- [fill = 0]
                  000032d0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003310    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003350    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003390    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000033cc    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003408    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003444    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00003480    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  000034bc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000034f8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003534    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003570    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000035ac    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000035e6    00000002     --HOLE-- [fill = 0]
                  000035e8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003622    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  0000365a    00000002     --HOLE-- [fill = 0]
                  0000365c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00003694    00000034     beep.o (.text.Beep_Time_Control)
                  000036c8    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000036fc    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003730    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00003764    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00003794    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  000037c4    00000030     clock.o (.text.mspm0_delay_ms)
                  000037f4    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00003824    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00003850    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  0000387c    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  000038a8    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  000038d4    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00003900    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  0000392c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003958    0000002c     usart_app.o (.text.fputc)
                  00003984    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  000039b0    0000002c     app_tracing_control.o (.text.speed_control)
                  000039dc    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00003a04    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003a2c    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00003a54    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003a7c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003aa4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003acc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003af4    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00003b1a    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00003b40    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003b66    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003b8c    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00003bb0    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003bd4    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003bf8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003c1a    00000002     --HOLE-- [fill = 0]
                  00003c1c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003c3c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003c5c    00000020     pid.o (.text.pid_set_speed_target)
                  00003c7c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003c9a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003cb8    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00003cd4    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00003cf0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003d0c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003d28    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00003d44    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003d60    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003d7c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003d98    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003db4    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00003dd0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003dec    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003e08    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003e24    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003e40    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003e5c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003e78    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003e90    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003ea8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003ec0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00003ed8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003ef0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003f08    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003f20    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00003f38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003f50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003f68    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00003f80    00000018     beep.o (.text.DL_GPIO_setPins)
                  00003f98    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003fb0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00003fc8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003fe0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003ff8    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00004010    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004028    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004040    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004058    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  00004070    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004088    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  000040a0    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  000040b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000040d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000040e8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004100    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004118    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004130    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004148    00000018     servo.o (.text.DL_Timer_startCounter)
                  00004160    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00004178    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00004190    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000041a8    00000018     usart_app.o (.text.DL_UART_isBusy)
                  000041c0    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  000041d8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000041f0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00004208    00000018     clock.o (.text.SysTick_Init)
                  00004220    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  00004236    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  0000424c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004262    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00004278    00000016     key.o (.text.DL_GPIO_readPins)
                  0000428e    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  000042a4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000042ba    00000016     usart_app.o (.text.DL_UART_transmitData)
                  000042d0    00000016     encoder.o (.text.Encoder_Init)
                  000042e6    00000016     interrupt.o (.text.Timer_Init)
                  000042fc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004312    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  00004326    00000014     beep.o (.text.DL_GPIO_clearPins)
                  0000433a    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000434e    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  00004362    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004376    00000002     --HOLE-- [fill = 0]
                  00004378    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  0000438c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000043a0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000043b4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000043c8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000043dc    00000014     interrupt.o (.text.DL_UART_receiveData)
                  000043f0    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00004404    00000014     servo.o (.text.Servo_init)
                  00004418    00000014     app_question_task.o (.text.State_Machine_init)
                  0000442c    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  0000443e    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  00004450    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004462    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004474    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004486    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  00004496    00000002     --HOLE-- [fill = 0]
                  00004498    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000044a8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000044b8    00000010     interrupt.o (.text.SysTick_Handler)
                  000044c8    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  000044d8    00000010     app_tracing_control.o (.text.servo_tracing)
                  000044e8    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  000044f6    00000002     --HOLE-- [fill = 0]
                  000044f8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004506    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004514    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004522    0000000c     app_tracing_control.o (.text.Tracing_Control)
                  0000452e    00000002     --HOLE-- [fill = 0]
                  00004530    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000453c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004546    0000000a     servo.o (.text.Servo_SetCenter)
                  00004550    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004558    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004560    00000008     libc.a : printf.c.obj (.text._outc)
                  00004568    00000008            : printf.c.obj (.text._outs)
                  00004570    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004574    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004578    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004588    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000458c    00000004            : exit.c.obj (.text:abort)

.cinit     0    00004ed0    00000098     
                  00004ed0    00000070     (.cinit..data.load) [load image, compression = lzss]
                  00004f40    0000000c     (__TI_handler_table)
                  00004f4c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004f54    00000010     (__TI_cinit_table)
                  00004f64    00000004     --HOLE-- [fill = 0]

.rodata    0    00004590    00000940     
                  00004590    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00004b80    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00004da8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004dd0    00000024     Ganv_Grayscale.o (.rodata.str1.8350192368951116151)
                  00004df4    00000021     Ganv_Grayscale.o (.rodata.str1.36112290702919017061)
                  00004e15    00000021     Ganv_Grayscale.o (.rodata.str1.97773892569755152051)
                  00004e36    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004e38    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  00004e50    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  00004e64    00000011     libc.a : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00004e75    00000010     encoder.o (.rodata.encoder_table)
                  00004e85    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00004e94    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004e9e    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  00004ea8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00004eb0    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00004eb8    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  00004ec0    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00004ec3    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00004ec6    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00004ec9    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004ecb    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00004ecd    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000321     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:tracing_val)
                  20200318    00000004     (.common:uart_rx_ticks)
                  2020031c    00000001     (.common:Key_Up)
                  2020031d    00000001     (.common:Key_Val)
                  2020031e    00000001     (.common:grayscale_count)
                  2020031f    00000001     (.common:task_num)
                  20200320    00000001     (.common:uart_rx_index)

.data      0    20200324    000001e3     UNINITIALIZED
                  20200324    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200414    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200494    00000024     Scheduler.o (.data.scheduler_task)
                  202004b8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004c8    00000010     Ganv_Grayscale.o (.data.black)
                  202004d8    00000010     Ganv_Grayscale.o (.data.white)
                  202004e8    00000004     libc.a : _lock.c.obj (.data._lock)
                  202004ec    00000004            : _lock.c.obj (.data._unlock)
                  202004f0    00000004     beep.o (.data.bee_time)
                  202004f4    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004f8    00000002     encoder.o (.data.encoder_A_count)
                  202004fa    00000002     encoder.o (.data.encoder_B_count)
                  202004fc    00000002     encoder.o (.data.encoder_count)
                  202004fe    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  202004ff    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200500    00000001     app_question_task.o (.data.circle_num)
                  20200501    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200502    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200503    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  20200504    00000001     encoder.o (.data.encoder_count_flag)
                  20200505    00000001     app_question_task.o (.data.q1_first_flag)
                  20200506    00000001     app_question_task.o (.data.turn_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_question_task.o            222     0         23     
       app_tracing_check.o            236     0         7      
       app_tracing_control.o          72      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         530     0         30     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1600    102       250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1600    102       250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1400    0         0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1728    0         8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2194    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2194    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          776     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         776     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658     17        0      
       defs.c.obj                     0       0         240    
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       memset16.S.obj                 14      0         0      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1122    17        248    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2526    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       148       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17596   2705      1796   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004f54 records: 2, size/record: 8, table size: 16
	.data: load addr=00004ed0, load size=00000070 bytes, run addr=20200324, run size=000001e3 bytes, compression=lzss
	.bss: load addr=00004f4c, load size=00000008 bytes, run addr=20200000, run size=00000321 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004f40 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003acd     00004578     00004574   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004571  ADC0_IRQHandler                      
00004571  ADC1_IRQHandler                      
00004571  AES_IRQHandler                       
202004b8  Anolog                               
00003695  Beep_Time_Control                    
0000458c  C$$EXIT                              
00004571  CANFD0_IRQHandler                    
00004571  DAC0_IRQHandler                      
000032d1  DL_ADC12_setClockConfig              
0000453d  DL_Common_delayCycles                
00002ed9  DL_DMA_initChannel                   
00002b7f  DL_I2C_fillControllerTXFIFO          
00003b67  DL_I2C_setClockConfig                
000018ed  DL_SYSCTL_configSYSPLL               
000031c1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001351  DL_Timer_initFourCCPWMMode           
00001639  DL_Timer_initTimerMode               
00003e25  DL_Timer_setCaptCompUpdateMethod     
00004131  DL_Timer_setCaptureCompareOutCtl     
000044a9  DL_Timer_setCaptureCompareValue      
00003e41  DL_Timer_setClockConfig              
00002ced  DL_UART_drainRXFIFO                  
000030e9  DL_UART_init                         
00004451  DL_UART_setClockConfig               
00004571  DMA_IRQHandler                       
00004571  Default_Handler                      
202002dd  Digtal                               
000025f9  Encoder_Get                          
000042d1  Encoder_Init                         
00004571  GROUP0_IRQHandler                    
00000ef9  GROUP1_IRQHandler                    
00001aa1  Get_Analog_value                     
00003445  Get_Anolog_Value                     
000044e9  Get_Digtal_For_User                  
00003623  Get_Normalize_For_User               
00004571  HardFault_Handler                    
00004571  I2C0_IRQHandler                      
00004571  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
00001b71  Key_Proc                             
00002509  Key_Read                             
2020031c  Key_Up                               
2020031d  Key_Val                              
00004571  NMI_Handler                          
000009e9  No_MCU_Ganv_Sensor_Init              
000027d5  No_MCU_Ganv_Sensor_Init_Frist        
0000328d  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
000028b3  OLED_Clear                           
00001135  OLED_Init                            
00003481  OLED_Set_Pos                         
0000101d  OLED_ShowChar                        
00001455  OLED_ShowNum                         
00001fc5  OLED_WR_Byte                         
00001721  Oled_Task                            
00002175  PID_init                             
00000ca9  PID_speed_realize                    
00004571  PendSV_Handler                       
00001c35  Question_Task_1                      
000009e7  Question_Task_2                      
0000154b  Question_Task_3                      
00001fc3  Question_Task_4                      
00004571  RTC_IRQHandler                       
00004575  Reset_Handler                        
00004571  SPI0_IRQHandler                      
00004571  SPI1_IRQHandler                      
00004571  SVC_Handler                          
00002f71  SYSCFG_DL_ADC1_init                  
000041f1  SYSCFG_DL_DMA_WIT_init               
00004551  SYSCFG_DL_DMA_init                   
00003731  SYSCFG_DL_FOR_CONTROL_init           
000006b1  SYSCFG_DL_GPIO_init                  
00002c95  SYSCFG_DL_I2C_OLED_init              
0000205d  SYSCFG_DL_PWM_MOTOR_init             
0000291d  SYSCFG_DL_PWM_SERVO_init             
000034bd  SYSCFG_DL_SYSCTL_init                
00002d41  SYSCFG_DL_UART_0_init                
00002985  SYSCFG_DL_UART_WIT_init              
00003205  SYSCFG_DL_init                       
00001dbd  SYSCFG_DL_initPower                  
00002755  Scheduler_Init                       
0000266d  Scheduler_Run                        
00002411  Servo_SetAngle                       
00004547  Servo_SetCenter                      
00004405  Servo_init                           
202002e0  State_Machine                        
00004419  State_Machine_init                   
000044b9  SysTick_Handler                      
00004209  SysTick_Init                         
00004571  TIMA0_IRQHandler                     
00004571  TIMA1_IRQHandler                     
000021fd  TIMG0_IRQHandler                     
00004571  TIMG12_IRQHandler                    
00004571  TIMG6_IRQHandler                     
00004571  TIMG7_IRQHandler                     
00004571  TIMG8_IRQHandler                     
00004463  TI_memcpy_small                      
00004515  TI_memset_small                      
000042e7  Timer_Init                           
00004523  Tracing_Control                      
00000b71  Tracing_Value_Get                    
00003249  UART0_IRQHandler                     
00004571  UART1_IRQHandler                     
000000c1  UART2_IRQHandler                     
00004571  UART3_IRQHandler                     
00002581  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004f54  __TI_CINIT_Base                      
00004f64  __TI_CINIT_Limit                     
00004f64  __TI_CINIT_Warm                      
00004f40  __TI_Handler_Table_Base              
00004f4c  __TI_Handler_Table_Limit             
00003571  __TI_auto_init_nobinit_nopinit       
0000248d  __TI_decompress_lzss                 
00004475  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
0000042d  __TI_printfi_minimal                 
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000042fd  __TI_zero_init_nomemset              
0000085f  __adddf3                             
000019d3  __addsf3                             
00002761  __aeabi_d2f                          
00003009  __aeabi_d2iz                         
0000085f  __aeabi_dadd                         
00002ab9  __aeabi_dcmpeq                       
00002af5  __aeabi_dcmpge                       
00002b09  __aeabi_dcmpgt                       
00002ae1  __aeabi_dcmple                       
00002acd  __aeabi_dcmplt                       
00001245  __aeabi_ddiv                         
00001809  __aeabi_dmul                         
00000855  __aeabi_dsub                         
00003351  __aeabi_f2d                          
0000365d  __aeabi_f2iz                         
000019d3  __aeabi_fadd                         
00002b1d  __aeabi_fcmpeq                       
00002b59  __aeabi_fcmpge                       
00002b6d  __aeabi_fcmpgt                       
00002b45  __aeabi_fcmple                       
00002b31  __aeabi_fcmplt                       
0000238d  __aeabi_fdiv                         
000020e9  __aeabi_fmul                         
000019c9  __aeabi_fsub                         
0000392d  __aeabi_i2d                          
000034f9  __aeabi_i2f                          
0000240f  __aeabi_idiv0                        
00004531  __aeabi_memclr                       
00004531  __aeabi_memclr4                      
00004531  __aeabi_memclr8                      
00004559  __aeabi_memcpy                       
00004559  __aeabi_memcpy4                      
00004559  __aeabi_memcpy8                      
000044f9  __aeabi_memset                       
000044f9  __aeabi_memset4                      
000044f9  __aeabi_memset8                      
00003bd5  __aeabi_ui2d                         
00003311  __aeabi_uidiv                        
00003311  __aeabi_uidivmod                     
ffffffff  __binit__                            
000029ed  __cmpdf2                             
000035ad  __cmpsf2                             
00001245  __divdf3                             
0000238d  __divsf3                             
000029ed  __eqdf2                              
000035ad  __eqsf2                              
00003351  __extendsfdf2                        
00003009  __fixdfsi                            
0000365d  __fixsfsi                            
0000392d  __floatsidf                          
000034f9  __floatsisf                          
00003bd5  __floatunsidf                        
000026e1  __gedf2                              
00003535  __gesf2                              
000026e1  __gtdf2                              
00003535  __gtsf2                              
000029ed  __ledf2                              
000035ad  __lesf2                              
000029ed  __ltdf2                              
000035ad  __ltsf2                              
UNDEFED   __mpu_init                           
00001809  __muldf3                             
000035e9  __muldsi3                            
000020e9  __mulsf3                             
000029ed  __nedf2                              
000035ad  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000855  __subdf3                             
000019c9  __subsf3                             
00002761  __truncdfsf2                         
00003acd  _c_int00_noargs                      
20200324  _ftable                              
202004e8  _lock                                
00002b1b  _nop                                 
UNDEFED   _system_post_cinit                   
00004589  _system_pre_init                     
202004ec  _unlock                              
0000458d  abort                                
00003053  adc_getValue                         
2020021c  angle_pid                            
00004b80  asc2_0806                            
00004590  asc2_1608                            
202004f0  bee_time                             
ffffffff  binit                                
202004c8  black                                
20200500  circle_num                           
00002847  convertAnalogToDigital               
000044c9  delay_ms                             
0000154d  detect_trace_state_change            
202004f8  encoder_A_count                      
202004fa  encoder_B_count                      
202004fc  encoder_count                        
20200504  encoder_count_flag                   
00003959  fputc                                
00002e89  fputs                                
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
00003131  gray_init                            
00000dd1  gray_task                            
2020031e  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
0000309d  main                                 
00003bf9  memccpy                              
00001cf9  motor_direction                      
000037c5  mspm0_delay_ms                       
00003985  mspm0_get_clock_ms                   
00001f19  normalizeAnalogValues                
00002a55  oled_i2c_sda_unlock                  
000037f5  oled_pow                             
00003c5d  pid_set_speed_target                 
00002c39  printf                               
00001e6d  pwm_set                              
20200505  q1_first_flag                        
20200000  sensor                               
000044d9  servo_tracing                        
20200244  speedA_pid                           
2020026c  speedB_pid                           
000039b1  speed_control                        
2020030c  start_time                           
2020031f  task_num                             
20200310  tick_ms                              
20200294  tracing_pid                          
20200314  tracing_val                          
20200506  turn_num                             
20200414  uart_rx_buffer                       
20200320  uart_rx_index                        
20200318  uart_rx_ticks                        
00002d95  uart_task                            
202004d8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  UART2_IRQHandler                     
00000200  __STACK_SIZE                         
0000042d  __TI_printfi_minimal                 
000006b1  SYSCFG_DL_GPIO_init                  
00000855  __aeabi_dsub                         
00000855  __subdf3                             
0000085f  __adddf3                             
0000085f  __aeabi_dadd                         
000009e7  Question_Task_2                      
000009e9  No_MCU_Ganv_Sensor_Init              
00000b71  Tracing_Value_Get                    
00000ca9  PID_speed_realize                    
00000dd1  gray_task                            
00000ef9  GROUP1_IRQHandler                    
0000101d  OLED_ShowChar                        
00001135  OLED_Init                            
00001245  __aeabi_ddiv                         
00001245  __divdf3                             
00001351  DL_Timer_initFourCCPWMMode           
00001455  OLED_ShowNum                         
0000154b  Question_Task_3                      
0000154d  detect_trace_state_change            
00001639  DL_Timer_initTimerMode               
00001721  Oled_Task                            
00001809  __aeabi_dmul                         
00001809  __muldf3                             
000018ed  DL_SYSCTL_configSYSPLL               
000019c9  __aeabi_fsub                         
000019c9  __subsf3                             
000019d3  __addsf3                             
000019d3  __aeabi_fadd                         
00001aa1  Get_Analog_value                     
00001b71  Key_Proc                             
00001c35  Question_Task_1                      
00001cf9  motor_direction                      
00001dbd  SYSCFG_DL_initPower                  
00001e6d  pwm_set                              
00001f19  normalizeAnalogValues                
00001fc3  Question_Task_4                      
00001fc5  OLED_WR_Byte                         
0000205d  SYSCFG_DL_PWM_MOTOR_init             
000020e9  __aeabi_fmul                         
000020e9  __mulsf3                             
00002175  PID_init                             
000021fd  TIMG0_IRQHandler                     
0000238d  __aeabi_fdiv                         
0000238d  __divsf3                             
0000240f  __aeabi_idiv0                        
00002411  Servo_SetAngle                       
0000248d  __TI_decompress_lzss                 
00002509  Key_Read                             
00002581  WIT_Get_Relative_Yaw                 
000025f9  Encoder_Get                          
0000266d  Scheduler_Run                        
000026e1  __gedf2                              
000026e1  __gtdf2                              
00002755  Scheduler_Init                       
00002761  __aeabi_d2f                          
00002761  __truncdfsf2                         
000027d5  No_MCU_Ganv_Sensor_Init_Frist        
00002847  convertAnalogToDigital               
000028b3  OLED_Clear                           
0000291d  SYSCFG_DL_PWM_SERVO_init             
00002985  SYSCFG_DL_UART_WIT_init              
000029ed  __cmpdf2                             
000029ed  __eqdf2                              
000029ed  __ledf2                              
000029ed  __ltdf2                              
000029ed  __nedf2                              
00002a55  oled_i2c_sda_unlock                  
00002ab9  __aeabi_dcmpeq                       
00002acd  __aeabi_dcmplt                       
00002ae1  __aeabi_dcmple                       
00002af5  __aeabi_dcmpge                       
00002b09  __aeabi_dcmpgt                       
00002b1b  _nop                                 
00002b1d  __aeabi_fcmpeq                       
00002b31  __aeabi_fcmplt                       
00002b45  __aeabi_fcmple                       
00002b59  __aeabi_fcmpge                       
00002b6d  __aeabi_fcmpgt                       
00002b7f  DL_I2C_fillControllerTXFIFO          
00002c39  printf                               
00002c95  SYSCFG_DL_I2C_OLED_init              
00002ced  DL_UART_drainRXFIFO                  
00002d41  SYSCFG_DL_UART_0_init                
00002d95  uart_task                            
00002e89  fputs                                
00002ed9  DL_DMA_initChannel                   
00002f71  SYSCFG_DL_ADC1_init                  
00003009  __aeabi_d2iz                         
00003009  __fixdfsi                            
00003053  adc_getValue                         
0000309d  main                                 
000030e9  DL_UART_init                         
00003131  gray_init                            
000031c1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003205  SYSCFG_DL_init                       
00003249  UART0_IRQHandler                     
0000328d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000032d1  DL_ADC12_setClockConfig              
00003311  __aeabi_uidiv                        
00003311  __aeabi_uidivmod                     
00003351  __aeabi_f2d                          
00003351  __extendsfdf2                        
00003445  Get_Anolog_Value                     
00003481  OLED_Set_Pos                         
000034bd  SYSCFG_DL_SYSCTL_init                
000034f9  __aeabi_i2f                          
000034f9  __floatsisf                          
00003535  __gesf2                              
00003535  __gtsf2                              
00003571  __TI_auto_init_nobinit_nopinit       
000035ad  __cmpsf2                             
000035ad  __eqsf2                              
000035ad  __lesf2                              
000035ad  __ltsf2                              
000035ad  __nesf2                              
000035e9  __muldsi3                            
00003623  Get_Normalize_For_User               
0000365d  __aeabi_f2iz                         
0000365d  __fixsfsi                            
00003695  Beep_Time_Control                    
00003731  SYSCFG_DL_FOR_CONTROL_init           
000037c5  mspm0_delay_ms                       
000037f5  oled_pow                             
0000392d  __aeabi_i2d                          
0000392d  __floatsidf                          
00003959  fputc                                
00003985  mspm0_get_clock_ms                   
000039b1  speed_control                        
00003acd  _c_int00_noargs                      
00003b67  DL_I2C_setClockConfig                
00003bd5  __aeabi_ui2d                         
00003bd5  __floatunsidf                        
00003bf9  memccpy                              
00003c5d  pid_set_speed_target                 
00003e25  DL_Timer_setCaptCompUpdateMethod     
00003e41  DL_Timer_setClockConfig              
00004131  DL_Timer_setCaptureCompareOutCtl     
000041f1  SYSCFG_DL_DMA_WIT_init               
00004209  SysTick_Init                         
000042d1  Encoder_Init                         
000042e7  Timer_Init                           
000042fd  __TI_zero_init_nomemset              
00004405  Servo_init                           
00004419  State_Machine_init                   
00004451  DL_UART_setClockConfig               
00004463  TI_memcpy_small                      
00004475  __TI_decompress_none                 
000044a9  DL_Timer_setCaptureCompareValue      
000044b9  SysTick_Handler                      
000044c9  delay_ms                             
000044d9  servo_tracing                        
000044e9  Get_Digtal_For_User                  
000044f9  __aeabi_memset                       
000044f9  __aeabi_memset4                      
000044f9  __aeabi_memset8                      
00004515  TI_memset_small                      
00004523  Tracing_Control                      
00004531  __aeabi_memclr                       
00004531  __aeabi_memclr4                      
00004531  __aeabi_memclr8                      
0000453d  DL_Common_delayCycles                
00004547  Servo_SetCenter                      
00004551  SYSCFG_DL_DMA_init                   
00004559  __aeabi_memcpy                       
00004559  __aeabi_memcpy4                      
00004559  __aeabi_memcpy8                      
00004571  ADC0_IRQHandler                      
00004571  ADC1_IRQHandler                      
00004571  AES_IRQHandler                       
00004571  CANFD0_IRQHandler                    
00004571  DAC0_IRQHandler                      
00004571  DMA_IRQHandler                       
00004571  Default_Handler                      
00004571  GROUP0_IRQHandler                    
00004571  HardFault_Handler                    
00004571  I2C0_IRQHandler                      
00004571  I2C1_IRQHandler                      
00004571  NMI_Handler                          
00004571  PendSV_Handler                       
00004571  RTC_IRQHandler                       
00004571  SPI0_IRQHandler                      
00004571  SPI1_IRQHandler                      
00004571  SVC_Handler                          
00004571  TIMA0_IRQHandler                     
00004571  TIMA1_IRQHandler                     
00004571  TIMG12_IRQHandler                    
00004571  TIMG6_IRQHandler                     
00004571  TIMG7_IRQHandler                     
00004571  TIMG8_IRQHandler                     
00004571  UART1_IRQHandler                     
00004571  UART3_IRQHandler                     
00004575  Reset_Handler                        
00004589  _system_pre_init                     
0000458c  C$$EXIT                              
0000458d  abort                                
00004590  asc2_1608                            
00004b80  asc2_0806                            
00004f40  __TI_Handler_Table_Base              
00004f4c  __TI_Handler_Table_Limit             
00004f54  __TI_CINIT_Base                      
00004f64  __TI_CINIT_Limit                     
00004f64  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  tracing_val                          
20200318  uart_rx_ticks                        
2020031c  Key_Up                               
2020031d  Key_Val                              
2020031e  grayscale_count                      
2020031f  task_num                             
20200320  uart_rx_index                        
20200324  _ftable                              
20200414  uart_rx_buffer                       
202004b8  Anolog                               
202004c8  black                                
202004d8  white                                
202004e8  _lock                                
202004ec  _unlock                              
202004f0  bee_time                             
202004f8  encoder_A_count                      
202004fa  encoder_B_count                      
202004fc  encoder_count                        
20200500  circle_num                           
20200504  encoder_count_flag                   
20200505  q1_first_flag                        
20200506  turn_num                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[268 symbols]
