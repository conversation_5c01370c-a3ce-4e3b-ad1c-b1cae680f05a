******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Sat Aug  2 03:41:35 2025

OUTPUT FILE NAME:   <25_Year_E_V5.0.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004d09


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006308  00019cf8  R  X
  SRAM                  20200000   00008000  0000071a  000078e6  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006308   00006308    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000057a0   000057a0    r-x .text
  00005860    00005860    00000a00   00000a00    r-- .rodata
  00006260    00006260    000000a8   000000a8    r-- .cinit
20200000    20200000    0000051d   00000000    rw-
  20200000    20200000    00000321   00000000    rw- .bss
  20200324    20200324    000001f9   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000057a0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    0000036c     interrupt.o (.text.UART2_IRQHandler)
                  00000dfc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000101c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000011f8    000001a4     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000152e    00000002     app_question_task.o (.text.Question_Task_2)
                  00001530    00000188     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init)
                  000016b8    0000015c     app_question_task.o (.text.Question_Task_1)
                  00001814    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001950    00000130     pid.o (.text.Tracing_Value_Get)
                  00001a80    00000128     pid.o (.text.PID_speed_realize)
                  00001ba8    00000124     encoder.o (.text.GROUP1_IRQHandler)
                  00001ccc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001dec    00000118     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00001f04    00000110     oled_hardware_i2c.o (.text.OLED_Init)
                  00002014    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002120    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002224    000000f6     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  0000231a    00000002     app_question_task.o (.text.Question_Task_3)
                  0000231c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002404    000000e8     oled_hardware_i2c.o (.text.Oled_Task)
                  000024ec    000000e8     app_tracing_check.o (.text.detect_trace_state_change)
                  000025d4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000026b8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002794    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000286c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002944    000000d0     Ganv_Grayscale.o (.text.Get_Analog_value)
                  00002a14    000000c4     key.o (.text.Key_Proc)
                  00002ad8    000000c4     motor.o (.text.motor_direction)
                  00002b9c    000000b8     interrupt.o (.text.TIMG0_IRQHandler)
                  00002c54    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002d04    000000ac     motor.o (.text.pwm_set)
                  00002db0    000000aa     Ganv_Grayscale.o (.text.normalizeAnalogValues)
                  00002e5a    00000002     app_question_task.o (.text.Question_Task_4)
                  00002e5c    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002efe    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002f00    000000a0     Ganv_Grayscale.o (.text.gray_task)
                  00002fa0    00000098     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00003038    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  000030c4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003150    00000088     pid.o (.text.PID_init)
                  000031d8    00000084     clock.o (.text.__NVIC_SetPriority)
                  0000325c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000032e0    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003362    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003364    0000007c     servo.o (.text.Servo_SetAngle)
                  000033e0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000345c    00000078     key.o (.text.Key_Read)
                  000034d4    00000078     wit.o (.text.WIT_Get_Relative_Yaw)
                  0000354c    00000074     encoder.o (.text.Encoder_Get)
                  000035c0    00000074     Scheduler.o (.text.Scheduler_Run)
                  00003634    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000036a8    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000036b0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003724    00000072     Ganv_Grayscale.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003796    0000006c     Ganv_Grayscale.o (.text.convertAnalogToDigital)
                  00003802    0000006a     oled_hardware_i2c.o (.text.OLED_Clear)
                  0000386c    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_SERVO_init)
                  000038d4    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_WIT_init)
                  0000393c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000039a4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a0a    00000002            : _lock.c.obj (.text._nop)
                  00003a0c    00000064     oled_hardware_i2c.o (.text.oled_i2c_sda_unlock)
                  00003a70    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003ad2    00000002     --HOLE-- [fill = 0]
                  00003ad4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003b36    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b94    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003bf0    0000005c     oled_hardware_i2c.o (.text.mspm0_i2c_enable)
                  00003c4c    0000005c     libc.a : printf.c.obj (.text.printf)
                  00003ca8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003d00    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003d58    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003db0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003e06    00000002     --HOLE-- [fill = 0]
                  00003e08    00000054     driverlib.a : dl_uart.o (.text.DL_UART_drainRXFIFO)
                  00003e5c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003eb0    00000054     usart_app.o (.text.uart_task)
                  00003f04    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003f56    00000002     --HOLE-- [fill = 0]
                  00003f58    00000050     oled_hardware_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003fa8    00000050     clock.o (.text.SysTick_Config)
                  00003ff8    00000050     usart_app.o (.text.fputs)
                  00004048    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004094    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000040e0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  0000412c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004176    00000002     --HOLE-- [fill = 0]
                  00004178    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000041c2    0000004a     adc_app.o (.text.adc_getValue)
                  0000420c    0000004a     main.o (.text.main)
                  00004256    00000002     --HOLE-- [fill = 0]
                  00004258    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000042a0    00000048     Ganv_Grayscale.o (.text.gray_init)
                  000042e8    00000048     oled_hardware_i2c.o (.text.mspm0_i2c_disable)
                  00004330    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004374    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000043b8    00000044     usart_app.o (.text.UART0_IRQHandler)
                  000043fc    00000042     Ganv_Grayscale.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000443e    00000002     --HOLE-- [fill = 0]
                  00004440    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004480    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000044c0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004500    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004540    0000003c     oled_hardware_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000457c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000045b8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000045f4    0000003c     Ganv_Grayscale.o (.text.Get_Anolog_Value)
                  00004630    0000003c     oled_hardware_i2c.o (.text.OLED_Set_Pos)
                  0000466c    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000046a8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000046e4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004720    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000475c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004796    00000002     --HOLE-- [fill = 0]
                  00004798    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000047d2    00000038     Ganv_Grayscale.o (.text.Get_Normalize_For_User)
                  0000480a    00000002     --HOLE-- [fill = 0]
                  0000480c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004844    00000034     beep.o (.text.Beep_Time_Control)
                  00004878    00000034     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000048ac    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000048e0    00000034     app_question_task.o (.text.Q1_Tracing_Control_With_Speed)
                  00004914    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_FOR_CONTROL_init)
                  00004948    00000030     adc_app.o (.text.DL_ADC12_getMemResult)
                  00004978    00000030     interrupt.o (.text.DL_DMA_setTransferSize)
                  000049a8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000049d8    00000030     clock.o (.text.mspm0_delay_ms)
                  00004a08    00000030     oled_hardware_i2c.o (.text.oled_pow)
                  00004a38    0000002c     encoder.o (.text.__NVIC_ClearPendingIRQ)
                  00004a64    0000002c     interrupt.o (.text.__NVIC_ClearPendingIRQ)
                  00004a90    0000002c     main.o (.text.__NVIC_ClearPendingIRQ)
                  00004abc    0000002c     encoder.o (.text.__NVIC_EnableIRQ)
                  00004ae8    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  00004b14    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00004b40    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004b6c    0000002c     usart_app.o (.text.fputc)
                  00004b98    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004bc4    0000002c     app_tracing_control.o (.text.speed_control)
                  00004bf0    00000028     oled_hardware_i2c.o (.text.DL_Common_updateReg)
                  00004c18    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004c40    00000028     interrupt.o (.text.DL_DMA_setDestAddr)
                  00004c68    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004c90    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004cb8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004ce0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004d08    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004d30    00000026     interrupt.o (.text.DL_DMA_disableChannel)
                  00004d56    00000026     interrupt.o (.text.DL_DMA_enableChannel)
                  00004d7c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00004da2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00004dc8    00000024     interrupt.o (.text.DL_DMA_getTransferSize)
                  00004dec    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00004e10    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004e34    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004e58    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004e7a    00000002     --HOLE-- [fill = 0]
                  00004e7c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00004e9c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004ebc    00000020     pid.o (.text.pid_set_speed_target)
                  00004edc    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00004efa    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00004f18    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004f36    00000002     --HOLE-- [fill = 0]
                  00004f38    0000001c     adc_app.o (.text.DL_ADC12_startConversion)
                  00004f54    0000001c     adc_app.o (.text.DL_ADC12_stopConversion)
                  00004f70    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00004f8c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004fa8    0000001c     oled_hardware_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004fc4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004fe0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004ffc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005018    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005034    0000001c     oled_hardware_i2c.o (.text.DL_I2C_getSDAStatus)
                  00005050    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000506c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005088    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000050a4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000050c0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000050dc    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000050f8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005110    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005128    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005140    00000018     oled_hardware_i2c.o (.text.DL_GPIO_enableOutput)
                  00005158    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005170    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005188    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000051a0    00000018     oled_hardware_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  000051b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000051d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000051e8    00000018     Ganv_Grayscale.o (.text.DL_GPIO_setPins)
                  00005200    00000018     beep.o (.text.DL_GPIO_setPins)
                  00005218    00000018     motor.o (.text.DL_GPIO_setPins)
                  00005230    00000018     oled_hardware_i2c.o (.text.DL_GPIO_setPins)
                  00005248    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005260    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005278    00000018     oled_hardware_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00005290    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000052a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000052c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000052d8    00000018     oled_hardware_i2c.o (.text.DL_I2C_enablePower)
                  000052f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005308    00000018     oled_hardware_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00005320    00000018     oled_hardware_i2c.o (.text.DL_I2C_reset)
                  00005338    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005350    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005368    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005380    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005398    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000053b0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000053c8    00000018     servo.o (.text.DL_Timer_startCounter)
                  000053e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000053f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005410    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005428    00000018     usart_app.o (.text.DL_UART_isBusy)
                  00005440    00000018     interrupt.o (.text.DL_UART_isRXFIFOEmpty)
                  00005458    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005470    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_WIT_init)
                  00005488    00000018     clock.o (.text.SysTick_Init)
                  000054a0    00000016     adc_app.o (.text.DL_ADC12_disableConversions)
                  000054b6    00000016     adc_app.o (.text.DL_ADC12_enableConversions)
                  000054cc    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000054e2    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000054f8    00000016     key.o (.text.DL_GPIO_readPins)
                  0000550e    00000016     oled_hardware_i2c.o (.text.DL_GPIO_readPins)
                  00005524    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000553a    00000016     usart_app.o (.text.DL_UART_transmitData)
                  00005550    00000016     encoder.o (.text.Encoder_Init)
                  00005566    00000016     interrupt.o (.text.Timer_Init)
                  0000557c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005592    00000014     Ganv_Grayscale.o (.text.DL_GPIO_clearPins)
                  000055a6    00000014     beep.o (.text.DL_GPIO_clearPins)
                  000055ba    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000055ce    00000014     oled_hardware_i2c.o (.text.DL_GPIO_clearPins)
                  000055e2    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000055f6    00000002     --HOLE-- [fill = 0]
                  000055f8    00000014     oled_hardware_i2c.o (.text.DL_I2C_getControllerStatus)
                  0000560c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005620    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005634    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005648    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000565c    00000014     interrupt.o (.text.DL_UART_receiveData)
                  00005670    00000014     usart_app.o (.text.DL_UART_receiveData)
                  00005684    00000014     servo.o (.text.Servo_init)
                  00005698    00000014     app_question_task.o (.text.State_Machine_init)
                  000056ac    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000056c0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000056d4    00000012     interrupt.o (.text.DL_Timer_getPendingInterrupt)
                  000056e6    00000012     usart_app.o (.text.DL_UART_getPendingInterrupt)
                  000056f8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000570a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000571c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000572e    00000010     adc_app.o (.text.DL_ADC12_getStatus)
                  0000573e    00000002     --HOLE-- [fill = 0]
                  00005740    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005750    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005760    00000010     interrupt.o (.text.SysTick_Handler)
                  00005770    00000010     oled_hardware_i2c.o (.text.delay_ms)
                  00005780    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005790    0000000e     Ganv_Grayscale.o (.text.Get_Digtal_For_User)
                  0000579e    00000002     --HOLE-- [fill = 0]
                  000057a0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000057ae    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000057bc    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000057ca    00000002     --HOLE-- [fill = 0]
                  000057cc    0000000c     Scheduler.o (.text.Scheduler_Init)
                  000057d8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000057e4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000057ee    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000057f8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005808    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005812    0000000a     servo.o (.text.Servo_SetCenter)
                  0000581c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005824    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000582c    00000008     libc.a : printf.c.obj (.text._outc)
                  00005834    00000008            : printf.c.obj (.text._outs)
                  0000583c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005840    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005844    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005854    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005858    00000004            : exit.c.obj (.text:abort)
                  0000585c    00000004     --HOLE-- [fill = 0]

.cinit     0    00006260    000000a8     
                  00006260    0000007e     (.cinit..data.load) [load image, compression = lzss]
                  000062de    00000002     --HOLE-- [fill = 0]
                  000062e0    0000000c     (__TI_handler_table)
                  000062ec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000062f4    00000010     (__TI_cinit_table)
                  00006304    00000004     --HOLE-- [fill = 0]

.rodata    0    00005860    00000a00     
                  00005860    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00005e50    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00006078    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006080    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006181    00000003     ti_msp_dl_config.o (.rodata.gFOR_CONTROLClockConfig)
                  00006184    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000061ac    00000018     ti_msp_dl_config.o (.rodata.gDMA_WITConfig)
                  000061c4    00000014     ti_msp_dl_config.o (.rodata.gFOR_CONTROLTimerConfig)
                  000061d8    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000061e9    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000061fa    00000010     encoder.o (.rodata.encoder_table)
                  0000620a    0000000f     usart_app.o (.rodata.str1.49640300125064107821)
                  00006219    0000000c     interrupt.o (.rodata.str1.146019215406595515531)
                  00006225    00000001     --HOLE-- [fill = 0]
                  00006226    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006230    0000000a     ti_msp_dl_config.o (.rodata.gUART_WITConfig)
                  0000623a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000623c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00006244    00000008     ti_msp_dl_config.o (.rodata.gPWM_SERVOConfig)
                  0000624c    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  0000624f    00000003     ti_msp_dl_config.o (.rodata.gPWM_SERVOClockConfig)
                  00006252    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00006254    00000002     ti_msp_dl_config.o (.rodata.gUART_WITClockConfig)
                  00006256    0000000a     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000321     UNINITIALIZED
                  20200000    000000b0     (.common:sensor)
                  202000b0    000000a0     (.common:gPWM_MOTORBackup)
                  20200150    000000a0     (.common:gPWM_SERVOBackup)
                  202001f0    0000002c     (.common:wit_data)
                  2020021c    00000028     (.common:angle_pid)
                  20200244    00000028     (.common:speedA_pid)
                  2020026c    00000028     (.common:speedB_pid)
                  20200294    00000028     (.common:tracing_pid)
                  202002bc    00000021     (.common:wit_dmaBuffer)
                  202002dd    00000001     (.common:Digtal)
                  202002de    00000001     (.common:Key_Down)
                  202002df    00000001     (.common:Key_Old)
                  202002e0    00000014     (.common:State_Machine)
                  202002f4    00000010     (.common:Normal)
                  20200304    00000008     (.common:grayscale_data)
                  2020030c    00000004     (.common:start_time)
                  20200310    00000004     (.common:tick_ms)
                  20200314    00000004     (.common:tracing_val)
                  20200318    00000004     (.common:uart_rx_ticks)
                  2020031c    00000001     (.common:Key_Up)
                  2020031d    00000001     (.common:Key_Val)
                  2020031e    00000001     (.common:grayscale_count)
                  2020031f    00000001     (.common:task_num)
                  20200320    00000001     (.common:uart_rx_index)

.data      0    20200324    000001f9     UNINITIALIZED
                  20200324    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200414    00000080     usart_app.o (.data.uart_rx_buffer)
                  20200494    00000024     Scheduler.o (.data.scheduler_task)
                  202004b8    00000010     Ganv_Grayscale.o (.data.Anolog)
                  202004c8    00000010     Ganv_Grayscale.o (.data.black)
                  202004d8    00000010     Ganv_Grayscale.o (.data.white)
                  202004e8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004ec    00000004            : _lock.c.obj (.data._lock)
                  202004f0    00000004            : _lock.c.obj (.data._unlock)
                  202004f4    00000004     beep.o (.data.bee_time)
                  202004f8    00000004     app_tracing_check.o (.data.detect_trace_state_change.state_change_start_time)
                  202004fc    00000004     app_question_task.o (.data.q1_current_base_speed)
                  20200500    00000004     app_question_task.o (.data.q1_min_speed)
                  20200504    00000004     app_question_task.o (.data.q1_smooth_start_time)
                  20200508    00000004     app_question_task.o (.data.q1_target_speed)
                  2020050c    00000002     encoder.o (.data.encoder_A_count)
                  2020050e    00000002     encoder.o (.data.encoder_B_count)
                  20200510    00000002     encoder.o (.data.encoder_count)
                  20200512    00000002     app_question_task.o (.data.q1_smooth_duration)
                  20200514    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_A)
                  20200515    00000001     encoder.o (.data.GROUP1_IRQHandler.last_state_B)
                  20200516    00000001     app_question_task.o (.data.circle_num)
                  20200517    00000001     app_tracing_check.o (.data.detect_trace_state_change.current_state)
                  20200518    00000001     app_tracing_check.o (.data.detect_trace_state_change.last_detected_state)
                  20200519    00000001     app_tracing_check.o (.data.detect_trace_state_change.state_confirmed)
                  2020051a    00000001     encoder.o (.data.encoder_count_flag)
                  2020051b    00000001     app_question_task.o (.data.q1_first_flag)
                  2020051c    00000001     app_question_task.o (.data.turn_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3156    143       320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         162     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3326    335       320    
                                                               
    .\APP\
       app_question_task.o            426     0         41     
       app_tracing_check.o            232     0         7      
       app_tracing_control.o          44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         702     0         48     
                                                               
    .\Hardware\ADC\
       adc_app.o                      238     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         238     0         0      
                                                               
    .\Hardware\BEEP\
       beep.o                         96      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         4      
                                                               
    .\Hardware\Encoder\
       encoder.o                      592     16        9      
    +--+------------------------------+-------+---------+---------+
       Total:                         592     16        9      
                                                               
    .\Hardware\Grayscale\
       Ganv_Grayscale.o               1464    0         250    
    +--+------------------------------+-------+---------+---------+
       Total:                         1464    0         250    
                                                               
    .\Hardware\Key\
       key.o                          338     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         338     0         4      
                                                               
    .\Hardware\MSPM0\
       interrupt.o                    1448    12        0      
       clock.o                        328     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         1776    12        8      
                                                               
    .\Hardware\Motor\
       motor.o                        412     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         412     0         0      
                                                               
    .\Hardware\OLED\
       oled_hardware_i2c.o            2194    2072      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2194    2072      0      
                                                               
    .\Hardware\PID\
       pid.o                          768     0         164    
    +--+------------------------------+-------+---------+---------+
       Total:                         768     0         164    
                                                               
    .\Hardware\Scheduler\
       Scheduler.o                    128     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         128     0         37     
                                                               
    .\Hardware\Servo\
       servo.o                        178     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         178     0         0      
                                                               
    .\Hardware\Uart\
       usart_app.o                    360     15        133    
    +--+------------------------------+-------+---------+---------+
       Total:                         360     15        133    
                                                               
    .\Hardware\WIT\
       wit.o                          120     0         77     
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         77     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      174     0         0      
       dl_i2c.o                       132     0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1332    0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       defs.c.obj                     0       0         240    
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       printf.c.obj                   108     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5458    291       252    
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2918    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       162       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   22400   2903      1818   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000062f4 records: 2, size/record: 8, table size: 16
	.data: load addr=00006260, load size=0000007e bytes, run addr=20200324, run size=000001f9 bytes, compression=lzss
	.bss: load addr=000062ec, load size=00000008 bytes, run addr=20200000, run size=00000321 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000062e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000139d     000057f8     000057f6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00004d09     00005844     00005840   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000583d  ADC0_IRQHandler                      
0000583d  ADC1_IRQHandler                      
0000583d  AES_IRQHandler                       
202004b8  Anolog                               
00004845  Beep_Time_Control                    
00005858  C$$EXIT                              
0000583d  CANFD0_IRQHandler                    
0000583d  DAC0_IRQHandler                      
00004441  DL_ADC12_setClockConfig              
000057e5  DL_Common_delayCycles                
00004049  DL_DMA_initChannel                   
00003b37  DL_I2C_fillControllerTXFIFO          
00004da3  DL_I2C_setClockConfig                
000026b9  DL_SYSCTL_configSYSPLL               
00004331  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002121  DL_Timer_initFourCCPWMMode           
0000231d  DL_Timer_initTimerMode               
000050a5  DL_Timer_setCaptCompUpdateMethod     
000053b1  DL_Timer_setCaptureCompareOutCtl     
00005751  DL_Timer_setCaptureCompareValue      
000050c1  DL_Timer_setClockConfig              
00003e09  DL_UART_drainRXFIFO                  
00004259  DL_UART_init                         
000056f9  DL_UART_setClockConfig               
0000583d  DMA_IRQHandler                       
0000583d  Default_Handler                      
202002dd  Digtal                               
0000354d  Encoder_Get                          
00005551  Encoder_Init                         
0000583d  GROUP0_IRQHandler                    
00001ba9  GROUP1_IRQHandler                    
00002945  Get_Analog_value                     
000045f5  Get_Anolog_Value                     
00005791  Get_Digtal_For_User                  
000047d3  Get_Normalize_For_User               
0000583d  HardFault_Handler                    
0000583d  I2C0_IRQHandler                      
0000583d  I2C1_IRQHandler                      
202002de  Key_Down                             
202002df  Key_Old                              
00002a15  Key_Proc                             
0000345d  Key_Read                             
2020031c  Key_Up                               
2020031d  Key_Val                              
0000583d  NMI_Handler                          
00001531  No_MCU_Ganv_Sensor_Init              
00003725  No_MCU_Ganv_Sensor_Init_Frist        
000043fd  No_Mcu_Ganv_Sensor_Task_Without_tick 
202002f4  Normal                               
00003803  OLED_Clear                           
00001f05  OLED_Init                            
00004631  OLED_Set_Pos                         
00001ded  OLED_ShowChar                        
00002225  OLED_ShowNum                         
00002fa1  OLED_WR_Byte                         
00002405  Oled_Task                            
00003151  PID_init                             
00001a81  PID_speed_realize                    
0000583d  PendSV_Handler                       
000048e1  Q1_Tracing_Control_With_Speed        
000016b9  Question_Task_1                      
0000152f  Question_Task_2                      
0000231b  Question_Task_3                      
00002e5b  Question_Task_4                      
0000583d  RTC_IRQHandler                       
00005841  Reset_Handler                        
0000583d  SPI0_IRQHandler                      
0000583d  SPI1_IRQHandler                      
0000583d  SVC_Handler                          
000040e1  SYSCFG_DL_ADC1_init                  
00005471  SYSCFG_DL_DMA_WIT_init               
000036a9  SYSCFG_DL_DMA_init                   
00004915  SYSCFG_DL_FOR_CONTROL_init           
000011f9  SYSCFG_DL_GPIO_init                  
00003ca9  SYSCFG_DL_I2C_OLED_init              
00003039  SYSCFG_DL_PWM_MOTOR_init             
0000386d  SYSCFG_DL_PWM_SERVO_init             
0000466d  SYSCFG_DL_SYSCTL_init                
00003e5d  SYSCFG_DL_UART_0_init                
000038d5  SYSCFG_DL_UART_WIT_init              
00004375  SYSCFG_DL_init                       
00002c55  SYSCFG_DL_initPower                  
000057cd  Scheduler_Init                       
000035c1  Scheduler_Run                        
00003365  Servo_SetAngle                       
00005813  Servo_SetCenter                      
00005685  Servo_init                           
202002e0  State_Machine                        
00005699  State_Machine_init                   
00005761  SysTick_Handler                      
00005489  SysTick_Init                         
0000583d  TIMA0_IRQHandler                     
0000583d  TIMA1_IRQHandler                     
00002b9d  TIMG0_IRQHandler                     
0000583d  TIMG12_IRQHandler                    
0000583d  TIMG6_IRQHandler                     
0000583d  TIMG7_IRQHandler                     
0000583d  TIMG8_IRQHandler                     
0000570b  TI_memcpy_small                      
000057bd  TI_memset_small                      
00005567  Timer_Init                           
00001951  Tracing_Value_Get                    
000043b9  UART0_IRQHandler                     
0000583d  UART1_IRQHandler                     
00000a91  UART2_IRQHandler                     
0000583d  UART3_IRQHandler                     
000034d5  WIT_Get_Relative_Yaw                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000062f4  __TI_CINIT_Base                      
00006304  __TI_CINIT_Limit                     
00006304  __TI_CINIT_Warm                      
000062e0  __TI_Handler_Table_Base              
000062ec  __TI_Handler_Table_Limit             
00004721  __TI_auto_init_nobinit_nopinit       
000033e1  __TI_decompress_lzss                 
0000571d  __TI_decompress_none                 
00003d01  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000557d  __TI_zero_init_nomemset              
000013a7  __adddf3                             
00002877  __addsf3                             
00006080  __aeabi_ctype_table_                 
00006080  __aeabi_ctype_table_C                
000036b1  __aeabi_d2f                          
00004179  __aeabi_d2iz                         
000013a7  __aeabi_dadd                         
00003a71  __aeabi_dcmpeq                       
00003aad  __aeabi_dcmpge                       
00003ac1  __aeabi_dcmpgt                       
00003a99  __aeabi_dcmple                       
00003a85  __aeabi_dcmplt                       
00002015  __aeabi_ddiv                         
000025d5  __aeabi_dmul                         
0000139d  __aeabi_dsub                         
202004e8  __aeabi_errno                        
0000581d  __aeabi_errno_addr                   
000044c1  __aeabi_f2d                          
0000480d  __aeabi_f2iz                         
00002877  __aeabi_fadd                         
00003ad5  __aeabi_fcmpeq                       
00003b11  __aeabi_fcmpge                       
00003b25  __aeabi_fcmpgt                       
00003afd  __aeabi_fcmple                       
00003ae9  __aeabi_fcmplt                       
000032e1  __aeabi_fdiv                         
000030c5  __aeabi_fmul                         
0000286d  __aeabi_fsub                         
00004b41  __aeabi_i2d                          
000046a9  __aeabi_i2f                          
00003db1  __aeabi_idiv                         
00002eff  __aeabi_idiv0                        
00003db1  __aeabi_idivmod                      
00003363  __aeabi_ldiv0                        
00004f19  __aeabi_llsl                         
00004e35  __aeabi_lmul                         
000057d9  __aeabi_memclr                       
000057d9  __aeabi_memclr4                      
000057d9  __aeabi_memclr8                      
00005825  __aeabi_memcpy                       
00005825  __aeabi_memcpy4                      
00005825  __aeabi_memcpy8                      
000057a1  __aeabi_memset                       
000057a1  __aeabi_memset4                      
000057a1  __aeabi_memset8                      
00004e11  __aeabi_ui2d                         
00004ce1  __aeabi_ui2f                         
00004481  __aeabi_uidiv                        
00004481  __aeabi_uidivmod                     
000056ad  __aeabi_uldivmod                     
00004f19  __ashldi3                            
ffffffff  __binit__                            
0000393d  __cmpdf2                             
0000475d  __cmpsf2                             
00002015  __divdf3                             
000032e1  __divsf3                             
0000393d  __eqdf2                              
0000475d  __eqsf2                              
000044c1  __extendsfdf2                        
00004179  __fixdfsi                            
0000480d  __fixsfsi                            
00004b41  __floatsidf                          
000046a9  __floatsisf                          
00004e11  __floatunsidf                        
00004ce1  __floatunsisf                        
00003635  __gedf2                              
000046e5  __gesf2                              
00003635  __gtdf2                              
000046e5  __gtsf2                              
0000393d  __ledf2                              
0000475d  __lesf2                              
0000393d  __ltdf2                              
0000475d  __ltsf2                              
UNDEFED   __mpu_init                           
000025d5  __muldf3                             
00004e35  __muldi3                             
00004799  __muldsi3                            
000030c5  __mulsf3                             
0000393d  __nedf2                              
0000475d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000139d  __subdf3                             
0000286d  __subsf3                             
000036b1  __truncdfsf2                         
00002e5d  __udivmoddi4                         
00004d09  _c_int00_noargs                      
20200324  _ftable                              
202004ec  _lock                                
00003a0b  _nop                                 
UNDEFED   _system_post_cinit                   
00005855  _system_pre_init                     
202004f0  _unlock                              
00005859  abort                                
000041c3  adc_getValue                         
2020021c  angle_pid                            
00005e50  asc2_0806                            
00005860  asc2_1608                            
00004501  atoi                                 
202004f4  bee_time                             
ffffffff  binit                                
202004c8  black                                
20200516  circle_num                           
00003797  convertAnalogToDigital               
00005771  delay_ms                             
000024ed  detect_trace_state_change            
2020050c  encoder_A_count                      
2020050e  encoder_B_count                      
20200510  encoder_count                        
2020051a  encoder_count_flag                   
00004b6d  fputc                                
00003ff9  fputs                                
00003b95  frexp                                
00003b95  frexpl                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
000042a1  gray_init                            
00002f01  gray_task                            
2020031e  grayscale_count                      
20200304  grayscale_data                       
00000000  interruptVectors                     
00002795  ldexp                                
00002795  ldexpl                               
0000420d  main                                 
00004e59  memccpy                              
00002ad9  motor_direction                      
000049d9  mspm0_delay_ms                       
00004b99  mspm0_get_clock_ms                   
00002db1  normalizeAnalogValues                
00003a0d  oled_i2c_sda_unlock                  
00004a09  oled_pow                             
00004ebd  pid_set_speed_target                 
00003c4d  printf                               
00002d05  pwm_set                              
202004fc  q1_current_base_speed                
2020051b  q1_first_flag                        
20200500  q1_min_speed                         
20200512  q1_smooth_duration                   
20200504  q1_smooth_start_time                 
20200508  q1_target_speed                      
00002795  scalbn                               
00002795  scalbnl                              
20200000  sensor                               
20200244  speedA_pid                           
2020026c  speedB_pid                           
00004bc5  speed_control                        
2020030c  start_time                           
2020031f  task_num                             
20200310  tick_ms                              
20200294  tracing_pid                          
20200314  tracing_val                          
2020051c  turn_num                             
20200414  uart_rx_buffer                       
20200320  uart_rx_index                        
20200318  uart_rx_ticks                        
00003eb1  uart_task                            
00005781  wcslen                               
202004d8  white                                
202001f0  wit_data                             
202002bc  wit_dmaBuffer                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  UART2_IRQHandler                     
000011f9  SYSCFG_DL_GPIO_init                  
0000139d  __aeabi_dsub                         
0000139d  __subdf3                             
000013a7  __adddf3                             
000013a7  __aeabi_dadd                         
0000152f  Question_Task_2                      
00001531  No_MCU_Ganv_Sensor_Init              
000016b9  Question_Task_1                      
00001951  Tracing_Value_Get                    
00001a81  PID_speed_realize                    
00001ba9  GROUP1_IRQHandler                    
00001ded  OLED_ShowChar                        
00001f05  OLED_Init                            
00002015  __aeabi_ddiv                         
00002015  __divdf3                             
00002121  DL_Timer_initFourCCPWMMode           
00002225  OLED_ShowNum                         
0000231b  Question_Task_3                      
0000231d  DL_Timer_initTimerMode               
00002405  Oled_Task                            
000024ed  detect_trace_state_change            
000025d5  __aeabi_dmul                         
000025d5  __muldf3                             
000026b9  DL_SYSCTL_configSYSPLL               
00002795  ldexp                                
00002795  ldexpl                               
00002795  scalbn                               
00002795  scalbnl                              
0000286d  __aeabi_fsub                         
0000286d  __subsf3                             
00002877  __addsf3                             
00002877  __aeabi_fadd                         
00002945  Get_Analog_value                     
00002a15  Key_Proc                             
00002ad9  motor_direction                      
00002b9d  TIMG0_IRQHandler                     
00002c55  SYSCFG_DL_initPower                  
00002d05  pwm_set                              
00002db1  normalizeAnalogValues                
00002e5b  Question_Task_4                      
00002e5d  __udivmoddi4                         
00002eff  __aeabi_idiv0                        
00002f01  gray_task                            
00002fa1  OLED_WR_Byte                         
00003039  SYSCFG_DL_PWM_MOTOR_init             
000030c5  __aeabi_fmul                         
000030c5  __mulsf3                             
00003151  PID_init                             
000032e1  __aeabi_fdiv                         
000032e1  __divsf3                             
00003363  __aeabi_ldiv0                        
00003365  Servo_SetAngle                       
000033e1  __TI_decompress_lzss                 
0000345d  Key_Read                             
000034d5  WIT_Get_Relative_Yaw                 
0000354d  Encoder_Get                          
000035c1  Scheduler_Run                        
00003635  __gedf2                              
00003635  __gtdf2                              
000036a9  SYSCFG_DL_DMA_init                   
000036b1  __aeabi_d2f                          
000036b1  __truncdfsf2                         
00003725  No_MCU_Ganv_Sensor_Init_Frist        
00003797  convertAnalogToDigital               
00003803  OLED_Clear                           
0000386d  SYSCFG_DL_PWM_SERVO_init             
000038d5  SYSCFG_DL_UART_WIT_init              
0000393d  __cmpdf2                             
0000393d  __eqdf2                              
0000393d  __ledf2                              
0000393d  __ltdf2                              
0000393d  __nedf2                              
00003a0b  _nop                                 
00003a0d  oled_i2c_sda_unlock                  
00003a71  __aeabi_dcmpeq                       
00003a85  __aeabi_dcmplt                       
00003a99  __aeabi_dcmple                       
00003aad  __aeabi_dcmpge                       
00003ac1  __aeabi_dcmpgt                       
00003ad5  __aeabi_fcmpeq                       
00003ae9  __aeabi_fcmplt                       
00003afd  __aeabi_fcmple                       
00003b11  __aeabi_fcmpge                       
00003b25  __aeabi_fcmpgt                       
00003b37  DL_I2C_fillControllerTXFIFO          
00003b95  frexp                                
00003b95  frexpl                               
00003c4d  printf                               
00003ca9  SYSCFG_DL_I2C_OLED_init              
00003d01  __TI_ltoa                            
00003db1  __aeabi_idiv                         
00003db1  __aeabi_idivmod                      
00003e09  DL_UART_drainRXFIFO                  
00003e5d  SYSCFG_DL_UART_0_init                
00003eb1  uart_task                            
00003ff9  fputs                                
00004049  DL_DMA_initChannel                   
000040e1  SYSCFG_DL_ADC1_init                  
00004179  __aeabi_d2iz                         
00004179  __fixdfsi                            
000041c3  adc_getValue                         
0000420d  main                                 
00004259  DL_UART_init                         
000042a1  gray_init                            
00004331  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004375  SYSCFG_DL_init                       
000043b9  UART0_IRQHandler                     
000043fd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004441  DL_ADC12_setClockConfig              
00004481  __aeabi_uidiv                        
00004481  __aeabi_uidivmod                     
000044c1  __aeabi_f2d                          
000044c1  __extendsfdf2                        
00004501  atoi                                 
000045f5  Get_Anolog_Value                     
00004631  OLED_Set_Pos                         
0000466d  SYSCFG_DL_SYSCTL_init                
000046a9  __aeabi_i2f                          
000046a9  __floatsisf                          
000046e5  __gesf2                              
000046e5  __gtsf2                              
00004721  __TI_auto_init_nobinit_nopinit       
0000475d  __cmpsf2                             
0000475d  __eqsf2                              
0000475d  __lesf2                              
0000475d  __ltsf2                              
0000475d  __nesf2                              
00004799  __muldsi3                            
000047d3  Get_Normalize_For_User               
0000480d  __aeabi_f2iz                         
0000480d  __fixsfsi                            
00004845  Beep_Time_Control                    
000048e1  Q1_Tracing_Control_With_Speed        
00004915  SYSCFG_DL_FOR_CONTROL_init           
000049d9  mspm0_delay_ms                       
00004a09  oled_pow                             
00004b41  __aeabi_i2d                          
00004b41  __floatsidf                          
00004b6d  fputc                                
00004b99  mspm0_get_clock_ms                   
00004bc5  speed_control                        
00004ce1  __aeabi_ui2f                         
00004ce1  __floatunsisf                        
00004d09  _c_int00_noargs                      
00004da3  DL_I2C_setClockConfig                
00004e11  __aeabi_ui2d                         
00004e11  __floatunsidf                        
00004e35  __aeabi_lmul                         
00004e35  __muldi3                             
00004e59  memccpy                              
00004ebd  pid_set_speed_target                 
00004f19  __aeabi_llsl                         
00004f19  __ashldi3                            
000050a5  DL_Timer_setCaptCompUpdateMethod     
000050c1  DL_Timer_setClockConfig              
000053b1  DL_Timer_setCaptureCompareOutCtl     
00005471  SYSCFG_DL_DMA_WIT_init               
00005489  SysTick_Init                         
00005551  Encoder_Init                         
00005567  Timer_Init                           
0000557d  __TI_zero_init_nomemset              
00005685  Servo_init                           
00005699  State_Machine_init                   
000056ad  __aeabi_uldivmod                     
000056f9  DL_UART_setClockConfig               
0000570b  TI_memcpy_small                      
0000571d  __TI_decompress_none                 
00005751  DL_Timer_setCaptureCompareValue      
00005761  SysTick_Handler                      
00005771  delay_ms                             
00005781  wcslen                               
00005791  Get_Digtal_For_User                  
000057a1  __aeabi_memset                       
000057a1  __aeabi_memset4                      
000057a1  __aeabi_memset8                      
000057bd  TI_memset_small                      
000057cd  Scheduler_Init                       
000057d9  __aeabi_memclr                       
000057d9  __aeabi_memclr4                      
000057d9  __aeabi_memclr8                      
000057e5  DL_Common_delayCycles                
00005813  Servo_SetCenter                      
0000581d  __aeabi_errno_addr                   
00005825  __aeabi_memcpy                       
00005825  __aeabi_memcpy4                      
00005825  __aeabi_memcpy8                      
0000583d  ADC0_IRQHandler                      
0000583d  ADC1_IRQHandler                      
0000583d  AES_IRQHandler                       
0000583d  CANFD0_IRQHandler                    
0000583d  DAC0_IRQHandler                      
0000583d  DMA_IRQHandler                       
0000583d  Default_Handler                      
0000583d  GROUP0_IRQHandler                    
0000583d  HardFault_Handler                    
0000583d  I2C0_IRQHandler                      
0000583d  I2C1_IRQHandler                      
0000583d  NMI_Handler                          
0000583d  PendSV_Handler                       
0000583d  RTC_IRQHandler                       
0000583d  SPI0_IRQHandler                      
0000583d  SPI1_IRQHandler                      
0000583d  SVC_Handler                          
0000583d  TIMA0_IRQHandler                     
0000583d  TIMA1_IRQHandler                     
0000583d  TIMG12_IRQHandler                    
0000583d  TIMG6_IRQHandler                     
0000583d  TIMG7_IRQHandler                     
0000583d  TIMG8_IRQHandler                     
0000583d  UART1_IRQHandler                     
0000583d  UART3_IRQHandler                     
00005841  Reset_Handler                        
00005855  _system_pre_init                     
00005858  C$$EXIT                              
00005859  abort                                
00005860  asc2_1608                            
00005e50  asc2_0806                            
00006080  __aeabi_ctype_table_                 
00006080  __aeabi_ctype_table_C                
000062e0  __TI_Handler_Table_Base              
000062ec  __TI_Handler_Table_Limit             
000062f4  __TI_CINIT_Base                      
00006304  __TI_CINIT_Limit                     
00006304  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  sensor                               
202000b0  gPWM_MOTORBackup                     
20200150  gPWM_SERVOBackup                     
202001f0  wit_data                             
2020021c  angle_pid                            
20200244  speedA_pid                           
2020026c  speedB_pid                           
20200294  tracing_pid                          
202002bc  wit_dmaBuffer                        
202002dd  Digtal                               
202002de  Key_Down                             
202002df  Key_Old                              
202002e0  State_Machine                        
202002f4  Normal                               
20200304  grayscale_data                       
2020030c  start_time                           
20200310  tick_ms                              
20200314  tracing_val                          
20200318  uart_rx_ticks                        
2020031c  Key_Up                               
2020031d  Key_Val                              
2020031e  grayscale_count                      
2020031f  task_num                             
20200320  uart_rx_index                        
20200324  _ftable                              
20200414  uart_rx_buffer                       
202004b8  Anolog                               
202004c8  black                                
202004d8  white                                
202004e8  __aeabi_errno                        
202004ec  _lock                                
202004f0  _unlock                              
202004f4  bee_time                             
202004fc  q1_current_base_speed                
20200500  q1_min_speed                         
20200504  q1_smooth_start_time                 
20200508  q1_target_speed                      
2020050c  encoder_A_count                      
2020050e  encoder_B_count                      
20200510  encoder_count                        
20200512  q1_smooth_duration                   
20200516  circle_num                           
2020051a  encoder_count_flag                   
2020051b  q1_first_flag                        
2020051c  turn_num                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[296 symbols]
