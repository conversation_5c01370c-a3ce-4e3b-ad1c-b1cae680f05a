# 舵机角度控制使用指南

## 功能概述

新封装的舵机控制函数提供了基于角度的直观控制接口，支持±45度范围内的精确角度控制。

## 核心函数

### Servo_SetAngle()
```c
void Servo_SetAngle(float angle);
```

**功能**: 设置舵机转向角度  
**参数**: 
- `angle` - 目标角度 (范围: -45.0° ~ +45.0°)
  - `0°` = 中心位置 (小车前轮正直)
  - `正值` = 左偏 (例如: +30° = 左转30度)  
  - `负值` = 右偏 (例如: -30° = 右转30度)

**特性**:
- 自动角度限幅保护 (超出±45°自动限制)
- 线性角度映射，控制精度高
- 兼容现有PWM配置

### Servo_SetCenter()
```c
void Servo_SetCenter(void);
```

**功能**: 将舵机设置到中心位置(0度)  
**使用场景**: 系统初始化、紧急回中等

## 技术参数

### PWM配置
- **定时器**: TIMG6
- **频率**: 1MHz
- **周期**: 20ms (20000计数值)
- **脉宽范围**: 1000μs ~ 2000μs
- **中心脉宽**: 1500μs

### 角度映射
```
角度范围: -45° ~ +45°
脉宽范围: 1000μs ~ 2000μs
映射关系: 线性映射
分辨率: 约0.1°/步
```

## 使用示例

### 基本使用
```c
#include "servo.h"

int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    Servo_init();  // 舵机初始化(自动回中)
    
    while(1) {
        // 左转30度
        Servo_SetAngle(30.0f);
        delay_ms(1000);
        
        // 回中
        Servo_SetCenter();
        delay_ms(1000);
        
        // 右转30度  
        Servo_SetAngle(-30.0f);
        delay_ms(1000);
        
        // 回中
        Servo_SetCenter();
        delay_ms(1000);
    }
}
```

### 在控制系统中使用
```c
// 角度控制模式下的舵机控制
void angle_control_with_servo(void) {
    // 获取当前角度误差
    float angle_error = target_angle - wit_data.relative_yaw;
    
    // 角度误差转换为舵机角度
    float servo_angle = angle_error * 0.5f;  // 比例系数可调
    
    // 设置舵机角度
    Servo_SetAngle(servo_angle);
}

// 循迹模式下的舵机控制
void tracing_control_with_servo(void) {
    // 获取循迹偏差
    Tracing_Value_Get();
    
    // 循迹偏差转换为舵机角度
    float servo_angle = tracing_val * 10.0f;  // 比例系数可调
    
    // 设置舵机角度
    Servo_SetAngle(servo_angle);
}
```

### 按键控制示例
```c
void key_servo_control(void) {
    switch(Key_Down) {
        case 1:  // KEY1 - 左转
            Servo_SetAngle(30.0f);
            break;
            
        case 2:  // KEY2 - 右转
            Servo_SetAngle(-30.0f);
            break;
            
        case 3:  // KEY3 - 回中
            Servo_SetCenter();
            break;
            
        case 4:  // KEY4 - 最大左转
            Servo_SetAngle(45.0f);
            break;
    }
}
```

## 参数调优指南

### 角度范围调整
如需修改角度范围，可调整以下参数：
```c
#define SERVO_MAX_ANGLE       45.0f   // 最大转角(度)
#define SERVO_MIN_PULSE       1000    // 最小脉宽(微秒)
#define SERVO_MAX_PULSE       2000    // 最大脉宽(微秒)
```

### 中心位置校准
如果舵机中心位置有偏差，可调整：
```c
#define SERVO_CENTER_PULSE    1500    // 中心位置脉宽(微秒)
```

### 控制精度优化
- **增加精度**: 减小角度范围或增加PWM分辨率
- **提高响应**: 减小控制周期或优化算法
- **增强稳定性**: 添加滤波或限制角度变化率

## 安全注意事项

1. **角度限制**: 函数内置±45°限幅保护，防止舵机过转
2. **初始化**: 系统启动时自动回中，避免意外转向
3. **电源要求**: 确保舵机电源充足，避免欠压影响控制精度
4. **机械限位**: 建议在机械结构上也设置限位保护

## 故障排除

### 舵机不响应
1. 检查PWM信号是否正常输出
2. 确认舵机电源连接
3. 验证GPIO配置是否正确

### 角度偏差
1. 校准中心脉宽参数
2. 检查机械安装是否正确
3. 验证角度映射算法

### 抖动问题
1. 检查电源纹波
2. 添加软件滤波
3. 优化控制算法参数

## 兼容性说明

- **向后兼容**: 保留原始`set_angle()`函数，不影响现有代码
- **系统集成**: 可与现有PID控制系统无缝集成
- **扩展性**: 支持多舵机控制扩展

## 性能指标

- **响应时间**: < 1ms (软件延迟)
- **控制精度**: ±0.1°
- **角度范围**: ±45°
- **更新频率**: 支持高达1kHz控制频率

---

*本文档由米醋电子工作室技术团队编写*
