#include "key.h"
#include "pid.h"

uint8_t Key_Val,Key_Down,Key_Up,Key_Old;

uint8_t Key_Read()
{
	uint8_t temp = 0;
	
	if(DL_GPIO_readPins(KEY_PORT,KEY_KEY1_PIN) == 0) temp = 1;
    if(DL_GPIO_readPins(KEY_PORT,KEY_KEY2_PIN) == 0) temp = 2;
    if(DL_GPIO_readPins(KEY_PORT,KEY_KEY3_PIN) == 0) temp = 3;
    if(DL_GPIO_readPins(KEY_PORT,KEY_KEY4_PIN) == 0) temp = 4;

	return temp;
}

void Key_Proc()
{
	Key_Val = Key_Read();
	Key_Down = Key_Val & (Key_Val ^ Key_Old);
	Key_Up = ~Key_Val & (Key_Val ^ Key_Old);
	Key_Old = Key_Val;
	
	switch(Key_Down)
	{
		case 1:
            detect_trace_state_change(1);
			State_Machine.Main_State = QUESTION_1;//切换到题目1
		    break;
        case 2:
            // detect_trace_state_change(1);
            // State_Machine.Main_State = QUESTION_2;//切换到题目2
            if(++circle_num == 6)
                circle_num = 5;
            break;
        case 3:
            // detect_trace_state_change(1);
            // State_Machine.Main_State = QUESTION_3;//切换到题目3
            if(--circle_num == 0)
                circle_num = 1;
            break;
        case 4:
            detect_trace_state_change(1);
            State_Machine.Main_State = QUESTION_4;//切换到题目4
            break;
	}
}