#ifndef __APP_QUESTION_TASK_H
#define __APP_QUESTION_TASK_H
#include "HeadFiles.h"

//question state definition
#define  STOP_STATE   0
#define  QUESTION_1   1
#define  QUESTION_2   2
#define  QUESTION_3   3
#define  QUESTION_4   4

//State_Machine
struct state_machine
{
    int Main_State;         //停止，或进行第几问的状态
	int Q1_State;           //每一问的子状态
	int Q2_State;
	int Q3_State;
	int Q4_State;
};

extern struct state_machine State_Machine;
extern float target_angle;

// Q1速度平滑过渡参数 (外部可调)
extern uint16_t q1_smooth_duration;    // 平滑过渡持续时间(ms)
extern float q1_min_speed;             // 起始最低基础速度
extern float q1_target_speed;          // 目标循迹基础速度
extern float q1_current_base_speed;    // 当前基础速度

void State_Machine_init(void);
void Question_Task_1(void);
void Question_Task_2(void);
void Question_Task_3(void);
void Question_Task_4(void);

// Q1专用循迹控制函数
void Q1_Tracing_Control_With_Speed(float base_speed);

// Q1速度平滑参数设置函数
void Q1_Set_Smooth_Params(uint16_t duration, float min_speed, float target_speed);

#endif
