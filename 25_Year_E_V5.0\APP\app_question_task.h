#ifndef __APP_QUESTION_TASK_H
#define __APP_QUESTION_TASK_H
#include "HeadFiles.h"

//question state definition
#define  STOP_STATE   0
#define  QUESTION_1   1
#define  QUESTION_2   2
#define  QUESTION_3   3
#define  QUESTION_4   4

//State_Machine
struct state_machine
{
    int Main_State;         //停止，或进行第几问的状态
	int Q1_State;           //每一问的子状态
	int Q2_State;
	int Q3_State;
	int Q4_State;
};

extern struct state_machine State_Machine;
extern float target_angle;

void State_Machine_init(void);
void Question_Task_1(void);
void Question_Task_2(void);
void Question_Task_3(void);
void Question_Task_4(void);

#endif
