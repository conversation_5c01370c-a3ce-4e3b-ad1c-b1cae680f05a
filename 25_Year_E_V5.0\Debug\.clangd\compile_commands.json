[{"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP/app_angle_control.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP/app_question_task.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP/app_tracing_check.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP/app_tracing_control.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC/adc_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP/beep.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder/encoder.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale/Ganv_Grayscale.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key/key.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0/clock.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0/interrupt.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED/oled_hardware_i2c.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID/pid.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler/Scheduler.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart/usart_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT/wit.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/main.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Key\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Encoder\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/APP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/BEEP\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/ADC\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Grayscale\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Motor\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Uart\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Scheduler\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/MSPM0\" -I\"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c++/v1\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib/clang/18/include\" -isystem\"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/include/c\"", "file": "C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Hardware/Servo/servo.c"}]