#include "pid.h"
#include "wit.h"
#include "math.h"
tPid speedA_pid;                         // A相速度环
tPid speedB_pid;                         // B相速度环
tPid angle_pid; 						 // 角度PID
tPid tracing_pid;                        // 循迹PID
uint8_t angle_control_enable = 0;        // 角度控制使能
float tracing_val;
float angle_basic_speed = 20.0;
float tracing_basic_speed = 20.0;

//给结构体类型变量赋初值
void PID_init()
{
	// 角度PID初始化
    angle_pid.actual_val = 0.0; 
    angle_pid.target_val = 0.0;
    angle_pid.actual_last = 0.0;
    angle_pid.err = 0.0;
    angle_pid.err_last = 0.0;
    angle_pid.err_sum = 0.0;
    angle_pid.Kp = 0.35;                   // 比例系数
    angle_pid.Ki = 0.0;                   // 积分系数(角度控制不用积分)
    angle_pid.Kd = 0.6;                   // 微分系数
    angle_pid.output = 0.0;

    tracing_pid.actual_val = 0.0; 
    tracing_pid.target_val = 0.0;
    tracing_pid.actual_last = 0.0;
    tracing_pid.err = 0.0;
    tracing_pid.err_last = 0.0;
    tracing_pid.err_sum = 0.0;
    tracing_pid.Kp = 2.0;                   // 比例系数
    tracing_pid.Ki = 0.0;                   // 积分系数
    tracing_pid.Kd = 0;                   // 微分系数
    tracing_pid.output = 0.0;

    speedA_pid.actual_val = 0.0; 
    speedA_pid.target_val = 0.0;
    speedA_pid.actual_last = 0.0;
    speedA_pid.err = 0.0;
    speedA_pid.err_last = 0.0;
    speedA_pid.err_sum = 0.0;
    speedA_pid.Kp = 15.0;                   // 比例系数
    speedA_pid.Ki = 3.0;                   // 积分系数
    speedA_pid.Kd = 0.0;                   // 微分系数
    speedA_pid.output = 0.0;

    speedB_pid.actual_val = 0.0; 
    speedB_pid.target_val = 0.0;
    speedB_pid.actual_last = 0.0;
    speedB_pid.err = 0.0;
    speedB_pid.err_last = 0.0;
    speedB_pid.err_sum = 0.0;
    speedB_pid.Kp = 15.0;                   // 比例系数
    speedB_pid.Ki = 3.0;                   // 积分系数
    speedB_pid.Kd = 0.0;                   // 微分系数
    speedB_pid.output = 0.0;
}


float PID_speed_realize(tPid * pid,uint8_t encoder)     //速度环
{
    pid->actual_val = Encoder_Get(encoder);     
    //printf("%d\r\n", (int16_t)pid->actual_val);
    pid->err = pid->target_val - pid->actual_val;
    pid->err_sum += pid->err;
     // 积分饱和保护
    if(pid->err_sum > 1000.0) pid->err_sum = 1000.0;
    if(pid->err_sum < -1000.0) pid->err_sum = -1000.0;

     // PID计算：比例 + 积分 + 微分
     pid->output = pid->Kp * pid->err                           // 比例项
                 + pid->Ki * pid->err_sum                       // 积分项
                 + pid->Kd * (pid->err - pid->err_last);        // 微分项
    // 保存本次误差供下次微分计算使用
    pid->err_last = pid->err;

    // 输出限幅（差速控制量）
    if( pid->output > 900.0)  pid->output = 900.0;
    if( pid->output < -900.0)  pid->output = -900.0;

    return  pid->output;
}
// 速度设定函数
void pid_set_speed_target(float motorAspeed,float motorBspeed) 
{
    speedA_pid.target_val = motorAspeed;
    speedB_pid.target_val = motorBspeed;
}

//角度环
float fabs_zzk(float value)
{
    if (value < 0) 
	{
        return -value;
    } 
	else 
	{
        return value;
    }
}

float Yaw_error_zzk(float Target, float Now)
{

    static float error;
    if (Target > 0) 
    {
        if (Now <= 0) 
        {
            if (fabs(Now) < (180 - Target)) 
            {
                error = fabs_zzk(Now) + Target;  // 正向跨越
            } 
            else 
            {
                error = -(180 - Target) - (180 - fabs_zzk(Now));  // 反向跨越
            }
        } 
        else 
        {
            if (Now > 0) 
            {
                error = Target - Now;  // 同象限，直接相减
            }
        }
    } 
    else if (Target < 0)
    {
        if (Now > 0) 
        {
            if (Now > Target + 180)
            {
                error = (180 - Now) + (180 - fabs_zzk(Target));  // 正向跨越
            }
            else if (Now < Target + 180) 
            {
                error = -(fabs_zzk(Target) + Now);  // 反向跨越
            }
        } 
        else if (Now < 0) 
        {
            error = -(fabs_zzk(Target) - fabs_zzk(Now));  // 同象限
        }
    }
    
    return error;
}

// 独立的角度控制PID函数
float PID_angle_realize(tPid * pid, float current_angle) 
{
    pid->actual_val = current_angle;      // 当前角度
    float angle_error = Yaw_error_zzk(pid->target_val, pid->actual_val);
    pid->err = angle_error;               // 角度误差
    
    // 积分项累加（角度控制通常不用积分，Ki=0）
    pid->err_sum += pid->err;
    
    // 积分饱和保护
    if(pid->err_sum > 1000.0) pid->err_sum = 1000.0;
    if(pid->err_sum < -1000.0) pid->err_sum = -1000.0;
    
    // PID计算：比例 + 积分 + 微分
    pid->output = pid->Kp * pid->err                           // 比例项
                 + pid->Ki * pid->err_sum                       // 积分项
                 + pid->Kd * (pid->err - pid->err_last);        // 微分项
    
    // 保存本次误差供下次微分计算使用
    pid->err_last = pid->err;

    // 输出限幅（差速控制量）
    if( pid->output > 30.0)  pid->output = 30.0;
    if( pid->output < -30.0)  pid->output = -30.0;
    
    //printf("%.3f,%.3f,%.3f,%.3f,%.3f,%.3f\r\n", pid->output,wit_data.relative_yaw,90.0,speedA_pid.target_val,speedA_pid.actual_val,speedA_pid.output);
    
    //输出偏移
    //if( pid->output < 0)  pid->output -= 3;
    //else if( pid->output > 0)  pid->output += 3;
    //else  pid->output = 0;
    //输入死区
    //if(fabs(pid->err) < 0.5)  pid->output = 0;

    speedA_pid.target_val = -pid->output + angle_basic_speed;
    speedB_pid.target_val = pid->output + angle_basic_speed;

    return  pid->output;
}

// 角度设定函数
void pid_set_angle_target(float target) 
{
    angle_pid.target_val = target;
}


void Tracing_Value_Get(void)
{
    float value = 0;
    int sensor_count = 0;
    
    // 统计检测到的传感器数量
    for(int i = 0; i < 8; i++) 
    {
        if(grayscale_data[i]) sensor_count++;
    }
    
    if(sensor_count == 0) 
    {
        value = 0.0;
        return;  // 没有检测到线
    }

    if(grayscale_data[0]) //检测到最左端
        value -= 40.0f;
    if(grayscale_data[1]) 
        value -= 30.0f;
    if(grayscale_data[2]) 
        value -= 20.0f;
    if(grayscale_data[3])
        value -= 10.0f;
    if(grayscale_data[4])
        value += 10.0f;
    if(grayscale_data[5])
        value += 20.0f;
    if(grayscale_data[6])
        value += 30.0f; 
    if(grayscale_data[7])
        value += 40.0f;
    tracing_val = value / (float)sensor_count;
}

float PID_tracing_realize(tPid * pid, float line_actual) 
{
    pid->actual_val = line_actual;
	float line_error =pid->target_val - pid->actual_val;
    pid->err = line_error;  
    
    // 积分项累加
    pid->err_sum += pid->err;
    
    // PID计算：比例 + 积分 + 微分
    pid->output = pid->Kp * pid->err                           // 比例项
                 + pid->Ki * pid->err_sum                       // 积分项
                 + pid->Kd * (pid->err - pid->err_last);        // 微分项
    
    // 保存本次误差供下次微分计算使用
    pid->err_last = pid->err;

    // 输出限幅（差速控制量）
    if( pid->output > 30.0)  pid->output = 30.0;
    if( pid->output < -30.0)  pid->output = -30.0;

    //  printf("%.3f,%.3f,%.3f,%.3f,%.3f,%.3f\r\n", pid->output,pid->actual_val,pid->target_val,speedA_pid.target_val,speedA_pid.actual_val,speedA_pid.output);

    speedA_pid.target_val = pid->output + tracing_basic_speed;
    speedB_pid.target_val = -pid->output + tracing_basic_speed;

    return  pid->output;
}


