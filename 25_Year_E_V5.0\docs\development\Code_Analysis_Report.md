# 25_Year_E_V5.0 项目代码分析报告

## 项目概述

这是一个基于TI MSPM0G3507微控制器的智能小车控制系统，主要用于完成循迹、角度控制等自动化任务。项目采用模块化设计，包含硬件抽象层、应用层和中间件层。

## 项目架构

### 目录结构
```
25_Year_E_V5.0/
├── APP/                    # 应用层代码
│   ├── app_question_task.* # 题目任务状态机
│   ├── app_angle_control.* # 角度控制应用
│   ├── app_tracing_control.* # 循迹控制应用
│   └── app_tracing_check.* # 循迹状态检测
├── Hardware/               # 硬件驱动层
│   ├── ADC/               # ADC采集
│   ├── BEEP/              # 蜂鸣器
│   ├── Encoder/           # 编码器
│   ├── Grayscale/         # 灰度传感器
│   ├── Key/               # 按键
│   ├── MSPM0/             # 微控制器底层
│   ├── Motor/             # 电机控制
│   ├── OLED/              # OLED显示
│   ├── PID/               # PID控制算法
│   ├── Scheduler/         # 任务调度器
│   ├── Servo/             # 舵机控制
│   ├── Uart/              # 串口通信
│   └── WIT/               # 陀螺仪
├── Debug/                 # 调试输出文件
├── main.c                 # 主程序入口
└── HeadFiles.h            # 头文件集合
```

## 核心功能模块

### 1. 主程序流程 (main.c)
- **系统初始化**: SYSCFG_DL_init(), SysTick_Init()
- **模块初始化**: PID、调度器、状态机、OLED、灰度传感器、编码器、舵机
- **中断配置**: 串口中断、定时器中断
- **主循环**: 调度器运行 + 按键处理

### 2. 状态机系统 (app_question_task.c)
**状态定义**:
- STOP_STATE: 停止状态
- QUESTION_1-4: 四个不同的题目任务

**核心状态机结构**:
```c
struct state_machine {
    int Main_State;    // 主状态
    int Q1_State;      // 题目1子状态
    int Q2_State;      // 题目2子状态  
    int Q3_State;      // 题目3子状态
    int Q4_State;      // 题目4子状态
};
```

**题目任务特点**:
- **题目1**: 基础循迹控制
- **题目2**: 5状态循环 (A→B→C→返回→A)，包含角度控制和循迹切换
- **题目3**: 复杂角度控制，包含斜向移动和编码器计数
- **题目4**: 重复4次的复杂路径，结合角度和循迹控制

### 3. PID控制系统 (pid.c)
**四个独立PID控制器**:
- `angle_pid`: 角度控制PID (Kp=0.35, Ki=0.0, Kd=0.6)
- `tracing_pid`: 循迹控制PID (Kp=2.0, Ki=0.0, Kd=0)
- `speedA_pid`: A轮速度PID (Kp=15.0, Ki=3.0, Kd=0.0)
- `speedB_pid`: B轮速度PID (Kp=15.0, Ki=3.0, Kd=0.0)

**关键算法**:
- `Yaw_error_zzk()`: 处理角度跨越±180°的误差计算
- `PID_angle_realize()`: 角度控制实现
- `PID_tracing_realize()`: 循迹控制实现
- `Tracing_Value_Get()`: 灰度传感器数据处理

### 4. 硬件驱动层

#### 编码器系统 (encoder.c)
- 双编码器支持 (A轮、B轮)
- 基于查表法的高效解码
- 中断驱动的实时计数
- 支持编码器计数累加功能

#### 灰度传感器 (Ganv_Grayscale.c)
- 8路灰度传感器阵列
- 支持模拟量和数字量输出
- 白平衡和黑平衡校准
- 归一化处理算法

#### 电机控制 (motor.c)
- 双电机PWM控制
- 方向控制 (正转/反转)
- 速度范围: 0-100% PWM占空比

#### 陀螺仪系统 (wit.c)
- WIT陀螺仪数据解析
- 支持加速度、角速度、角度数据
- 相对角度计算和校准功能
- DMA + 中断的高效数据处理

### 5. 任务调度系统 (Scheduler.c)
**调度任务**:
- `uart_task`: 10ms周期
- `gray_task`: 50ms周期  
- `Oled_Task`: 100ms周期

**调度算法**: 基于时间片的非抢占式调度

### 6. 中断系统 (interrupt.c)
**主要中断**:
- `SysTick_Handler()`: 1ms系统时钟
- `FOR_CONTROL_INST_IRQHandler()`: 10ms控制中断，执行状态机任务
- `UART_WIT_INST_IRQHandler()`: 陀螺仪数据接收中断

## 控制算法分析

### 循迹控制算法
1. **传感器数据处理**: 8路灰度传感器加权平均
2. **线位置计算**: 基于传感器位置的加权算法
3. **PID控制**: 将线位置误差转换为差速控制量
4. **电机输出**: 差速控制实现转向

### 角度控制算法  
1. **角度误差处理**: 特殊的跨越±180°误差计算
2. **PID控制**: 角度误差转换为差速控制
3. **速度合成**: 基础速度 + 角度控制量

### 状态检测算法
- **循迹状态检测**: 50ms确认机制，避免误判
- **状态类型**: TRACE_STATE_WHITE_LINE, TRACE_STATE_HAS_BLACK

## 性能特点

### 优势
1. **模块化设计**: 清晰的分层架构
2. **实时性**: 10ms控制周期，1ms系统时钟
3. **鲁棒性**: 多重PID控制，状态确认机制
4. **可扩展性**: 良好的硬件抽象层

### 待优化点
1. **代码注释**: 部分关键算法缺少详细注释
2. **参数调优**: PID参数可能需要根据实际硬件调整
3. **错误处理**: 缺少传感器故障检测机制
4. **性能监控**: 缺少实时性能指标监控

## 编译和部署

### 开发环境
- **IDE**: Code Composer Studio (CCS)
- **目标芯片**: MSPM0G3507
- **调试接口**: SWD (SWCLK: PA20, SWDIO: PA19)

### 编译输出
- **可执行文件**: 25_Year_E_V5.0.out
- **映射文件**: 25_Year_E_V5.0.map
- **链接信息**: 25_Year_E_V5.0_linkInfo.xml

## 总结

这是一个设计良好的嵌入式控制系统，采用了现代化的软件架构设计原则。系统具有良好的实时性和可维护性，适合用于智能小车竞赛或教学项目。主要的技术亮点包括多层PID控制、状态机管理、模块化硬件抽象等。
