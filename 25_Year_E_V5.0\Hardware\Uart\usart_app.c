#include "usart_app.h"

uint32_t uart_rx_ticks;
uint8_t uart_rx_index;
uint8_t uart_rx_buffer[128] = {0};

int fputc(int ch, FILE *stream)
{
    while( DL_UART_isBusy(UART_0_INST) == true );
    DL_UART_Main_transmitData(UART_0_INST, ch);
    return ch;
}

//重定向fputs函数
int fputs(const char* restrict s, FILE* restrict stream) {

    uint16_t char_len=0;
    while(*s!=0)
    {
        while( DL_UART_isBusy(UART_0_INST) == true );
        DL_UART_Main_transmitData(UART_0_INST, *s++);
        char_len++;
    }
    return char_len;
}

int puts(const char* _ptr)
{
    return 0;
}

//串口发送单个字符
void uart0_send_char(char ch)
{
    //当串口0忙的时候等待，不忙的时候再发送传进来的字符
    while( DL_UART_isBusy(UART_0_INST) == true );
    //发送单个字符
    DL_UART_Main_transmitData(UART_0_INST, ch);
}

//串口发送字符串
void uart0_send_string(char* str)
{
    //当前字符串地址不在结尾 并且 字符串首地址不为空
    while(*str!=0&&str!=0)
    {
        //发送字符串首地址中的字符，并且在发送完成之后首地址自增
        uart0_send_char(*str++);
    }
}

void UART_0_INST_IRQHandler(void)
{
   //如果产生了串口中断
   switch( DL_UART_getPendingInterrupt(UART_0_INST) )
   {
       case DL_UART_IIDX_RX://如果是接收中断
			uart_rx_ticks = tick_ms;
           uart_rx_buffer[uart_rx_index++] = DL_UART_Main_receiveData(UART_0_INST);
           break;
       default://其他的串口中断

           break;
   }
}

void uart_task(void) //超时解析
{
	if (uart_rx_index == 0)
		return;
	if(tick_ms - uart_rx_ticks >= 30)
	{
		printf("uart data: %s\n", uart_rx_buffer);
		uart_rx_index = 0;
		memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
	}
}
