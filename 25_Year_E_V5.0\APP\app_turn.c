#include "app_turn.h"

// 舵机转弯状态枚举
typedef enum {
    TURN_STATE_IDLE = 0,           // 空闲状态，正常循迹
    TURN_STATE_WHITE_DETECTED,     // 检测到全白，准备转弯
    TURN_STATE_TURNING,            // 正在转弯
    TURN_STATE_SEARCHING,          // 转弯后寻找黑线
    TURN_STATE_COMPLETED           // 转弯完成，恢复循迹
} TurnState_t;

// 舵机转弯参数结构体
typedef struct {
    float turn_angle;              // 转弯角度 (度)
    uint32_t turn_duration;        // 转弯持续时间 (毫秒)
    float turn_speed;              // 转弯时的速度
    uint8_t turn_direction;        // 转弯方向 (1=左转, 2=右转, 0=自动判断)
} ServoTurnParams_t;

// 全局变量
static TurnState_t turn_state = TURN_STATE_IDLE;
static uint32_t turn_start_time = 0;
static float last_tracing_val = 0.0f;
static ServoTurnParams_t turn_params = {
    .turn_angle = 45.0f,           // 默认转弯角度45度
    .turn_duration = 1000,         // 默认转弯时间1秒
    .turn_speed = 150.0f,          // 默认转弯速度
    .turn_direction = 0            // 默认自动判断方向
};

/**
 * @brief 设置舵机转弯参数
 * @param angle 转弯角度 (10.0° ~ 50.0°)
 * @param duration 转弯持续时间 (毫秒, 500~3000)
 * @param speed 转弯时的速度 (50~300)
 * @param direction 转弯方向 (0=自动, 1=左转, 2=右转)
 */
void Servo_Turn_SetParams(float angle, uint32_t duration, float speed, uint8_t direction)
{
    // 参数限幅保护
    if(angle < 10.0f) angle = 10.0f;
    if(angle > 50.0f) angle = 50.0f;
    if(duration < 500) duration = 500;
    if(duration > 3000) duration = 3000;
    if(speed < 50.0f) speed = 50.0f;
    if(speed > 300.0f) speed = 300.0f;
    if(direction > 2) direction = 0;

    turn_params.turn_angle = angle;
    turn_params.turn_duration = duration;
    turn_params.turn_speed = speed;
    turn_params.turn_direction = direction;
}

/**
 * @brief 获取当前转弯状态
 * @return 转弯状态枚举值
 */
TurnState_t Servo_Turn_GetState(void)
{
    return turn_state;
}

/**
 * @brief 重置转弯状态机
 */
void Servo_Turn_Reset(void)
{
    turn_state = TURN_STATE_IDLE;
    turn_start_time = 0;
    last_tracing_val = 0.0f;
    Servo_SetCenter();  // 舵机回中
}

/**
 * @brief 舵机全白转直角主函数
 * @note 在主控制循环中调用，替代或配合Tracing_Control()使用
 * @return 1=正在转弯过程中, 0=正常循迹状态
 */
uint8_t Servo_Turn_WhiteDetection(void)
{
    TraceState_t trace_state = detect_trace_state_change(0);

    switch(turn_state)
    {
        case TURN_STATE_IDLE:
        {
            // 正常循迹状态，监测全白
            if(trace_state == TRACE_STATE_WHITE_LINE)
            {
                // 检测到全白，记录转弯前的循迹方向
                Tracing_Value_Get();
                last_tracing_val = tracing_val;

                // 切换到全白检测状态
                turn_state = TURN_STATE_WHITE_DETECTED;
                turn_start_time = tick_ms;

                // 响铃提示
                Beep_ms(100);

                printf("检测到全白，准备转弯，记录方向: %.2f\r\n", last_tracing_val);
            }
            else
            {
                // 正常循迹
                Tracing_Control();
            }
            return 0;  // 正常循迹状态
        }

        case TURN_STATE_WHITE_DETECTED:
        {
            // 确认全白状态，准备转弯
            if(tick_ms - turn_start_time >= 100)  // 100ms确认时间
            {
                // 确定转弯方向
                float turn_angle = turn_params.turn_angle;

                if(turn_params.turn_direction == 0)  // 自动判断方向
                {
                    // 根据转弯前的循迹偏差判断方向
                    if(last_tracing_val > 5.0f)  // 线在右侧，应该右转
                    {
                        turn_angle = -turn_params.turn_angle;
                        printf("自动判断：右转 %.1f度\r\n", turn_params.turn_angle);
                    }
                    else if(last_tracing_val < -5.0f)  // 线在左侧，应该左转
                    {
                        turn_angle = turn_params.turn_angle;
                        printf("自动判断：左转 %.1f度\r\n", turn_params.turn_angle);
                    }
                    else  // 偏差较小，默认左转
                    {
                        turn_angle = turn_params.turn_angle;
                        printf("偏差较小，默认左转 %.1f度\r\n", turn_params.turn_angle);
                    }
                }
                else if(turn_params.turn_direction == 1)  // 强制左转
                {
                    turn_angle = turn_params.turn_angle;
                    printf("强制左转 %.1f度\r\n", turn_params.turn_angle);
                }
                else  // 强制右转
                {
                    turn_angle = -turn_params.turn_angle;
                    printf("强制右转 %.1f度\r\n", turn_params.turn_angle);
                }

                // 设置舵机角度并开始转弯
                Servo_SetAngle(turn_angle);

                // 设置转弯速度
                speed_basic = (uint16_t)turn_params.turn_speed;

                // 切换到转弯状态
                turn_state = TURN_STATE_TURNING;
                turn_start_time = tick_ms;

                // 响铃提示开始转弯
                Beep_ms(200);
            }

            // 转弯准备期间保持当前速度
            pwm_set(turn_params.turn_speed, turn_params.turn_speed);
            return 1;  // 转弯过程中
        }

        case TURN_STATE_TURNING:
        {
            // 执行转弯动作
            if(tick_ms - turn_start_time >= turn_params.turn_duration)
            {
                // 转弯时间到，开始寻找黑线
                Servo_SetCenter();  // 舵机回中
                turn_state = TURN_STATE_SEARCHING;
                turn_start_time = tick_ms;

                printf("转弯完成，开始寻找黑线\r\n");
            }

            // 转弯期间保持设定速度
            pwm_set(turn_params.turn_speed, turn_params.turn_speed);
            return 1;  // 转弯过程中
        }

        case TURN_STATE_SEARCHING:
        {
            // 寻找黑线
            if(trace_state == TRACE_STATE_HAS_BLACK)
            {
                // 找到黑线，转弯完成
                turn_state = TURN_STATE_COMPLETED;
                turn_start_time = tick_ms;

                // 响铃提示找到黑线
                Beep_ms(300);

                printf("找到黑线，转弯完成\r\n");
            }
            else if(tick_ms - turn_start_time >= 2000)  // 2秒超时
            {
                // 寻线超时，强制恢复循迹
                turn_state = TURN_STATE_IDLE;
                printf("寻线超时，恢复正常循迹\r\n");
                return 0;
            }

            // 寻线期间保持低速前进
            pwm_set(100, 100);
            return 1;  // 转弯过程中
        }

        case TURN_STATE_COMPLETED:
        {
            // 转弯完成，短暂稳定后恢复正常循迹
            if(tick_ms - turn_start_time >= 200)  // 200ms稳定时间
            {
                turn_state = TURN_STATE_IDLE;
                printf("恢复正常循迹模式\r\n");
            }

            // 开始恢复循迹控制
            Tracing_Control();
            return 0;  // 即将恢复正常状态
        }

        default:
            turn_state = TURN_STATE_IDLE;
            return 0;
    }
}
