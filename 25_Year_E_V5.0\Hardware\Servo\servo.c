#include "servo.h"

uint16_t init_angle = 1600;
float now_speedA = 0.3;
float now_speedB = 0.3;
volatile uint8_t ProtectFlag = 1;

// 舵机角度控制参数配置
#define SERVO_CENTER_PULSE    1600    // 中心位置脉宽(微秒) - 对应0度
#define SERVO_MAX_ANGLE       70.0f   // 最大转角(度) - 修改为70度
#define SERVO_MIN_PULSE       800     // 最小脉宽(微秒) - 对应-70度
#define SERVO_MAX_PULSE       2400    // 最大脉宽(微秒) - 对应+70度
#define SERVO_PWM_PERIOD      20000   // PWM周期(微秒) - 20ms
#define SERVO_TIMER_FREQ      1000000 // 定时器频率(Hz) - 1MHz


/**
 * @brief 舵机角度控制函数
 * @param angle 目标角度 (-45.0° ~ +45.0°)
 *               0°   = 中心位置(小车前轮正直)
 *               正值 = 左偏 (例如: +30° = 左转30度)
 *               负值 = 右偏 (例如: -30° = 右转30度)
 * @note 角度范围限制在±45度以保护舵机
 */
void Servo_SetAngle(float angle)
{
    // 角度限幅保护
    if(angle > SERVO_MAX_ANGLE) {
        angle = SERVO_MAX_ANGLE;
    } else if(angle < -SERVO_MAX_ANGLE) {
        angle = -SERVO_MAX_ANGLE;
    }

    // 角度转换为脉宽(微秒)
    // 线性映射: angle(-45~+45) -> pulse_width(1000~2000)
    float pulse_width = SERVO_CENTER_PULSE + (angle / SERVO_MAX_ANGLE) * (SERVO_MAX_PULSE - SERVO_CENTER_PULSE);

    // 脉宽转换为定时器比较值
    // PWM_SERVO配置: 周期20000, 频率1MHz
    // 比较值 = 周期 - 脉宽 (因为使用的是反向PWM模式)
    uint16_t compare_value = SERVO_PWM_PERIOD - (uint16_t)pulse_width;

    // 设置PWM比较值
    DL_TimerG_setCaptureCompareValue(PWM_SERVO_INST, compare_value, GPIO_PWM_SERVO_C0_IDX);
}

/**
 * @brief 舵机回中函数
 * @note 将舵机设置到中心位置(0度)
 */
void Servo_SetCenter(void)
{
    Servo_SetAngle(0.0f);
}

void Servo_init()
{
	DL_TimerG_startCounter(PWM_SERVO_INST);
    // 初始化时设置舵机到中心位置
    Servo_SetCenter();
}

