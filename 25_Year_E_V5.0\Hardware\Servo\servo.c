#include "servo.h"

uint16_t init_angle = 1600;
float now_speedA = 0.3;
float now_speedB = 0.3;
volatile uint8_t ProtectFlag = 1;

void set_angle(uint16_t angle)   //  通道0专用占空比设置函数
{
	uint16_t compareValue;
	float duty;
	
	duty = (float)angle / 200.0f;
	
	compareValue = 20000 - 200 * duty; 
	
	DL_TimerG_setCaptureCompareValue(PWM_SERVO_INST,(uint16_t)compareValue,GPIO_PWM_SERVO_C0_IDX);
}

void Servo_init()
{	
	DL_TimerG_startCounter(PWM_SERVO_INST);
    set_angle(init_angle + 250);
}

// void Servo_Proc_stiff() //此函数为坡度循迹测试专用
// {
// 	Digtal_Transfer();
// 		if(grayscale_data[0] == 1 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //最左侧扫线
// 		{
// 			set_angle(init_angle + 850);
// 			now_speedA = 0.5;
// 			now_speedB = 0.15;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 1 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中左侧扫线
// 		{
// 			set_angle(init_angle + 250);
// 			now_speedA = 0.4;
// 			now_speedB = 0.25;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 1 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小左侧扫线
// 		{
// 			set_angle(init_angle + 150);
// 			now_speedA = 0.4;
// 			now_speedB = 0.25;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 1 && grayscale_data[4] == 1 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中侧扫线 - 直行 
// 		{
// 			set_angle(init_angle);
// 			now_speedA = 0.3;
// 			now_speedB = 0.3;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 1 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小小小右扫线 
// 		{
// 			set_angle(init_angle - 150);
// 			now_speedA = 0.35;
// 			now_speedB = 0.45;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小小右侧扫线
// 		{
// 			set_angle(init_angle - 350);
// 			now_speedA = 0.3;
// 			now_speedB = 0.45;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 0) //小右侧扫线
// 		{
// 			set_angle(init_angle - 500);
// 			now_speedA = 0.3;
// 			now_speedB = 0.5;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 0) //小右侧扫线
// 		{
// 			set_angle(init_angle - 550);
// 			now_speedA = 0.3;
// 			now_speedB = 0.5;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 1) //中右侧扫线
// 		{
// 			set_angle(init_angle - 700);
// 			now_speedA = 0.25;
// 			now_speedB = 0.55;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 1) //大右侧扫线
// 		{
// 			set_angle(init_angle - 830);
// 			now_speedA = 0.2;
// 			now_speedB = 0.6;
// 		}
// 		//制动
// 		if(grayscale_count >= 4) //4路以上黑线 制动
// 		{
// 			set_angle(init_angle);
// 			//制动代码在这写-->
// 			pid_set_target_val(0.0,0.0);
// 			if(ProtectFlag)  //防止多次进入误触发
// 			{
// 				BeepFlag = 1; //定时器计时开启标志位
// 				ProtectFlag = 0;
// 			}
// 		}
// 		else 
// 		{
// 			pid_set_target_val(now_speedA,now_speedB);
// 		}
// }
// void Servo_Proc_Zero() //此函数为平地循迹测试专用
// {
// 	Digtal_Transfer();
// 		if(grayscale_data[0] == 1 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //最左侧扫线
// 		{
// 			set_angle(init_angle + 830);
// 			now_speedA = 0.7;
// 			now_speedB = 0.2;
// 		}
// 		if(grayscale_data[0] == 1 && grayscale_data[1] == 1 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中大左侧扫线
// 		{
// 			set_angle(init_angle + 700);
// 			now_speedA = 0.6;
// 			now_speedB = 0.2;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 1 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中左侧扫线
// 		{
// 			set_angle(init_angle + 550);
// 			now_speedA = 0.6;
// 			now_speedB = 0.3;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 1 && grayscale_data[2] == 1 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中大左侧扫线
// 		{
// 			set_angle(init_angle + 500);
// 			now_speedA = 0.55;
// 			now_speedB = 0.3;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 1 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小左侧扫线
// 		{
// 			set_angle(init_angle + 350);
// 			now_speedA = 0.5;
// 			now_speedB = 0.3;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 1 &&
// 			 grayscale_data[3] == 1 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小小左侧扫线
// 		{
// 			set_angle(init_angle + 150);
// 			now_speedA = 0.45;
// 			now_speedB = 0.3;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 1 && grayscale_data[4] == 1 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //中侧扫线 - 直行 
// 		{
// 			set_angle(init_angle);
// 			now_speedA = 0.4;
// 			now_speedB = 0.4;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 1 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小小小右扫线 
// 		{
// 			set_angle(init_angle - 150);
// 			now_speedA = 0.35;
// 			now_speedB = 0.45;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 0) //小小右侧扫线
// 		{
// 			set_angle(init_angle - 350);
// 			now_speedA = 0.3;
// 			now_speedB = 0.45;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 1 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 0) //小右侧扫线
// 		{
// 			set_angle(init_angle - 500);
// 			now_speedA = 0.3;
// 			now_speedB = 0.5;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 0) //小右侧扫线
// 		{
// 			set_angle(init_angle - 550);
// 			now_speedA = 0.3;
// 			now_speedB = 0.5;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 1 && grayscale_data[7] == 1) //中右侧扫线
// 		{
// 			set_angle(init_angle - 700);
// 			now_speedA = 0.25;
// 			now_speedB = 0.55;
// 		}
// 		if(grayscale_data[0] == 0 && grayscale_data[1] == 0 && grayscale_data[2] == 0 &&
// 			 grayscale_data[3] == 0 && grayscale_data[4] == 0 && grayscale_data[5] == 0 &&
// 			 grayscale_data[6] == 0 && grayscale_data[7] == 1) //大右侧扫线
// 		{
// 			set_angle(init_angle - 830);
// 			now_speedA = 0.2;
// 			now_speedB = 0.6;
// 		}
// 		//制动
// 		if(grayscale_count >= 4) //4路以上黑线 制动
// 		{
// 			set_angle(init_angle);
// 			//制动代码在这写-->
// 			pid_set_target_val(0.0,0.0);
// 			if(ProtectFlag)  //防止多次进入误触发
// 			{
// 				BeepFlag = 1;
// 				ProtectFlag = 0;
// 			}
// 		}
// 		else 
// 		{
// 			pid_set_target_val(now_speedA,now_speedB);
// 		}
// }

