#include "app_question_task.h"

#define  Q1_STATE_1   1
#define  Q1_STATE_2   2
#define  Q1_STATE_3   3  // 新增：速度平滑过渡状态
#define  Q1_STATE_4   4
#define  Q1_STATE_5   5
#define  Q3_STATE_1   1
#define  Q3_STATE_2   2
#define  Q3_STATE_3   3
#define  Q3_STATE_4   4
#define  Q3_STATE_5   5

uint8_t turn_num = 0;
uint8_t circle_num = 1;

struct state_machine State_Machine;

float target_angle;
uint8_t q1_first_flag = 0;
uint8_t q2_first_flag = 0;
uint8_t q3_first_flag = 0;
uint8_t q4_first_flag = 0;
uint32_t time_last;

// Q1速度平滑过渡相关变量
uint32_t q1_smooth_start_time = 0;     // 平滑过渡开始时间
uint16_t q1_smooth_duration = 3000;    // 平滑过渡持续时间(ms) - 测试用3秒
uint16_t q1_min_speed = 80;            // 起始最低速度
uint16_t q1_target_speed = 200;        // 目标循迹速度

void State_Machine_init()
{
    State_Machine.Main_State = STOP_STATE;
    State_Machine.Q1_State = STOP_STATE;
    State_Machine.Q2_State = STOP_STATE;
    State_Machine.Q3_State = STOP_STATE;
    State_Machine.Q4_State = STOP_STATE;
}

void Question_Task_1()
{
    if(q1_first_flag == 0)
    {
        q1_first_flag = 1;
        State_Machine.Q1_State = Q1_STATE_1;
        speed_basic = q1_target_speed;  // 设置正常循迹速度
    }


    if(State_Machine.Q1_State == Q1_STATE_1)
    {
        // 正常循迹状态
        speed_basic = q1_target_speed;  // 确保使用目标速度
        Tracing_Control();

        if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
        {
            turn_num++;
            if(turn_num == circle_num * 4 + 1)
            {
                State_Machine.Q1_State = STOP_STATE;
                State_Machine.Main_State = STOP_STATE;
            }
            else
            {
                State_Machine.Q1_State = Q1_STATE_2;
            }
        }
    }
    else if(State_Machine.Q1_State == Q1_STATE_2)
    {
        // 转弯状态
        pid_set_speed_target(0.0, 5.0);  // 原地左转

        if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
        {
            // 检测到黑线，进入速度平滑过渡状态
            State_Machine.Q1_State = Q1_STATE_3;
            q1_smooth_start_time = tick_ms;  // 记录平滑开始时间
            speed_basic = q1_min_speed;      // 设置起始低速
        }
    }
    else if(State_Machine.Q1_State == Q1_STATE_3)
    {
        // 速度平滑过渡状态
        uint32_t elapsed_time = tick_ms - q1_smooth_start_time;

        if(elapsed_time >= q1_smooth_duration)
        {
            // 平滑过渡完成，回到正常循迹
            speed_basic = q1_target_speed;
            State_Machine.Q1_State = Q1_STATE_1;
        }
        else
        {
            // 计算当前应该的速度 (线性插值)
            float progress = (float)elapsed_time / (float)q1_smooth_duration;  // 进度 0.0~1.0
            uint16_t current_speed = q1_min_speed + (uint16_t)((q1_target_speed - q1_min_speed) * progress);
            speed_basic = current_speed;
        }

        // 在平滑过渡期间继续循迹控制
        Tracing_Control();
    }
}

/**
 * @brief 设置Q1速度平滑过渡参数
 * @param duration 平滑过渡持续时间 (毫秒, 建议300~1500)
 * @param min_speed 起始最低速度 (建议50~120)
 * @param target_speed 目标循迹速度 (建议150~300)
 */
void Q1_Set_Smooth_Params(uint16_t duration, uint16_t min_speed, uint16_t target_speed)
{
    // 参数合理性检查
    if(duration < 200) duration = 200;
    if(duration > 2000) duration = 2000;
    if(min_speed < 30) min_speed = 30;
    if(min_speed > 150) min_speed = 150;
    if(target_speed < 100) target_speed = 100;
    if(target_speed > 400) target_speed = 400;
    if(min_speed >= target_speed) min_speed = target_speed - 50;

    q1_smooth_duration = duration;
    q1_min_speed = min_speed;
    q1_target_speed = target_speed;
}

void Question_Task_2()
{
    // if(q2_first_flag == 0)
    // {
    //     //记录发车角度
    //     first_angle = wit_data.relative_yaw;

    //     //设置目标值
    //     float target_angle = wit_data.relative_yaw;
    //     pid_set_angle_target(target_angle);


    //     //响铃
    //     Beep_ms(300);

    //     q2_first_flag = 1;

    //     State_Machine.Q2_State = Q2_STATE_1;
    //     angle_basic_speed = 40.0;
    //     tracing_basic_speed = 20.0;
    // }

    // if(State_Machine.Q2_State == Q2_STATE_1)//第二问状态1，A -> B
    // {
    //     speed_basic = 200;
    //     angele_control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         //响铃
    //         Beep_ms(300);

    //         //切换下一个状态
    //         State_Machine.Q2_State = Q2_STATE_2;
    //     }
    // }
    // else if(State_Machine.Q2_State == Q2_STATE_2)//第二问状态2，B -> C
    // {
    //     speed_basic = 300;
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         //响铃
    //         Beep_ms(300);

    //         //切换下一个状态
    //         State_Machine.Q2_State = Q2_STATE_3;
    //     }
    // }
    // else if(State_Machine.Q2_State == Q2_STATE_3)
    // {
    //     speed_basic = 400;
    //     angele_control();
    //     pid_set_angle_target((first_angle + 180.0) > 180.0 ? (-180.0 + first_angle) :(first_angle + 180.0));
    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         //响铃
    //         Beep_ms(300);

    //         //切换下一个状态
    //         State_Machine.Q2_State = Q2_STATE_4;
    //     }
    // } 
    // else if(State_Machine.Q2_State == Q2_STATE_4) 
    // {
    //     speed_basic = 300;
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         //响铃
    //         Beep_ms(300);

    //         //切换下一个状态
    //         State_Machine.Q2_State = Q2_STATE_5;
    //     }
    // }
    // else 
    // {
    //     State_Machine.Q2_State = STOP_STATE;
	// 	State_Machine.Main_State = STOP_STATE;
	// 	q2_first_flag = 0;
    // }
}

void Question_Task_3()
{
    // if(q3_first_flag == 0)
    // {
    //     q3_first_flag = 1;
    //     Beep_ms(300);
    //     //记录发车角度
    //     first_angle = wit_data.relative_yaw;
    //     //设置目标值
    //     target_angle = first_angle - 50.0;
    //     pid_set_angle_target(target_angle);
    //     State_Machine.Q3_State = Q3_STATE_1;
    //     encoder_count = 0;
    //     angle_basic_speed = 20.0;
    //     tracing_basic_speed = 20.0;
    // }

    // if(State_Machine.Q3_State == Q3_STATE_1)
    // {
    //     angele_control();
    //     if(wit_data.relative_yaw >= target_angle - 2.0 && wit_data.relative_yaw <= target_angle + 2.0)
    //         encoder_count_flag = 1;
    //     if(encoder_count > 6500)
    //     {
    //         target_angle = first_angle;
    //         pid_set_angle_target(target_angle);
    //         encoder_count_flag = 0;
    //         encoder_count = 0;
    //     }

    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_2;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_2)
    // {
    //     speed_basic = 300;
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         Beep_ms(300);
    //         target_angle = ((first_angle + 180.0) > 180.0 ? (-180.0 + first_angle) :(first_angle + 180.0)) + 50.0;
    //         // 添加角度归一化
    //         while (target_angle > 180.0f) target_angle -= 360.0f;
    //         while (target_angle < -180.0f) target_angle += 360.0f;
    //         pid_set_angle_target(target_angle);
    //         State_Machine.Q3_State = Q3_STATE_3;
    //         encoder_count = 0;
    //         encoder_count_flag = 0;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_3)
    // {
    //     angele_control();
    //     if((wit_data.relative_yaw >= target_angle - 2.0) && (wit_data.relative_yaw <= target_angle + 2.0))
    //         encoder_count_flag = 1;
    //     if(encoder_count > 5800)
    //     {
    //         speed_basic = 200;
    //         target_angle = ((first_angle + 180.0) > 180.0) ? (-180.0 + first_angle) :(first_angle + 180.0);
    //         pid_set_angle_target(target_angle);
    //         encoder_count_flag = 0;
    //         encoder_count = 0;
    //     }
    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_4;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_4)
    // {
    //     speed_basic = 300;
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_5;
    //     }
    // }
    // else 
    // {
    //     State_Machine.Main_State = STOP_STATE;
    //     State_Machine.Q3_State = STOP_STATE;
    //     pwm_set(0, 0);
    //     q3_first_flag = 0;
    // }
}

void Question_Task_4()
{
    // static uint16_t q4_count = 0;
    // if(q4_first_flag == 0)
    // {
    //     q4_first_flag = 1;
    //     Beep_ms(300);
    //     //记录发车角度
    //     first_angle = wit_data.relative_yaw;
    //     //设置目标值
    //     target_angle = first_angle - 47.5;
    //     pid_set_angle_target(target_angle);
    //     State_Machine.Q3_State = Q3_STATE_1;
    //     encoder_count = 0;
    //     angle_basic_speed = 40.0;
    //     tracing_basic_speed = 20.0;
    // }

    // if(State_Machine.Q3_State == Q3_STATE_1)//第一次斜
    // {
    //     angele_control();
    //     if(wit_data.relative_yaw >= target_angle - 2.0 && wit_data.relative_yaw <= target_angle + 2.0)
    //         encoder_count_flag = 1;
    //     if(encoder_count > 5200 && q4_count == 0)
    //     {
    //         target_angle = first_angle;
    //         pid_set_angle_target(target_angle);
    //         encoder_count_flag = 0;
    //         encoder_count = 0;
    //         angle_basic_speed = 20.0;
    //     }
        
    //     if(encoder_count > 5100 && q4_count != 0)
    //     {
    //         target_angle = first_angle;
    //         pid_set_angle_target(target_angle);
    //         encoder_count_flag = 0;
    //         encoder_count = 0;
    //         angle_basic_speed = 20.0;
    //     }

    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_2;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_2)
    // {
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         Beep_ms(300);
    //         target_angle = ((first_angle + 180.0) > 180.0 ? (-180.0 + first_angle) :(first_angle + 180.0)) + 45.0;
    //         // 添加角度归一化
    //         while (target_angle > 180.0f) target_angle -= 360.0f;
    //         while (target_angle < -180.0f) target_angle += 360.0f;
    //         pid_set_angle_target(target_angle);
    //         State_Machine.Q3_State = Q3_STATE_3;
    //         encoder_count = 0;
    //         encoder_count_flag = 0;
    //         angle_basic_speed = 40.0;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_3)
    // {
    //     angele_control();
    //     if((wit_data.relative_yaw >= target_angle - 2.0) && (wit_data.relative_yaw <= target_angle + 2.0))
    //         encoder_count_flag = 1;
    //     if(encoder_count > 4900)
    //     {
    //         speed_basic = 200;
    //         target_angle = ((first_angle + 180.0) > 180.0) ? (-180.0 + first_angle) :(first_angle + 180.0);
    //         pid_set_angle_target(target_angle);
    //         encoder_count_flag = 0;
    //         encoder_count = 0;
    //         angle_basic_speed = 20.0;
    //     }
    //     if(detect_trace_state_change(0) == TRACE_STATE_HAS_BLACK)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_4;
    //     }
    // }
    // else if(State_Machine.Q3_State == Q3_STATE_4)
    // {
    //     Tracing_Control();
    //     if(detect_trace_state_change(0) == TRACE_STATE_WHITE_LINE)
    //     {
    //         Beep_ms(300);
    //         State_Machine.Q3_State = Q3_STATE_5;
    //     }
    // }
    // else 
    // {
    //     if(++q4_count == 4)
    //     {
    //         State_Machine.Main_State = STOP_STATE;
    //         State_Machine.Q3_State = STOP_STATE;
    //         State_Machine.Q4_State = STOP_STATE;
    //         pwm_set(0, 0);
    //         q4_first_flag = 0;
    //     }
    //     else
    //     {
    //         target_angle = first_angle - 47.0;
    //         pid_set_angle_target(target_angle);
    //         State_Machine.Q3_State = Q3_STATE_1;
    //         encoder_count = 0;
    //         encoder_count_flag = 0;
    //         angle_basic_speed = 40.0;
    //     }
    // }
}

