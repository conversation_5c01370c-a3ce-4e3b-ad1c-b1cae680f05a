################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

CG_TOOL_ROOT := D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS

GEN_OPTS__FLAG := @"./device.opt" 
GEN_CMDS__FLAG := -Wl,-l"./device_linker.cmd" 

ORDERED_OBJS += \
"./ti_msp_dl_config.o" \
"./startup_mspm0g350x_ticlang.o" \
"./main.o" \
"./APP/app_angle_control.o" \
"./APP/app_question_task.o" \
"./APP/app_tracing_check.o" \
"./APP/app_tracing_control.o" \
"./Hardware/ADC/adc_app.o" \
"./Hardware/BEEP/beep.o" \
"./Hardware/Encoder/encoder.o" \
"./Hardware/Grayscale/Ganv_Grayscale.o" \
"./Hardware/Key/key.o" \
"./Hardware/MSPM0/clock.o" \
"./Hardware/MSPM0/interrupt.o" \
"./Hardware/Motor/motor.o" \
"./Hardware/OLED/oled_hardware_i2c.o" \
"./Hardware/PID/pid.o" \
"./Hardware/Scheduler/Scheduler.o" \
"./Hardware/Servo/servo.o" \
"./Hardware/Uart/usart_app.o" \
"./Hardware/WIT/wit.o" \
$(GEN_CMDS__FLAG) \
-Wl,-ldevice.cmd.genlibs \
-Wl,-llibc.a \

-include ../makefile.init

RM := DEL /F
RMDIR := RMDIR /S/Q

# All of the sources participating in the build are defined here
-include sources.mk
-include subdir_vars.mk
-include APP/subdir_vars.mk
-include Hardware/ADC/subdir_vars.mk
-include Hardware/BEEP/subdir_vars.mk
-include Hardware/Encoder/subdir_vars.mk
-include Hardware/Grayscale/subdir_vars.mk
-include Hardware/Key/subdir_vars.mk
-include Hardware/MSPM0/subdir_vars.mk
-include Hardware/Motor/subdir_vars.mk
-include Hardware/OLED/subdir_vars.mk
-include Hardware/PID/subdir_vars.mk
-include Hardware/Scheduler/subdir_vars.mk
-include Hardware/Servo/subdir_vars.mk
-include Hardware/Uart/subdir_vars.mk
-include Hardware/WIT/subdir_vars.mk
-include subdir_rules.mk
-include APP/subdir_rules.mk
-include Hardware/ADC/subdir_rules.mk
-include Hardware/BEEP/subdir_rules.mk
-include Hardware/Encoder/subdir_rules.mk
-include Hardware/Grayscale/subdir_rules.mk
-include Hardware/Key/subdir_rules.mk
-include Hardware/MSPM0/subdir_rules.mk
-include Hardware/Motor/subdir_rules.mk
-include Hardware/OLED/subdir_rules.mk
-include Hardware/PID/subdir_rules.mk
-include Hardware/Scheduler/subdir_rules.mk
-include Hardware/Servo/subdir_rules.mk
-include Hardware/Uart/subdir_rules.mk
-include Hardware/WIT/subdir_rules.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(C55_DEPS)),)
-include $(C55_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(S67_DEPS)),)
-include $(S67_DEPS)
endif
ifneq ($(strip $(S62_DEPS)),)
-include $(S62_DEPS)
endif
ifneq ($(strip $(S_DEPS)),)
-include $(S_DEPS)
endif
ifneq ($(strip $(OPT_DEPS)),)
-include $(OPT_DEPS)
endif
ifneq ($(strip $(C??_DEPS)),)
-include $(C??_DEPS)
endif
ifneq ($(strip $(ASM_UPPER_DEPS)),)
-include $(ASM_UPPER_DEPS)
endif
ifneq ($(strip $(S??_DEPS)),)
-include $(S??_DEPS)
endif
ifneq ($(strip $(C64_DEPS)),)
-include $(C64_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(S64_DEPS)),)
-include $(S64_DEPS)
endif
ifneq ($(strip $(INO_DEPS)),)
-include $(INO_DEPS)
endif
ifneq ($(strip $(CLA_DEPS)),)
-include $(CLA_DEPS)
endif
ifneq ($(strip $(S55_DEPS)),)
-include $(S55_DEPS)
endif
ifneq ($(strip $(SV7A_DEPS)),)
-include $(SV7A_DEPS)
endif
ifneq ($(strip $(C62_DEPS)),)
-include $(C62_DEPS)
endif
ifneq ($(strip $(C67_DEPS)),)
-include $(C67_DEPS)
endif
ifneq ($(strip $(PDE_DEPS)),)
-include $(PDE_DEPS)
endif
ifneq ($(strip $(K_DEPS)),)
-include $(K_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(C43_DEPS)),)
-include $(C43_DEPS)
endif
ifneq ($(strip $(S43_DEPS)),)
-include $(S43_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
ifneq ($(strip $(SA_DEPS)),)
-include $(SA_DEPS)
endif
endif

-include ../makefile.defs

# Add inputs and outputs from these tool invocations to the build variables 
EXE_OUTPUTS += \
25_Year_E_V5.0.out 

EXE_OUTPUTS__QUOTED += \
"25_Year_E_V5.0.out" 


# All Target
all: $(OBJS) $(GEN_CMDS)
	@$(MAKE) --no-print-directory -Onone "25_Year_E_V5.0.out"

# Tool invocations
25_Year_E_V5.0.out: $(OBJS) $(GEN_CMDS)
	@echo 'Building target: "$@"'
	@echo 'Invoking: Arm Linker'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -gdwarf-3 -Wl,-m"25_Year_E_V5.0.map" -Wl,-i"D:/ti/ccstheia151/mspm0_sdk_2_05_00_05/source" -Wl,-i"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0" -Wl,-i"C:/Users/<USER>/workspace_ccstheia/25_Year_E_V5.0/Debug/syscfg" -Wl,-i"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib" -Wl,--diag_wrap=off -Wl,--display_error_number -Wl,--warn_sections -Wl,--xml_link_info="25_Year_E_V5.0_linkInfo.xml" -Wl,--rom_model -o "25_Year_E_V5.0.out" $(ORDERED_OBJS)
	@echo 'Finished building target: "$@"'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(GEN_MISC_FILES__QUOTED)$(GEN_FILES__QUOTED)$(EXE_OUTPUTS__QUOTED)
	-$(RM) "ti_msp_dl_config.o" "startup_mspm0g350x_ticlang.o" "main.o" "APP\app_angle_control.o" "APP\app_question_task.o" "APP\app_tracing_check.o" "APP\app_tracing_control.o" "Hardware\ADC\adc_app.o" "Hardware\BEEP\beep.o" "Hardware\Encoder\encoder.o" "Hardware\Grayscale\Ganv_Grayscale.o" "Hardware\Key\key.o" "Hardware\MSPM0\clock.o" "Hardware\MSPM0\interrupt.o" "Hardware\Motor\motor.o" "Hardware\OLED\oled_hardware_i2c.o" "Hardware\PID\pid.o" "Hardware\Scheduler\Scheduler.o" "Hardware\Servo\servo.o" "Hardware\Uart\usart_app.o" "Hardware\WIT\wit.o" 
	-$(RM) "ti_msp_dl_config.d" "startup_mspm0g350x_ticlang.d" "main.d" "APP\app_angle_control.d" "APP\app_question_task.d" "APP\app_tracing_check.d" "APP\app_tracing_control.d" "Hardware\ADC\adc_app.d" "Hardware\BEEP\beep.d" "Hardware\Encoder\encoder.d" "Hardware\Grayscale\Ganv_Grayscale.d" "Hardware\Key\key.d" "Hardware\MSPM0\clock.d" "Hardware\MSPM0\interrupt.d" "Hardware\Motor\motor.d" "Hardware\OLED\oled_hardware_i2c.d" "Hardware\PID\pid.d" "Hardware\Scheduler\Scheduler.d" "Hardware\Servo\servo.d" "Hardware\Uart\usart_app.d" "Hardware\WIT\wit.d" 
	-@echo 'Finished clean'
	-@echo ' '

.PHONY: all clean dependents
.SECONDARY:

-include ../makefile.targets

