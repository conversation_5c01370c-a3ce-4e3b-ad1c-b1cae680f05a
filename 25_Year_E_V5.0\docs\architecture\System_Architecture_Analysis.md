# 25_Year_E_V5.0 系统架构分析

## 架构概览

本项目采用分层架构设计，基于TI MSPM0G3507微控制器构建的智能小车控制系统。系统遵循嵌入式系统设计的最佳实践，实现了硬件抽象、中间件和应用层的清晰分离。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                     应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  app_question_task  │  app_angle_control  │  app_tracing_*   │
│  (状态机管理)        │  (角度控制)          │  (循迹控制)       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   中间件层 (Middleware Layer)                 │
├─────────────────────────────────────────────────────────────┤
│    PID控制器    │    任务调度器    │    状态检测    │    中断管理   │
│   (pid.c)      │   (Scheduler.c)  │  (tracing_check) │ (interrupt.c)│
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  硬件抽象层 (Hardware Abstraction Layer)       │
├─────────────────────────────────────────────────────────────┤
│ Motor │ Encoder │ Grayscale │ WIT │ OLED │ Key │ Servo │ UART │
│ 电机   │ 编码器   │ 灰度传感器 │陀螺仪│显示器│按键 │舵机   │串口  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    硬件层 (Hardware Layer)                    │
├─────────────────────────────────────────────────────────────┤
│              TI MSPM0G3507 微控制器                          │
│    GPIO │ PWM │ ADC │ UART │ Timer │ DMA │ Interrupt         │
└─────────────────────────────────────────────────────────────┘
```

## 核心架构组件

### 1. 应用层 (Application Layer)

#### 状态机管理 (app_question_task.c)
- **职责**: 管理四个不同题目的执行逻辑
- **设计模式**: 分层状态机 (Main_State + Sub_States)
- **状态转换**: 基于传感器输入和时间条件
- **特点**: 每个题目独立的状态管理，支持复杂的多阶段任务

#### 控制应用模块
- **角度控制** (app_angle_control.c): 基于陀螺仪的方向控制
- **循迹控制** (app_tracing_control.c): 基于灰度传感器的路径跟踪
- **状态检测** (app_tracing_check.c): 传感器状态变化检测

### 2. 中间件层 (Middleware Layer)

#### PID控制系统 (pid.c)
**架构特点**:
- **多控制器架构**: 4个独立PID控制器
- **分层控制**: 角度控制 → 速度控制 → PWM输出
- **控制流程**:
  ```
  传感器输入 → PID计算 → 目标速度设定 → 速度PID → PWM输出
  ```

**控制器配置**:
```c
// 角度控制 - 外环控制器
angle_pid: {Kp=0.35, Ki=0.0, Kd=0.6}

// 循迹控制 - 外环控制器  
tracing_pid: {Kp=2.0, Ki=0.0, Kd=0}

// 速度控制 - 内环控制器
speedA_pid, speedB_pid: {Kp=15.0, Ki=3.0, Kd=0.0}
```

#### 任务调度器 (Scheduler.c)
**调度策略**: 基于时间片的协作式调度
**任务配置**:
- 高频任务: uart_task (10ms)
- 中频任务: gray_task (50ms)
- 低频任务: Oled_Task (100ms)

#### 中断管理 (interrupt.c)
**中断层次**:
- **系统级**: SysTick (1ms) - 系统时钟
- **控制级**: Timer (10ms) - 主控制循环
- **通信级**: UART - 陀螺仪数据接收

### 3. 硬件抽象层 (HAL)

#### 传感器接口
- **编码器**: 双通道正交编码器，中断驱动
- **灰度传感器**: 8路ADC采集，支持数字化处理
- **陀螺仪**: UART + DMA，实时姿态数据

#### 执行器接口
- **电机**: PWM + 方向控制，差速驱动
- **舵机**: PWM控制，角度定位
- **蜂鸣器**: 状态指示和调试

## 数据流架构

### 感知-决策-执行循环

```
传感器数据采集 → 数据处理 → 状态判断 → 控制决策 → 执行输出
     ↓              ↓          ↓          ↓          ↓
  编码器/灰度    → 滤波/校准 → 状态机   → PID控制  → PWM输出
  陀螺仪/按键    → 解析/去噪 → 模式切换 → 速度分配 → 电机驱动
```

### 控制数据流

1. **循迹模式**:
   ```
   灰度传感器 → 线位置计算 → 循迹PID → 差速控制 → 电机PWM
   ```

2. **角度控制模式**:
   ```
   陀螺仪 → 角度误差 → 角度PID → 差速控制 → 电机PWM
   ```

3. **速度闭环**:
   ```
   编码器 → 速度反馈 → 速度PID → PWM调节 → 电机输出
   ```

## 技术选型分析

### 微控制器选择: TI MSPM0G3507
**优势**:
- ARM Cortex-M0+ 内核，32MHz主频
- 丰富的外设接口 (PWM, ADC, UART, Timer)
- 低功耗设计，适合移动应用
- TI生态系统支持完善

### 控制算法选择: PID控制
**优势**:
- 成熟稳定的控制算法
- 参数调节相对简单
- 适合线性和弱非线性系统
- 实时性好，计算开销小

### 软件架构选择: 分层架构
**优势**:
- 模块化程度高，便于维护
- 硬件抽象层提供良好的可移植性
- 应用层专注业务逻辑
- 中间件层提供通用服务

## 性能分析

### 实时性能
- **控制周期**: 10ms (100Hz控制频率)
- **系统时钟**: 1ms (1kHz时间基准)
- **中断响应**: 微秒级响应时间
- **任务调度**: 毫秒级调度精度

### 资源利用
- **内存使用**: 主要用于PID参数、状态变量、传感器数据缓存
- **CPU利用率**: 控制算法计算量适中，留有充足余量
- **外设利用**: 充分利用硬件PWM、ADC、DMA等外设

### 可扩展性
- **传感器扩展**: HAL层支持新传感器接入
- **算法扩展**: 中间件层可添加新的控制算法
- **功能扩展**: 应用层可增加新的任务模式

## 架构优势

1. **模块化设计**: 清晰的分层结构，职责分离明确
2. **实时性保证**: 中断驱动 + 定时调度，满足控制系统实时要求
3. **可维护性**: 良好的代码组织，便于调试和修改
4. **可扩展性**: 硬件抽象层支持功能扩展
5. **鲁棒性**: 多重PID控制，状态确认机制

## 架构改进建议

1. **错误处理**: 增加传感器故障检测和恢复机制
2. **参数管理**: 实现PID参数的在线调节功能
3. **日志系统**: 添加运行状态和性能监控
4. **通信接口**: 增加与上位机的通信功能
5. **配置管理**: 实现参数的非易失性存储

## 总结

该系统架构设计合理，采用了成熟的分层架构模式，具有良好的模块化、实时性和可维护性。PID控制系统设计完善，能够满足智能小车的控制需求。整体架构为后续功能扩展和性能优化提供了良好的基础。
