/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     80000000



/* Defines for PWM_MOTOR */
#define PWM_MOTOR_INST                                                     TIMG7
#define PWM_MOTOR_INST_IRQHandler                               TIMG7_IRQHandler
#define PWM_MOTOR_INST_INT_IRQN                                 (TIMG7_INT_IRQn)
#define PWM_MOTOR_INST_CLK_FREQ                                         10000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTOR_C0_PORT                                             GPIOB
#define GPIO_PWM_MOTOR_C0_PIN                                     DL_GPIO_PIN_15
#define GPIO_PWM_MOTOR_C0_IOMUX                                  (IOMUX_PINCM32)
#define GPIO_PWM_MOTOR_C0_IOMUX_FUNC                 IOMUX_PINCM32_PF_TIMG7_CCP0
#define GPIO_PWM_MOTOR_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTOR_C1_PORT                                             GPIOB
#define GPIO_PWM_MOTOR_C1_PIN                                     DL_GPIO_PIN_16
#define GPIO_PWM_MOTOR_C1_IOMUX                                  (IOMUX_PINCM33)
#define GPIO_PWM_MOTOR_C1_IOMUX_FUNC                 IOMUX_PINCM33_PF_TIMG7_CCP1
#define GPIO_PWM_MOTOR_C1_IDX                                DL_TIMER_CC_1_INDEX

/* Defines for PWM_SERVO */
#define PWM_SERVO_INST                                                     TIMG6
#define PWM_SERVO_INST_IRQHandler                               TIMG6_IRQHandler
#define PWM_SERVO_INST_INT_IRQN                                 (TIMG6_INT_IRQn)
#define PWM_SERVO_INST_CLK_FREQ                                          1000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_SERVO_C0_PORT                                             GPIOA
#define GPIO_PWM_SERVO_C0_PIN                                     DL_GPIO_PIN_29
#define GPIO_PWM_SERVO_C0_IOMUX                                   (IOMUX_PINCM4)
#define GPIO_PWM_SERVO_C0_IOMUX_FUNC                  IOMUX_PINCM4_PF_TIMG6_CCP0
#define GPIO_PWM_SERVO_C0_IDX                                DL_TIMER_CC_0_INDEX



/* Defines for FOR_CONTROL */
#define FOR_CONTROL_INST                                                 (TIMG0)
#define FOR_CONTROL_INST_IRQHandler                             TIMG0_IRQHandler
#define FOR_CONTROL_INST_INT_IRQN                               (TIMG0_INT_IRQn)
#define FOR_CONTROL_INST_LOAD_VALUE                                      (4999U)




/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C0
#define I2C_OLED_INST_IRQHandler                                 I2C0_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C0_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOA
#define GPIO_I2C_OLED_SDA_PIN                                     DL_GPIO_PIN_28
#define GPIO_I2C_OLED_IOMUX_SDA                                   (IOMUX_PINCM3)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                    IOMUX_PINCM3_PF_I2C0_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOA
#define GPIO_I2C_OLED_SCL_PIN                                     DL_GPIO_PIN_31
#define GPIO_I2C_OLED_IOMUX_SCL                                   (IOMUX_PINCM6)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                    IOMUX_PINCM6_PF_I2C0_SCL


/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           40000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_40_MHZ_115200_BAUD                                      (21)
#define UART_0_FBRD_40_MHZ_115200_BAUD                                      (45)
/* Defines for UART_WIT */
#define UART_WIT_INST                                                      UART2
#define UART_WIT_INST_FREQUENCY                                         40000000
#define UART_WIT_INST_IRQHandler                                UART2_IRQHandler
#define UART_WIT_INST_INT_IRQN                                    UART2_INT_IRQn
#define GPIO_UART_WIT_RX_PORT                                              GPIOB
#define GPIO_UART_WIT_RX_PIN                                      DL_GPIO_PIN_18
#define GPIO_UART_WIT_IOMUX_RX                                   (IOMUX_PINCM44)
#define GPIO_UART_WIT_IOMUX_RX_FUNC                    IOMUX_PINCM44_PF_UART2_RX
#define UART_WIT_BAUD_RATE                                              (115200)
#define UART_WIT_IBRD_40_MHZ_115200_BAUD                                    (21)
#define UART_WIT_FBRD_40_MHZ_115200_BAUD                                    (45)





/* Defines for ADC1 */
#define ADC1_INST                                                           ADC0
#define ADC1_INST_IRQHandler                                     ADC0_IRQHandler
#define ADC1_INST_INT_IRQN                                       (ADC0_INT_IRQn)
#define ADC1_ADCMEM_0                                         DL_ADC12_MEM_IDX_0
#define ADC1_ADCMEM_0_REF                        DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC1_ADCMEM_0_REF_VOLTAGE_V                                          3.3
#define GPIO_ADC1_C5_PORT                                                  GPIOB
#define GPIO_ADC1_C5_PIN                                          DL_GPIO_PIN_24



/* Defines for DMA_WIT */
#define DMA_WIT_CHAN_ID                                                      (0)
#define UART_WIT_INST_DMA_TRIGGER                            (DMA_UART2_RX_TRIG)


/* Port definition for Pin Group BEEP */
#define BEEP_PORT                                                        (GPIOB)

/* Defines for PIN_5: GPIOB.5 with pinCMx 18 on package pin 53 */
#define BEEP_PIN_5_PIN                                           (DL_GPIO_PIN_5)
#define BEEP_PIN_5_IOMUX                                         (IOMUX_PINCM18)
/* Port definition for Pin Group GPIO_MOTOR */
#define GPIO_MOTOR_PORT                                                  (GPIOA)

/* Defines for AIN1: GPIOA.24 with pinCMx 54 on package pin 25 */
#define GPIO_MOTOR_AIN1_PIN                                     (DL_GPIO_PIN_24)
#define GPIO_MOTOR_AIN1_IOMUX                                    (IOMUX_PINCM54)
/* Defines for AIN2: GPIOA.25 with pinCMx 55 on package pin 26 */
#define GPIO_MOTOR_AIN2_PIN                                     (DL_GPIO_PIN_25)
#define GPIO_MOTOR_AIN2_IOMUX                                    (IOMUX_PINCM55)
/* Defines for BIN1: GPIOA.12 with pinCMx 34 on package pin 5 */
#define GPIO_MOTOR_BIN1_PIN                                     (DL_GPIO_PIN_12)
#define GPIO_MOTOR_BIN1_IOMUX                                    (IOMUX_PINCM34)
/* Defines for BIN2: GPIOA.13 with pinCMx 35 on package pin 6 */
#define GPIO_MOTOR_BIN2_PIN                                     (DL_GPIO_PIN_13)
#define GPIO_MOTOR_BIN2_IOMUX                                    (IOMUX_PINCM35)
/* Port definition for Pin Group ENCODER */
#define ENCODER_PORT                                                     (GPIOA)

/* Defines for E1A: GPIOA.7 with pinCMx 14 on package pin 49 */
// pins affected by this interrupt request:["E1A","E1B","E2A","E2B"]
#define ENCODER_INT_IRQN                                        (GPIOA_INT_IRQn)
#define ENCODER_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define ENCODER_E1A_IIDX                                     (DL_GPIO_IIDX_DIO7)
#define ENCODER_E1A_PIN                                          (DL_GPIO_PIN_7)
#define ENCODER_E1A_IOMUX                                        (IOMUX_PINCM14)
/* Defines for E1B: GPIOA.14 with pinCMx 36 on package pin 7 */
#define ENCODER_E1B_IIDX                                    (DL_GPIO_IIDX_DIO14)
#define ENCODER_E1B_PIN                                         (DL_GPIO_PIN_14)
#define ENCODER_E1B_IOMUX                                        (IOMUX_PINCM36)
/* Defines for E2A: GPIOA.15 with pinCMx 37 on package pin 8 */
#define ENCODER_E2A_IIDX                                    (DL_GPIO_IIDX_DIO15)
#define ENCODER_E2A_PIN                                         (DL_GPIO_PIN_15)
#define ENCODER_E2A_IOMUX                                        (IOMUX_PINCM37)
/* Defines for E2B: GPIOA.17 with pinCMx 39 on package pin 10 */
#define ENCODER_E2B_IIDX                                    (DL_GPIO_IIDX_DIO17)
#define ENCODER_E2B_PIN                                         (DL_GPIO_PIN_17)
#define ENCODER_E2B_IOMUX                                        (IOMUX_PINCM39)
/* Port definition for Pin Group Gray_Address */
#define Gray_Address_PORT                                                (GPIOB)

/* Defines for PIN_0: GPIOB.0 with pinCMx 12 on package pin 47 */
#define Gray_Address_PIN_0_PIN                                   (DL_GPIO_PIN_0)
#define Gray_Address_PIN_0_IOMUX                                 (IOMUX_PINCM12)
/* Defines for PIN_1: GPIOB.1 with pinCMx 13 on package pin 48 */
#define Gray_Address_PIN_1_PIN                                   (DL_GPIO_PIN_1)
#define Gray_Address_PIN_1_IOMUX                                 (IOMUX_PINCM13)
/* Defines for PIN_2: GPIOB.6 with pinCMx 23 on package pin 58 */
#define Gray_Address_PIN_2_PIN                                   (DL_GPIO_PIN_6)
#define Gray_Address_PIN_2_IOMUX                                 (IOMUX_PINCM23)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for KEY1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define KEY_KEY1_PIN                                             (DL_GPIO_PIN_9)
#define KEY_KEY1_IOMUX                                           (IOMUX_PINCM26)
/* Defines for KEY2: GPIOB.10 with pinCMx 27 on package pin 62 */
#define KEY_KEY2_PIN                                            (DL_GPIO_PIN_10)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM27)
/* Defines for KEY3: GPIOB.12 with pinCMx 29 on package pin 64 */
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_12)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM29)
/* Defines for KEY4: GPIOB.13 with pinCMx 30 on package pin 1 */
#define KEY_KEY4_PIN                                            (DL_GPIO_PIN_13)
#define KEY_KEY4_IOMUX                                           (IOMUX_PINCM30)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_MOTOR_init(void);
void SYSCFG_DL_PWM_SERVO_init(void);
void SYSCFG_DL_FOR_CONTROL_init(void);
void SYSCFG_DL_I2C_OLED_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_WIT_init(void);
void SYSCFG_DL_ADC1_init(void);
void SYSCFG_DL_DMA_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
