#ifndef __HEADRFILES_H_
#define __HEADRFILES_H_

#include "ti_msp_dl_config.h"

#include "clock.h"
#include "interrupt.h"

#include <string.h>
#include <stdio.h>

#include "oled_hardware_i2c.h"
#include "Scheduler.h"
#include "usart_app.h"
#include "motor.h"
#include "pid.h"
#include "wit.h"
#include "Ganv_Grayscale.h"
#include "adc_app.h"
#include "beep.h"
#include "key.h"
#include "encoder.h"
#include "servo.h"


#include "app_question_task.h"
#include "app_angle_control.h"
#include "app_tracing_control.h"
#include "app_tracing_check.h"

#endif
