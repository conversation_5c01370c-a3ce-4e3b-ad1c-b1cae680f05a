/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const I2C    = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1   = I2C.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const PWM2   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();
const UART2  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

ADC121.$name                      = "ADC1";
ADC121.sampClkDiv                 = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.adcMem0chansel             = "DL_ADC12_INPUT_CHAN_5";
ADC121.powerDownMode              = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                = "125us";
ADC121.peripheral.$assign         = "ADC0";
ADC121.peripheral.adcPin5.$assign = "PB24";
ADC121.adcPin5Config.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "GPIO_MOTOR";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "AIN1";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].pin.$assign  = "PA24";
GPIO1.associatedPins[1].$name        = "AIN2";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].pin.$assign  = "PA25";
GPIO1.associatedPins[2].$name        = "BIN1";
GPIO1.associatedPins[2].assignedPort = "PORTA";
GPIO1.associatedPins[2].assignedPin  = "12";
GPIO1.associatedPins[2].pin.$assign  = "PA12";
GPIO1.associatedPins[3].$name        = "BIN2";
GPIO1.associatedPins[3].assignedPort = "PORTA";
GPIO1.associatedPins[3].assignedPin  = "13";
GPIO1.associatedPins[3].pin.$assign  = "PA13";

GPIO2.$name                         = "ENCODER";
GPIO2.port                          = "PORTA";
GPIO2.associatedPins.create(4);
GPIO2.associatedPins[0].$name       = "E1A";
GPIO2.associatedPins[0].direction   = "INPUT";
GPIO2.associatedPins[0].assignedPin = "7";
GPIO2.associatedPins[0].interruptEn = true;
GPIO2.associatedPins[0].polarity    = "RISE_FALL";
GPIO2.associatedPins[0].pin.$assign = "PA7";
GPIO2.associatedPins[1].$name       = "E1B";
GPIO2.associatedPins[1].direction   = "INPUT";
GPIO2.associatedPins[1].assignedPin = "14";
GPIO2.associatedPins[1].interruptEn = true;
GPIO2.associatedPins[1].polarity    = "RISE_FALL";
GPIO2.associatedPins[1].pin.$assign = "PA14";
GPIO2.associatedPins[2].$name       = "E2A";
GPIO2.associatedPins[2].assignedPin = "15";
GPIO2.associatedPins[2].direction   = "INPUT";
GPIO2.associatedPins[2].polarity    = "RISE_FALL";
GPIO2.associatedPins[2].interruptEn = true;
GPIO2.associatedPins[2].pin.$assign = "PA15";
GPIO2.associatedPins[3].$name       = "E2B";
GPIO2.associatedPins[3].direction   = "INPUT";
GPIO2.associatedPins[3].assignedPin = "17";
GPIO2.associatedPins[3].interruptEn = true;
GPIO2.associatedPins[3].polarity    = "RISE_FALL";
GPIO2.associatedPins[3].pin.$assign = "PA17";

GPIO3.$name                          = "Gray_Address";
GPIO3.associatedPins.create(3);
GPIO3.associatedPins[0].$name        = "PIN_0";
GPIO3.associatedPins[0].assignedPort = "PORTB";
GPIO3.associatedPins[0].assignedPin  = "0";
GPIO3.associatedPins[0].pin.$assign  = "PB0";
GPIO3.associatedPins[1].$name        = "PIN_1";
GPIO3.associatedPins[1].assignedPort = "PORTB";
GPIO3.associatedPins[1].assignedPin  = "1";
GPIO3.associatedPins[1].pin.$assign  = "PB1";
GPIO3.associatedPins[2].$name        = "PIN_2";
GPIO3.associatedPins[2].assignedPort = "PORTB";
GPIO3.associatedPins[2].assignedPin  = "6";
GPIO3.associatedPins[2].pin.$assign  = "PB6";

GPIO4.$name                          = "BEEP";
GPIO4.associatedPins[0].$name        = "PIN_5";
GPIO4.associatedPins[0].assignedPort = "PORTB";
GPIO4.associatedPins[0].assignedPin  = "5";
GPIO4.associatedPins[0].initialValue = "SET";
GPIO4.associatedPins[0].pin.$assign  = "PB5";

GPIO5.$name                              = "KEY";
GPIO5.associatedPins.create(4);
GPIO5.associatedPins[0].assignedPort     = "PORTB";
GPIO5.associatedPins[0].$name            = "KEY1";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].assignedPin      = "9";
GPIO5.associatedPins[0].pin.$assign      = "PB9";
GPIO5.associatedPins[1].$name            = "KEY2";
GPIO5.associatedPins[1].assignedPort     = "PORTB";
GPIO5.associatedPins[1].assignedPin      = "10";
GPIO5.associatedPins[1].direction        = "INPUT";
GPIO5.associatedPins[1].internalResistor = "PULL_UP";
GPIO5.associatedPins[1].pin.$assign      = "PB10";
GPIO5.associatedPins[2].$name            = "KEY3";
GPIO5.associatedPins[2].assignedPort     = "PORTB";
GPIO5.associatedPins[2].assignedPin      = "12";
GPIO5.associatedPins[2].direction        = "INPUT";
GPIO5.associatedPins[2].internalResistor = "PULL_UP";
GPIO5.associatedPins[2].pin.$assign      = "PB12";
GPIO5.associatedPins[3].$name            = "KEY4";
GPIO5.associatedPins[3].direction        = "INPUT";
GPIO5.associatedPins[3].assignedPort     = "PORTB";
GPIO5.associatedPins[3].assignedPin      = "13";
GPIO5.associatedPins[3].internalResistor = "PULL_UP";
GPIO5.associatedPins[3].pin.$assign      = "PB13";

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.peripheral.$assign                = "I2C0";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM1.$name                              = "PWM_MOTOR";
PWM1.clockPrescale                      = 8;
PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign                 = "TIMG7";
PWM1.peripheral.ccp0Pin.$assign         = "PB15";
PWM1.peripheral.ccp1Pin.$assign         = "PB16";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

PWM2.$name                              = "PWM_SERVO";
PWM2.clockPrescale                      = 80;
PWM2.timerCount                         = 20000;
PWM2.timerStartTimer                    = true;
PWM2.ccIndex                            = [0];
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_0.ccValue              = 18400;
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM2.peripheral.$assign                 = "TIMG6";
PWM2.peripheral.ccp0Pin.$assign         = "PA29";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

TIMER1.$name              = "FOR_CONTROL";
TIMER1.timerClkPrescale   = 80;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "10 ms";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.enabledInterrupts        = ["RX"];
UART1.interruptPriority        = "1";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

UART2.$name                            = "UART_WIT";
UART2.targetBaudRate                   = 115200;
UART2.direction                        = "RX";
UART2.enableFIFO                       = true;
UART2.rxTimeoutValue                   = 1;
UART2.enabledInterrupts                = ["RX_TIMEOUT_ERROR"];
UART2.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";
UART2.peripheral.$assign               = "UART2";
UART2.peripheral.rxPin.$assign         = "PB18";
UART2.DMA_CHANNEL_RX.$name             = "DMA_WIT";
UART2.DMA_CHANNEL_RX.addressMode       = "f2b";
UART2.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART2.DMA_CHANNEL_RX.dstLength         = "BYTE";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution                = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution       = "PA20";
Board.peripheral.swdioPin.$suggestSolution       = "PA19";
UART2.DMA_CHANNEL_RX.peripheral.$suggestSolution = "DMA_CH0";
